#!/usr/bin/env python3
"""
Complete end-to-end integration test for the soil system stack.

This script tests seamless communication between:
- Backend API (FastAPI on port 8000)
- Database Layer (PostgreSQL with extensions)
- Soil-AI Component (BGE embeddings, vector search, Mem0)

Verifies: API → Database → AI → Vector Search → Memory
"""

import asyncio
import sys
import os
import logging
import uuid
import json
import httpx
import time
from datetime import datetime
from typing import Dict, Any, List

# Add soil-ai path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'soil-ai/soil_ai'))

from embeddings.bge_embedder import BGEEmbedder
from embeddings.vector_search import VectorSearchService
from memory.mem0_adapter import Mem0Adapter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BACKEND_URL = "http://localhost:8000"
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_api_health_and_connectivity():
    """Test API health and basic connectivity."""
    logger.info("🌐 Testing API health and connectivity...")
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            health_response = await client.get(f"{BACKEND_URL}/health")
            assert health_response.status_code == 200
            health_data = health_response.json()
            assert health_data["status"] == "healthy"
            
            # Test API documentation
            docs_response = await client.get(f"{BACKEND_URL}/docs")
            assert docs_response.status_code == 200
            assert "text/html" in docs_response.headers.get("content-type", "")
            
            logger.info(f"✅ API Health: {health_data}")
            logger.info("✅ API Documentation accessible")
            
            return {
                "success": True,
                "health_status": health_data,
                "docs_accessible": True
            }
            
    except Exception as e:
        logger.error(f"❌ API connectivity test failed: {e}")
        return {"success": False, "error": str(e)}


async def test_database_integration():
    """Test database integration with all extensions."""
    logger.info("🗄️ Testing database integration...")
    
    try:
        import asyncpg
        
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test all required extensions
        extensions = await conn.fetch("""
            SELECT extname, extversion 
            FROM pg_extension 
            WHERE extname IN ('vector', 'timescaledb', 'age', 'postgis')
            ORDER BY extname
        """)
        
        extension_dict = {ext['extname']: ext['extversion'] for ext in extensions}
        
        # Verify required versions
        required_extensions = {
            'vector': '0.8.0',
            'timescaledb': '2.21.0',
            'age': '1.5.0',
            'postgis': '3.5.3'
        }
        
        all_present = all(ext in extension_dict for ext in required_extensions)
        
        # Test vector operations
        vector_test = await conn.fetchval("SELECT '[1,2,3]'::vector(3) <-> '[4,5,6]'::vector(3)")
        
        # Test concurrent operations
        tasks = []
        for i in range(5):
            task = conn.fetchval("SELECT $1::text", f"concurrent_test_{i}")
            tasks.append(task)
        
        concurrent_results = await asyncio.gather(*tasks)
        
        await conn.close()
        
        logger.info(f"✅ Extensions: {extension_dict}")
        logger.info(f"✅ Vector operations working: {vector_test}")
        logger.info(f"✅ Concurrent operations: {len(concurrent_results)} completed")
        
        return {
            "success": True,
            "extensions": extension_dict,
            "all_extensions_present": all_present,
            "vector_operations": True,
            "concurrent_operations": len(concurrent_results)
        }
        
    except Exception as e:
        logger.error(f"❌ Database integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_complete_soil_workflow():
    """Test complete soil data workflow: API → Database → AI → Vector Search → Memory."""
    logger.info("🌱 Testing complete soil workflow...")
    
    try:
        # Initialize components
        embedder = BGEEmbedder()
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        mem0_adapter = Mem0Adapter(DATABASE_URL, embedder)
        await mem0_adapter.initialize()
        
        # Test user
        test_user_id = "2ce2f130-5c65-4cc7-b35b-e4fa4a8a7263"
        
        # Simulate complete soil analysis workflow
        soil_analysis_data = {
            "field_id": "field_A1_2025",
            "analysis": "Soil analysis for Field A1: pH 6.8, Nitrogen 45ppm, Phosphorus 28ppm, Potassium 185ppm. Organic matter 3.2%. Excellent conditions for corn cultivation.",
            "recommendations": "Maintain current nutrient levels. Consider slight potassium boost for optimal yield.",
            "metadata": {
                "field": "A1",
                "crop": "corn",
                "season": "spring_2025",
                "ph": 6.8,
                "nitrogen": 45,
                "phosphorus": 28,
                "potassium": 185,
                "organic_matter": 3.2
            }
        }
        
        # Step 1: Store soil analysis in vector search
        analysis_id = str(uuid.uuid4())
        start_time = time.time()
        
        await vector_service.add_content_embedding(
            analysis_id,
            "soil_analysis",
            soil_analysis_data["analysis"],
            soil_analysis_data["metadata"]
        )
        
        vector_store_time = time.time() - start_time
        
        # Step 2: Store user preference in memory
        start_time = time.time()
        
        preference_memory_id = await mem0_adapter.add_memory(
            test_user_id,
            f"User prefers detailed soil analysis for {soil_analysis_data['metadata']['crop']} cultivation in field {soil_analysis_data['metadata']['field']}",
            {"type": "preference", "field": soil_analysis_data["metadata"]["field"]}
        )
        
        memory_store_time = time.time() - start_time
        
        # Step 3: Store recommendation as insight
        start_time = time.time()
        
        insight_memory_id = await mem0_adapter.add_memory(
            test_user_id,
            soil_analysis_data["recommendations"],
            {"type": "insight", "field": soil_analysis_data["metadata"]["field"], "priority": "medium"}
        )
        
        insight_store_time = time.time() - start_time
        
        # Step 4: Test semantic search for similar analyses
        start_time = time.time()
        
        similar_analyses = await vector_service.search_similar_content(
            "corn cultivation soil pH nitrogen phosphorus",
            limit=5
        )
        
        search_time = time.time() - start_time
        
        # Step 5: Test memory retrieval for user context
        start_time = time.time()
        
        user_memories = await mem0_adapter.search_memories(
            test_user_id,
            "soil analysis corn field recommendations",
            limit=5
        )
        
        memory_search_time = time.time() - start_time
        
        # Step 6: Test user context isolation
        all_user_memories = await mem0_adapter.get_all_memories(test_user_id)
        
        # Performance verification
        performance_metrics = {
            "vector_store_time": vector_store_time,
            "memory_store_time": memory_store_time,
            "insight_store_time": insight_store_time,
            "search_time": search_time,
            "memory_search_time": memory_search_time
        }
        
        # Verify all operations completed successfully
        workflow_success = all([
            analysis_id is not None,
            preference_memory_id is not None,
            insight_memory_id is not None,
            len(similar_analyses) >= 0,  # May be 0 if no similar content
            len(user_memories) > 0,
            len(all_user_memories) > 0
        ])
        
        # Cleanup
        await vector_service.close()
        await mem0_adapter.close()
        
        logger.info(f"✅ Soil analysis stored in vector search: {vector_store_time:.3f}s")
        logger.info(f"✅ User preference stored in memory: {memory_store_time:.3f}s")
        logger.info(f"✅ Insight stored in memory: {insight_store_time:.3f}s")
        logger.info(f"✅ Semantic search completed: {search_time:.3f}s, {len(similar_analyses)} results")
        logger.info(f"✅ Memory search completed: {memory_search_time:.3f}s, {len(user_memories)} results")
        logger.info(f"✅ User has {len(all_user_memories)} total memories")
        
        return {
            "success": workflow_success,
            "performance_metrics": performance_metrics,
            "results": {
                "analysis_stored": analysis_id is not None,
                "preference_stored": preference_memory_id is not None,
                "insight_stored": insight_memory_id is not None,
                "similar_analyses_found": len(similar_analyses),
                "user_memories_found": len(user_memories),
                "total_user_memories": len(all_user_memories)
            }
        }
        
    except Exception as e:
        logger.error(f"❌ Complete soil workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_concurrent_operations():
    """Test concurrent operations across all components."""
    logger.info("⚡ Testing concurrent operations...")
    
    try:
        # Test concurrent API calls
        async with httpx.AsyncClient() as client:
            api_tasks = [
                client.get(f"{BACKEND_URL}/health")
                for _ in range(5)
            ]
            
            start_time = time.time()
            api_responses = await asyncio.gather(*api_tasks)
            api_time = time.time() - start_time
            
            api_success = all(r.status_code == 200 for r in api_responses)
        
        # Test concurrent embedding generation
        embedder = BGEEmbedder()
        
        test_texts = [
            f"Soil analysis for block {i}: pH 6.{i}, nitrogen {30+i}ppm"
            for i in range(5)
        ]
        
        start_time = time.time()
        embedding_tasks = [
            embedder.embed_text_async(text)
            for text in test_texts
        ]
        embeddings = await asyncio.gather(*embedding_tasks)
        embedding_time = time.time() - start_time
        
        embedding_success = all(len(emb) == 1024 for emb in embeddings)
        
        logger.info(f"✅ Concurrent API calls: {len(api_responses)} in {api_time:.3f}s")
        logger.info(f"✅ Concurrent embeddings: {len(embeddings)} in {embedding_time:.3f}s")
        
        return {
            "success": api_success and embedding_success,
            "api_concurrent_time": api_time,
            "embedding_concurrent_time": embedding_time,
            "api_responses": len(api_responses),
            "embeddings_generated": len(embeddings)
        }
        
    except Exception as e:
        logger.error(f"❌ Concurrent operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def run_complete_integration_tests():
    """Run complete end-to-end integration tests."""
    logger.info("🚀 Starting Complete Soil System Integration Tests...")
    
    tests = [
        ("API Health & Connectivity", test_api_health_and_connectivity),
        ("Database Integration", test_database_integration),
        ("Complete Soil Workflow", test_complete_soil_workflow),
        ("Concurrent Operations", test_concurrent_operations),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"Running {test_name}...")
        logger.info(f"{'='*80}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result.get("success", False):
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # Calculate success rate
    passed = sum(1 for result in results.values() if result.get("success", False))
    total = len(results)
    success_rate = (passed / total) * 100
    
    # Summary
    logger.info(f"\n{'='*80}")
    logger.info("COMPLETE INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*80}")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({success_rate:.1f}% success rate)")
    
    if success_rate >= 90:
        logger.info("🎉 COMPLETE SOIL SYSTEM INTEGRATION SUCCESSFUL!")
        logger.info("✅ All components working seamlessly together!")
        return True, results, success_rate
    else:
        logger.error(f"💥 INTEGRATION ISSUES DETECTED! ({success_rate:.1f}% success rate)")
        return False, results, success_rate


if __name__ == "__main__":
    success, results, success_rate = asyncio.run(run_complete_integration_tests())
    
    # Print final metrics
    print(f"\n{'='*80}")
    print("FINAL INTEGRATION METRICS")
    print(f"{'='*80}")
    print(f"Success Rate: {success_rate:.1f}%")
    print(f"Tests Passed: {sum(1 for r in results.values() if r.get('success', False))}/{len(results)}")
    print(f"System Status: {'✅ PRODUCTION READY' if success_rate >= 90 else '❌ NEEDS ATTENTION'}")
    
    sys.exit(0 if success else 1)
