# Core ML libraries
numpy>=2.2.1
pandas>=2.2.3
scikit-learn>=1.6.0
xgboost>=2.1.3
torch>=2.5.1
shap>=0.46.0

# Spatial analysis
geopandas>=1.0.1
shapely>=2.0.6
pyproj>=3.7.0
rasterio>=1.4.3
pykrige>=1.7.2

# API and web framework
fastapi>=0.115.12
uvicorn[standard]>=0.32.1
pydantic[email]>=2.10.4
pydantic-settings>=2.7.0

# Database
sqlalchemy>=2.0.36
asyncpg>=0.30.0
psycopg2-binary>=2.9.10
geoalchemy2>=0.16.0

# Optimization and hyperparameter tuning
optuna>=4.1.0
hyperopt>=0.2.7

# Utilities
structlog>=24.5.0
python-dotenv>=1.0.1
aiofiles>=24.1.0
httpx>=0.28.1

# Visualization and plotting
matplotlib>=3.10.0
seaborn>=0.13.2
plotly>=5.24.1

# Embedding and vector search
sentence-transformers>=3.3.1
transformers>=4.47.1
torch>=2.5.1
pgvector>=0.3.6

# Memory and chat functionality
asyncio-mqtt>=0.16.2
schedule>=1.2.2
