#!/usr/bin/env python3
"""
Simple integration test for soil-ai component.

This script tests core functionality without foreign key dependencies.
"""

import asyncio
import sys
import os
import logging
import uuid
import json
from datetime import datetime

# Add the soil_ai module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'soil_ai'))

from soil_ai.embeddings.bge_embedder import BGEEmbedder
from soil_ai.embeddings.vector_search import VectorSearchService
from soil_ai.memory.mem0_adapter import Mem0Adapter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_core_functionality():
    """Test core functionality without foreign key dependencies."""
    logger.info("Testing core soil-ai functionality...")
    
    try:
        # Test BGE embedder
        logger.info("1. Testing BGE embedder...")
        embedder = BGEEmbedder()
        
        test_text = "Soil pH is 6.5 with high nitrogen content"
        embedding = await embedder.embed_text_async(test_text)
        
        assert embedding is not None
        assert len(embedding) == 1024
        logger.info("✅ BGE embedder working correctly")
        
        # Test vector search service
        logger.info("2. Testing vector search service...")
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Add test content
        test_content_id = str(uuid.uuid4())
        test_content = "Soil analysis shows pH 6.8, moisture 50%, nitrogen 40ppm"
        
        await vector_service.add_content_embedding(
            test_content_id,
            "soil_analysis",
            test_content,
            {"test": True, "timestamp": datetime.now().isoformat()}
        )
        logger.info("✅ Content embedding added successfully")
        
        # Search for similar content
        results = await vector_service.search_similar_content(
            "soil pH nitrogen analysis",
            limit=5
        )
        
        assert len(results) > 0
        logger.info(f"✅ Vector search working. Found {len(results)} results")
        
        # Test Mem0 adapter (without foreign key constraints)
        logger.info("3. Testing Mem0 adapter...")
        mem0_adapter = Mem0Adapter(DATABASE_URL, embedder)
        await mem0_adapter.initialize()
        
        # Use the pre-created test user
        test_user_id = "550e8400-e29b-41d4-a716-446655440000"
        
        # Add test memory
        memory_text = "User prefers organic fertilizers for their tomato crops"
        memory_id = await mem0_adapter.add_memory(
            test_user_id,
            memory_text,
            {"type": "preference", "crop": "tomato"}
        )
        
        assert memory_id is not None
        logger.info(f"✅ Memory added with ID: {memory_id}")
        
        # Search memories
        memories = await mem0_adapter.search_memories(
            test_user_id,
            "fertilizer preferences",
            limit=5
        )
        
        assert len(memories) > 0
        logger.info(f"✅ Memory search working. Found {len(memories)} memories")
        
        # Get statistics
        stats = await vector_service.get_content_statistics()
        logger.info(f"✅ Vector database stats: {stats}")
        
        memory_stats = await mem0_adapter.get_memory_stats(test_user_id)
        logger.info(f"✅ Memory stats: {memory_stats}")
        
        # Cleanup
        await vector_service.close()
        await mem0_adapter.close()
        
        logger.info("🎉 All core functionality tests PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Core functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_embedding_pipeline():
    """Test the embedding pipeline functionality."""
    logger.info("Testing embedding pipeline...")
    
    try:
        embedder = BGEEmbedder()
        
        # Test different types of soil data
        test_cases = [
            {
                'soil_ph': 6.5,
                'soil_moisture': 45.0,
                'soil_temperature': 25.0,
                'soil_nitrogen': 35.0
            },
            {
                'soil_ph': 7.2,
                'soil_moisture': 60.0,
                'soil_ec': 1.8,
                'soil_phosphorus': 25.0
            },
            {
                'soil_ph': 5.8,
                'soil_potassium': 150.0,
                'soil_temperature': 22.0
            }
        ]
        
        embeddings = []
        for i, soil_data in enumerate(test_cases):
            embedding = embedder.embed_soil_data(soil_data)
            embeddings.append(embedding)
            logger.info(f"✅ Generated embedding {i+1} with dimension {len(embedding)}")
        
        # Test similarity between embeddings
        similarity_1_2 = embedder.compute_similarity(embeddings[0], embeddings[1])
        similarity_1_3 = embedder.compute_similarity(embeddings[0], embeddings[2])
        
        logger.info(f"✅ Similarity between case 1 and 2: {similarity_1_2:.3f}")
        logger.info(f"✅ Similarity between case 1 and 3: {similarity_1_3:.3f}")
        
        # Test chat message embedding
        chat_embedding = embedder.embed_chat_message(
            "What's the optimal pH for tomatoes?",
            {"estate": "test_farm", "user_role": "farmer"}
        )
        
        assert len(chat_embedding) == 1024
        logger.info("✅ Chat message embedding working")
        
        logger.info("🎉 Embedding pipeline tests PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_simple_tests():
    """Run simplified integration tests."""
    logger.info("Starting simplified soil-ai integration tests...")
    
    tests = [
        ("Core Functionality", test_core_functionality),
        ("Embedding Pipeline", test_embedding_pipeline),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*60}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("SIMPLIFIED INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All simplified integration tests PASSED!")
        logger.info("✅ Soil-AI component is working correctly!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests FAILED!")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_simple_tests())
    sys.exit(0 if success else 1)
