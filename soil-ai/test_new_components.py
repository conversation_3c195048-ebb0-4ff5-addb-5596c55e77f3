#!/usr/bin/env python3
"""
Test script for new soil-ai components.

This script tests the newly implemented:
- BGE embeddings
- Vector search
- Mem0 AI integration
- Apache AGE graph functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add soil_ai to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_embeddings():
    """Test BGE embeddings functionality."""
    print("🧪 Testing BGE Embeddings...")
    
    try:
        from soil_ai.embeddings import BGEEmbedder
        
        # Initialize embedder (this will download the model if needed)
        embedder = BGEEmbedder()
        
        # Test basic embedding
        test_text = "Soil pH is 6.5 with high nitrogen content"
        embedding = embedder.embed_text(test_text)
        
        print(f"✅ BGE Embedder working - embedding dimension: {len(embedding)}")
        
        # Test soil data embedding
        soil_data = {
            'soil_ph': 6.5,
            'soil_moisture': 45.0,
            'soil_temperature': 25.0,
            'soil_nitrogen': 35.0
        }
        
        soil_embedding = embedder.embed_soil_data(soil_data)
        print(f"✅ Soil data embedding working - dimension: {len(soil_embedding)}")
        
        return True
        
    except Exception as e:
        print(f"❌ BGE Embeddings test failed: {e}")
        return False


async def test_vector_search():
    """Test vector search functionality."""
    print("\n🧪 Testing Vector Search...")
    
    try:
        from soil_ai.embeddings import VectorSearchService
        
        # Database URL from environment or default
        database_url = os.getenv('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
        
        # Initialize vector search service
        vector_service = VectorSearchService(database_url)
        await vector_service.initialize()
        
        # Test adding content embedding
        await vector_service.add_content_embedding(
            content_id="test_001",
            content_type="test_content",
            content_text="This is a test soil analysis with pH 6.8 and good moisture levels",
            metadata={"test": True, "ph": 6.8}
        )
        
        print("✅ Vector search service initialized and content added")
        
        # Test searching
        results = await vector_service.search_similar_content(
            "soil pH analysis",
            content_types=["test_content"],
            limit=5
        )
        
        print(f"✅ Vector search working - found {len(results)} results")
        
        await vector_service.close()
        return True
        
    except Exception as e:
        print(f"❌ Vector Search test failed: {e}")
        return False


async def test_mem0_adapter():
    """Test Mem0 AI adapter."""
    print("\n🧪 Testing Mem0 AI Adapter...")
    
    try:
        from soil_ai.memory import Mem0Adapter
        
        # Database URL from environment or default
        database_url = os.getenv('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
        
        # Initialize Mem0 adapter
        mem0_adapter = Mem0Adapter(database_url)
        
        # Test adding memory
        memory_id = await mem0_adapter.add_memory(
            message="User prefers organic fertilizers for tomato crops",
            user_id="test_user_001",
            metadata={"crop": "tomato", "preference": "organic"}
        )
        
        print(f"✅ Mem0 adapter working - added memory: {memory_id}")
        
        # Test searching memories
        memories = await mem0_adapter.search_memories(
            query="fertilizer preferences",
            user_id="test_user_001",
            limit=5
        )
        
        print(f"✅ Memory search working - found {len(memories)} memories")
        
        return True
        
    except Exception as e:
        print(f"❌ Mem0 Adapter test failed: {e}")
        return False


async def test_soil_memory():
    """Test soil-specific memory service."""
    print("\n🧪 Testing Soil Memory Service...")
    
    try:
        from soil_ai.memory import SoilMemoryService, SoilContext
        
        # Database URL from environment or default
        database_url = os.getenv('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
        
        # Initialize soil memory service
        soil_memory = SoilMemoryService(database_url)
        await soil_memory.initialize()
        
        # Create soil context
        soil_context = SoilContext(
            estate_name="Test Estate",
            block_name="Block A",
            user_role="agronomist",
            current_season="spring"
        )
        
        # Test adding soil observation
        memory_id = await soil_memory.add_soil_observation(
            user_id="test_user_001",
            observation="Noticed yellowing leaves in tomato plants, possibly nitrogen deficiency",
            soil_context=soil_context,
            observation_type="observation"
        )
        
        print(f"✅ Soil memory service working - added observation: {memory_id}")
        
        # Test getting relevant memories
        memories = await soil_memory.get_relevant_memories(
            user_id="test_user_001",
            query="tomato plant issues",
            soil_context=soil_context,
            limit=5
        )
        
        print(f"✅ Soil memory retrieval working - found {len(memories)} relevant memories")
        
        await soil_memory.close()
        return True
        
    except Exception as e:
        print(f"❌ Soil Memory Service test failed: {e}")
        return False


async def test_apache_age():
    """Test Apache AGE graph functionality."""
    print("\n🧪 Testing Apache AGE Graph Service...")
    
    try:
        from soil_ai.graph import SoilGraphService
        
        # Database URL from environment or default
        database_url = os.getenv('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
        
        # Initialize soil graph service
        graph_service = SoilGraphService(database_url)
        await graph_service.initialize()
        
        # Test getting parameter correlations
        correlations = await graph_service.get_parameter_correlations("pH")
        
        print(f"✅ Apache AGE service initialized - found {len(correlations)} pH correlations")
        
        # Test graph statistics
        stats = await graph_service.age_service.get_graph_statistics()
        
        print(f"✅ Graph statistics retrieved: {stats.get('total_nodes', 0)} nodes, {stats.get('total_relationships', 0)} relationships")
        
        await graph_service.close()
        return True
        
    except Exception as e:
        print(f"❌ Apache AGE Graph Service test failed: {e}")
        return False


async def test_database_connectivity():
    """Test basic database connectivity."""
    print("\n🧪 Testing Database Connectivity...")
    
    try:
        import asyncpg
        
        # Database URL from environment or default
        database_url = os.getenv('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
        
        # Test connection
        conn = await asyncpg.connect(database_url.replace('+asyncpg', ''))
        
        # Test vector extension
        result = await conn.fetchval("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
        if result:
            print("✅ pgvector extension available")
        else:
            print("❌ pgvector extension not found")
            
        # Test our tables
        tables = await conn.fetch("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN ('vector_embeddings', 'mem0_memories', 'chat_messages')
        """)
        
        table_names = [row['tablename'] for row in tables]
        print(f"✅ Found tables: {', '.join(table_names)}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connectivity test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Soil-AI Component Tests\n")
    
    # Test database connectivity first
    db_ok = await test_database_connectivity()
    if not db_ok:
        print("\n❌ Database connectivity failed - stopping tests")
        return
    
    # Run component tests
    tests = [
        test_embeddings,
        test_vector_search,
        test_mem0_adapter,
        test_soil_memory,
        test_apache_age
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Soil-AI components are working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    # Set environment variables if needed
    os.environ.setdefault('DATABASE_URL', 'postgresql://user_test:password123@localhost/db_test')
    
    # Run tests
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
