#!/usr/bin/env python3
"""
Comprehensive deployment verification for soil-ai component.

This script verifies:
- BGE-large-en embedding model deployment
- 1024-dimensional vector operations
- Vector search functionality with performance metrics
- Mem0 AI integration with clean adapter architecture
- User context isolation and memory operations
"""

import asyncio
import sys
import os
import logging
import uuid
import time
from datetime import datetime
from typing import List, Dict, Any

# Add the soil_ai module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'soil_ai'))

from soil_ai.embeddings.bge_embedder import BGEEmbedder
from soil_ai.embeddings.vector_search import VectorSearchService
from soil_ai.memory.mem0_adapter import Mem0Adapter
from soil_ai.memory.soil_memory_service import SoilMemoryService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_bge_model_deployment():
    """Test BGE-large-en model deployment and performance."""
    logger.info("🧠 Testing BGE-large-en model deployment...")
    
    try:
        embedder = BGEEmbedder()
        
        # Test soil-specific scenarios
        soil_scenarios = [
            "Soil pH is 6.8 with optimal nitrogen levels for corn cultivation",
            "Sandy loam soil with low phosphorus content requires fertilization",
            "Clay soil with poor drainage affecting root development",
            "Organic matter content at 3.2% supports healthy microbial activity",
            "Soil temperature at 22°C optimal for seed germination"
        ]
        
        embeddings = []
        total_time = 0
        
        for i, scenario in enumerate(soil_scenarios):
            start_time = time.time()
            embedding = await embedder.embed_text_async(scenario)
            end_time = time.time()
            
            generation_time = end_time - start_time
            total_time += generation_time
            
            embeddings.append(embedding)
            
            # Verify embedding properties
            assert embedding is not None, f"Embedding {i+1} is None"
            assert len(embedding) == 1024, f"Embedding {i+1} has wrong dimension: {len(embedding)}"
            
            logger.info(f"✅ Embedding {i+1}: {generation_time:.3f}s, dimension: {len(embedding)}")
        
        avg_time = total_time / len(soil_scenarios)
        
        # Test similarity calculations
        similarity_1_2 = embedder.compute_similarity(embeddings[0], embeddings[1])
        similarity_1_3 = embedder.compute_similarity(embeddings[0], embeddings[2])
        
        # Performance verification
        performance_met = avg_time < 0.1  # <0.1s requirement
        
        logger.info(f"📊 Performance Metrics:")
        logger.info(f"   Average generation time: {avg_time:.3f}s")
        logger.info(f"   Performance requirement (<0.1s): {'✅ MET' if performance_met else '❌ NOT MET'}")
        logger.info(f"   Similarity pH-Phosphorus: {similarity_1_2:.3f}")
        logger.info(f"   Similarity pH-Drainage: {similarity_1_3:.3f}")
        
        return {
            "success": True,
            "avg_generation_time": avg_time,
            "performance_met": performance_met,
            "embeddings_generated": len(embeddings),
            "dimension": len(embeddings[0]) if embeddings else 0,
            "similarities": {
                "ph_phosphorus": similarity_1_2,
                "ph_drainage": similarity_1_3
            }
        }
        
    except Exception as e:
        logger.error(f"❌ BGE model deployment test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_vector_search_functionality():
    """Test vector search functionality with soil data scenarios."""
    logger.info("🔍 Testing vector search functionality...")
    
    try:
        embedder = BGEEmbedder()
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Add diverse soil data for testing
        soil_data_scenarios = [
            {
                "id": str(uuid.uuid4()),
                "content": "Soil analysis Block A1: pH 6.5, N 45ppm, P 25ppm, K 180ppm",
                "metadata": {"block": "A1", "ph": 6.5, "nitrogen": 45, "phosphorus": 25, "potassium": 180}
            },
            {
                "id": str(uuid.uuid4()),
                "content": "Soil analysis Block B2: pH 7.2, N 38ppm, P 30ppm, K 165ppm",
                "metadata": {"block": "B2", "ph": 7.2, "nitrogen": 38, "phosphorus": 30, "potassium": 165}
            },
            {
                "id": str(uuid.uuid4()),
                "content": "Soil analysis Block C3: pH 5.8, N 52ppm, P 18ppm, K 195ppm",
                "metadata": {"block": "C3", "ph": 5.8, "nitrogen": 52, "phosphorus": 18, "potassium": 195}
            }
        ]
        
        # Add soil data to vector store
        for data in soil_data_scenarios:
            await vector_service.add_content_embedding(
                data["id"],
                "soil_analysis",
                data["content"],
                data["metadata"]
            )
        
        logger.info(f"✅ Added {len(soil_data_scenarios)} soil analysis records")
        
        # Test semantic search queries
        search_queries = [
            "high nitrogen soil blocks",
            "low pH acidic soil conditions",
            "phosphorus deficient areas",
            "optimal potassium levels"
        ]
        
        search_results = {}
        for query in search_queries:
            start_time = time.time()
            results = await vector_service.search_similar_content(query, limit=5)
            end_time = time.time()
            
            search_time = end_time - start_time
            search_results[query] = {
                "results_count": len(results),
                "search_time": search_time,
                "results": results
            }
            
            logger.info(f"🔍 Query: '{query}' → {len(results)} results in {search_time:.3f}s")
        
        # Get statistics
        stats = await vector_service.get_content_statistics()
        
        await vector_service.close()
        
        return {
            "success": True,
            "scenarios_added": len(soil_data_scenarios),
            "search_results": search_results,
            "database_stats": stats
        }
        
    except Exception as e:
        logger.error(f"❌ Vector search functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_mem0_integration():
    """Test Mem0 AI integration with clean adapter architecture."""
    logger.info("🧠 Testing Mem0 AI integration...")
    
    try:
        embedder = BGEEmbedder()
        mem0_adapter = Mem0Adapter(DATABASE_URL, embedder)
        await mem0_adapter.initialize()
        
        # Test user context isolation
        test_users = [
            "2ce2f130-5c65-4cc7-b35b-e4fa4a8a7263",  # Existing user
            "163ec109-b435-4312-bb80-807edf4b6239"   # Another existing user
        ]
        
        user_memories = {}
        
        for user_id in test_users:
            # Add user-specific memories
            memories = [
                f"User {user_id[:8]} prefers organic fertilizers for sustainable farming",
                f"User {user_id[:8]} has 50 acres of corn and 30 acres of soybeans",
                f"User {user_id[:8]} typically plants in early May based on soil temperature"
            ]
            
            user_memory_ids = []
            for memory_text in memories:
                memory_id = await mem0_adapter.add_memory(
                    user_id,
                    memory_text,
                    {"type": "preference", "category": "farming_practice"}
                )
                user_memory_ids.append(memory_id)
            
            # Test memory search for this user
            search_results = await mem0_adapter.search_memories(
                user_id,
                "organic farming preferences",
                limit=5
            )
            
            # Test memory retrieval
            all_memories = await mem0_adapter.get_all_memories(user_id)
            
            user_memories[user_id] = {
                "added_memories": len(user_memory_ids),
                "search_results": len(search_results),
                "total_memories": len(all_memories),
                "memory_ids": user_memory_ids
            }
            
            logger.info(f"✅ User {user_id[:8]}: {len(user_memory_ids)} memories added, {len(search_results)} found in search")
        
        # Test cross-user isolation
        user1_memories = await mem0_adapter.get_all_memories(test_users[0])
        user2_memories = await mem0_adapter.get_all_memories(test_users[1])
        
        isolation_verified = len(user1_memories) != len(user2_memories) or any(
            m1['memory_text'] != m2['memory_text'] 
            for m1, m2 in zip(user1_memories, user2_memories)
        )
        
        await mem0_adapter.close()
        
        return {
            "success": True,
            "users_tested": len(test_users),
            "user_memories": user_memories,
            "isolation_verified": isolation_verified
        }
        
    except Exception as e:
        logger.error(f"❌ Mem0 integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_soil_memory_service():
    """Test soil-specific memory service functionality."""
    logger.info("🌱 Testing soil memory service...")
    
    try:
        memory_service = SoilMemoryService(DATABASE_URL)
        await memory_service.initialize()
        
        test_user_id = "2ce2f130-5c65-4cc7-b35b-e4fa4a8a7263"
        
        # Test soil-specific memory operations
        preference_id = await memory_service.store_user_preference(
            test_user_id,
            "fertilizer_type",
            "organic_only",
            {"crop_type": "corn", "season": "spring_2025"}
        )
        
        insight_id = await memory_service.store_soil_insight(
            test_user_id,
            "Block A1 shows consistent pH decline over 3 months, recommend lime application",
            {"block": "A1", "ph_trend": "declining", "recommendation": "lime"},
            {"priority": "high", "action_required": True}
        )
        
        # Test context retrieval
        context = await memory_service.get_relevant_context(
            test_user_id,
            "pH lime application recommendations",
            limit=5
        )
        
        await memory_service.close()
        
        return {
            "success": True,
            "preference_stored": preference_id is not None,
            "insight_stored": insight_id is not None,
            "context_retrieved": len(context) > 0,
            "context_items": len(context)
        }
        
    except Exception as e:
        logger.error(f"❌ Soil memory service test failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def run_soil_ai_deployment_verification():
    """Run comprehensive soil-ai deployment verification."""
    logger.info("🚀 Starting Soil-AI Deployment Verification...")
    
    tests = [
        ("BGE Model Deployment", test_bge_model_deployment),
        ("Vector Search Functionality", test_vector_search_functionality),
        ("Mem0 AI Integration", test_mem0_integration),
        ("Soil Memory Service", test_soil_memory_service),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*70}")
        logger.info(f"Running {test_name}...")
        logger.info(f"{'='*70}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result.get("success", False):
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("SOIL-AI DEPLOYMENT VERIFICATION SUMMARY")
    logger.info(f"{'='*70}")
    
    passed = sum(1 for result in results.values() if result.get("success", False))
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    success_rate = (passed / total) * 100
    if success_rate >= 90:
        logger.info(f"🎉 SOIL-AI DEPLOYMENT SUCCESSFUL! ({success_rate:.1f}% success rate)")
        return True, results
    else:
        logger.error(f"💥 SOIL-AI DEPLOYMENT ISSUES! ({success_rate:.1f}% success rate)")
        return False, results


if __name__ == "__main__":
    success, results = asyncio.run(run_soil_ai_deployment_verification())
    sys.exit(0 if success else 1)
