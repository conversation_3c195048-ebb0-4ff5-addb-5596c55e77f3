"""
High-level soil graph service for relationship analysis.

This module provides soil-specific graph operations for:
- Parameter correlation analysis
- Cause-effect relationship modeling
- Anomaly causality detection
- Regional correlation patterns
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import uuid

from .age_service import ApacheAGEService, GraphNode, GraphRelationship

logger = logging.getLogger(__name__)


@dataclass
class SoilCorrelation:
    """Soil parameter correlation."""
    source_parameter: str
    target_parameter: str
    correlation_strength: float
    relationship_type: str
    description: str


@dataclass
class SoilAnomaly:
    """Soil anomaly detection result."""
    parameter: str
    value: float
    expected_range: Tuple[float, float]
    severity: str
    potential_causes: List[str]


class SoilGraphService:
    """
    High-level soil graph service.
    
    This service provides soil-specific graph operations using
    Apache AGE for relationship modeling and analysis.
    """
    
    def __init__(self, database_url: str, graph_name: str = "soil_relationships"):
        """
        Initialize soil graph service.
        
        Args:
            database_url: PostgreSQL database URL
            graph_name: Name of the AGE graph
        """
        self.age_service = ApacheAGEService(database_url, graph_name)
        
    async def initialize(self) -> None:
        """Initialize the service."""
        await self.age_service.initialize()
        logger.info("Soil graph service initialized")
        
    async def close(self) -> None:
        """Close the service."""
        await self.age_service.close()
    
    async def add_sensor_reading(
        self,
        reading_id: str,
        sensor_id: str,
        estate_name: str,
        block_name: str,
        soil_data: Dict[str, float],
        timestamp: datetime
    ) -> None:
        """
        Add sensor reading to the graph.
        
        Args:
            reading_id: Unique reading ID
            sensor_id: Sensor ID
            estate_name: Estate name
            block_name: Block name
            soil_data: Soil parameter values
            timestamp: Reading timestamp
        """
        try:
            # Create sensor reading node
            properties = {
                'id': reading_id,
                'sensor_id': sensor_id,
                'estate': estate_name,
                'block': block_name,
                'timestamp': timestamp.isoformat(),
                **soil_data
            }
            
            await self.age_service.create_node('SensorReading', properties)
            
            # Create relationships to soil parameters
            for param_name, value in soil_data.items():
                if value is not None:
                    await self._link_reading_to_parameter(reading_id, param_name, value, timestamp)
                    
            logger.debug(f"Added sensor reading {reading_id} to graph")
            
        except Exception as e:
            logger.error(f"Failed to add sensor reading to graph: {e}")
            raise
    
    async def _link_reading_to_parameter(
        self,
        reading_id: str,
        parameter_name: str,
        value: float,
        timestamp: datetime
    ) -> None:
        """Link sensor reading to soil parameter."""
        # Find the parameter node
        param_nodes = await self.age_service.find_nodes(
            'SoilParameter',
            {'name': parameter_name}
        )
        
        if not param_nodes:
            logger.warning(f"Soil parameter '{parameter_name}' not found in graph")
            return
            
        # Find the reading node
        reading_nodes = await self.age_service.find_nodes(
            'SensorReading',
            {'id': reading_id}
        )
        
        if not reading_nodes:
            logger.warning(f"Sensor reading '{reading_id}' not found in graph")
            return
            
        # Create relationship
        relationship_props = {
            'value': value,
            'timestamp': timestamp.isoformat()
        }
        
        await self.age_service.create_relationship(
            reading_nodes[0].id,
            param_nodes[0].id,
            'MEASURED_AT',
            relationship_props
        )
    
    async def get_parameter_correlations(self, parameter_name: str) -> List[SoilCorrelation]:
        """
        Get correlations for a soil parameter.
        
        Args:
            parameter_name: Name of the soil parameter
            
        Returns:
            List of correlations
        """
        try:
            # Find correlations using Cypher query
            cypher_query = f"""
            MATCH (source:SoilParameter {{name: '{parameter_name}'}})-[r:CORRELATES_WITH]->(target:SoilParameter)
            RETURN source.name, target.name, r.correlation, r.strength, r.description
            """
            
            results = await self.age_service.execute_cypher(cypher_query)
            
            correlations = []
            for result in results:
                correlation = SoilCorrelation(
                    source_parameter=result.get('source.name', ''),
                    target_parameter=result.get('target.name', ''),
                    correlation_strength=float(result.get('r.correlation', 0)),
                    relationship_type=result.get('r.strength', ''),
                    description=result.get('r.description', '')
                )
                correlations.append(correlation)
                
            return correlations
            
        except Exception as e:
            logger.error(f"Failed to get parameter correlations: {e}")
            return []
    
    async def analyze_soil_anomalies(
        self,
        soil_data: Dict[str, float],
        estate_name: str,
        block_name: str
    ) -> List[SoilAnomaly]:
        """
        Analyze soil data for anomalies using graph relationships.
        
        Args:
            soil_data: Current soil parameter values
            estate_name: Estate name
            block_name: Block name
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        try:
            # Get optimal ranges for each parameter
            for param_name, value in soil_data.items():
                if value is None:
                    continue
                    
                # Get parameter optimal range from graph
                cypher_query = f"""
                MATCH (p:SoilParameter {{name: '{param_name}'}})
                RETURN p.optimal_min, p.optimal_max, p.description
                """
                
                results = await self.age_service.execute_cypher(cypher_query)
                
                if not results:
                    continue
                    
                result = results[0]
                optimal_min = float(result.get('p.optimal_min', 0))
                optimal_max = float(result.get('p.optimal_max', 100))
                
                # Check if value is outside optimal range
                if value < optimal_min or value > optimal_max:
                    # Find potential causes using graph relationships
                    potential_causes = await self._find_anomaly_causes(param_name, value)
                    
                    # Determine severity
                    severity = self._calculate_anomaly_severity(value, optimal_min, optimal_max)
                    
                    anomaly = SoilAnomaly(
                        parameter=param_name,
                        value=value,
                        expected_range=(optimal_min, optimal_max),
                        severity=severity,
                        potential_causes=potential_causes
                    )
                    anomalies.append(anomaly)
                    
            return anomalies
            
        except Exception as e:
            logger.error(f"Failed to analyze soil anomalies: {e}")
            return []
    
    async def _find_anomaly_causes(self, parameter_name: str, value: float) -> List[str]:
        """Find potential causes for parameter anomaly."""
        try:
            # Find parameters that can cause changes in the target parameter
            cypher_query = f"""
            MATCH (cause:SoilParameter)-[r:CAUSES]->(target:SoilParameter {{name: '{parameter_name}'}})
            RETURN cause.name, r.effect, r.description
            """
            
            results = await self.age_service.execute_cypher(cypher_query)
            
            causes = []
            for result in results:
                cause_name = result.get('cause.name', '')
                effect = result.get('r.effect', '')
                description = result.get('r.description', '')
                
                causes.append(f"{cause_name}: {description}")
                
            return causes
            
        except Exception as e:
            logger.error(f"Failed to find anomaly causes: {e}")
            return []
    
    def _calculate_anomaly_severity(self, value: float, min_val: float, max_val: float) -> str:
        """Calculate anomaly severity based on deviation from optimal range."""
        range_size = max_val - min_val
        
        if value < min_val:
            deviation = (min_val - value) / range_size
        else:
            deviation = (value - max_val) / range_size
            
        if deviation > 0.5:
            return "critical"
        elif deviation > 0.3:
            return "high"
        elif deviation > 0.1:
            return "medium"
        else:
            return "low"
    
    async def get_regional_correlations(
        self,
        estate_name: str,
        parameter_name: str,
        time_window_hours: int = 24
    ) -> Dict[str, Any]:
        """
        Get regional correlations for a parameter within an estate.
        
        Args:
            estate_name: Estate name
            parameter_name: Soil parameter name
            time_window_hours: Time window for analysis
            
        Returns:
            Regional correlation analysis
        """
        try:
            # Find recent readings for the parameter in the estate
            cypher_query = f"""
            MATCH (reading:SensorReading)-[r:MEASURED_AT]->(param:SoilParameter {{name: '{parameter_name}'}})
            WHERE reading.estate = '{estate_name}'
            AND datetime(reading.timestamp) > datetime() - duration({{hours: {time_window_hours}}})
            RETURN reading.block, r.value, reading.timestamp
            ORDER BY reading.timestamp DESC
            """
            
            results = await self.age_service.execute_cypher(cypher_query)
            
            # Group by block
            block_data = {}
            for result in results:
                block = result.get('reading.block', '')
                value = float(result.get('r.value', 0))
                timestamp = result.get('reading.timestamp', '')
                
                if block not in block_data:
                    block_data[block] = []
                    
                block_data[block].append({
                    'value': value,
                    'timestamp': timestamp
                })
            
            # Calculate statistics per block
            block_stats = {}
            for block, readings in block_data.items():
                values = [r['value'] for r in readings]
                if values:
                    block_stats[block] = {
                        'count': len(values),
                        'average': sum(values) / len(values),
                        'min': min(values),
                        'max': max(values),
                        'latest': readings[0]['value']  # Most recent
                    }
            
            return {
                'estate': estate_name,
                'parameter': parameter_name,
                'time_window_hours': time_window_hours,
                'block_statistics': block_stats,
                'total_readings': sum(len(readings) for readings in block_data.values())
            }
            
        except Exception as e:
            logger.error(f"Failed to get regional correlations: {e}")
            return {}
    
    async def add_prediction_to_graph(
        self,
        prediction_id: str,
        predicted_parameters: Dict[str, float],
        confidence_scores: Dict[str, float],
        model_version: str,
        location: Tuple[float, float],
        timestamp: datetime
    ) -> None:
        """
        Add prediction result to the graph.
        
        Args:
            prediction_id: Unique prediction ID
            predicted_parameters: Predicted parameter values
            confidence_scores: Confidence scores for predictions
            model_version: Model version used
            location: Prediction location (lat, lon)
            timestamp: Prediction timestamp
        """
        try:
            # Create prediction node
            properties = {
                'id': prediction_id,
                'model_version': model_version,
                'latitude': location[0],
                'longitude': location[1],
                'timestamp': timestamp.isoformat(),
                **predicted_parameters
            }
            
            await self.age_service.create_node('Prediction', properties)
            
            # Create relationships to predicted parameters
            for param_name, value in predicted_parameters.items():
                confidence = confidence_scores.get(param_name, 0.0)
                await self._link_prediction_to_parameter(
                    prediction_id, param_name, value, confidence, timestamp
                )
                
            logger.debug(f"Added prediction {prediction_id} to graph")
            
        except Exception as e:
            logger.error(f"Failed to add prediction to graph: {e}")
            raise
    
    async def _link_prediction_to_parameter(
        self,
        prediction_id: str,
        parameter_name: str,
        predicted_value: float,
        confidence: float,
        timestamp: datetime
    ) -> None:
        """Link prediction to soil parameter."""
        # Find the parameter node
        param_nodes = await self.age_service.find_nodes(
            'SoilParameter',
            {'name': parameter_name}
        )
        
        if not param_nodes:
            return
            
        # Find the prediction node
        prediction_nodes = await self.age_service.find_nodes(
            'Prediction',
            {'id': prediction_id}
        )
        
        if not prediction_nodes:
            return
            
        # Create relationship
        relationship_props = {
            'predicted_value': predicted_value,
            'confidence': confidence,
            'timestamp': timestamp.isoformat()
        }
        
        await self.age_service.create_relationship(
            prediction_nodes[0].id,
            param_nodes[0].id,
            'PREDICTS',
            relationship_props
        )
    
    async def get_graph_insights(self) -> Dict[str, Any]:
        """
        Get insights from the soil graph.
        
        Returns:
            Dictionary with graph insights
        """
        try:
            # Get basic statistics
            stats = await self.age_service.get_graph_statistics()
            
            # Get strongest correlations
            cypher_query = """
            MATCH (source:SoilParameter)-[r:CORRELATES_WITH]->(target:SoilParameter)
            RETURN source.name, target.name, r.correlation, r.strength
            ORDER BY abs(r.correlation) DESC
            LIMIT 5
            """
            
            correlation_results = await self.age_service.execute_cypher(cypher_query)
            
            strongest_correlations = []
            for result in correlation_results:
                strongest_correlations.append({
                    'source': result.get('source.name', ''),
                    'target': result.get('target.name', ''),
                    'correlation': float(result.get('r.correlation', 0)),
                    'strength': result.get('r.strength', '')
                })
            
            return {
                'statistics': stats,
                'strongest_correlations': strongest_correlations,
                'insights_generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get graph insights: {e}")
            return {'error': str(e)}
