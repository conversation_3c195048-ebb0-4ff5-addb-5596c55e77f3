"""
Apache AGE service for graph database operations.

This module provides low-level Apache AGE graph database operations
for soil parameter relationship modeling.
"""

import logging
import async<PERSON>
from typing import Dict, Any, List, Optional, Tuple
import asyncpg
import json
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class GraphNode:
    """Graph node representation."""
    id: str
    label: str
    properties: Dict[str, Any]


@dataclass
class GraphEdge:
    """Graph edge representation."""
    source_id: str
    target_id: str
    label: str
    properties: Dict[str, Any]


@dataclass
class GraphRelationship:
    """Graph relationship with source and target nodes."""
    source: GraphNode
    target: GraphNode
    edge: GraphEdge


class ApacheAGEService:
    """
    Apache AGE graph database service.
    
    This service provides low-level operations for the Apache AGE
    graph database to model soil parameter relationships.
    """
    
    def __init__(self, database_url: str, graph_name: str = "soil_relationships"):
        """
        Initialize Apache AGE service.
        
        Args:
            database_url: PostgreSQL database URL
            graph_name: Name of the AGE graph
        """
        self.database_url = database_url
        self.graph_name = graph_name
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize database connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=5,
                command_timeout=30
            )
            
            # Test AGE functionality
            await self._test_age_connection()
            
            logger.info("Apache AGE service initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Apache AGE service: {e}")
            raise
    
    async def close(self) -> None:
        """Close database connections."""
        if self.connection_pool:
            await self.connection_pool.close()
    
    async def _test_age_connection(self) -> None:
        """Test Apache AGE connection and setup."""
        try:
            async with self.connection_pool.acquire() as conn:
                # Set search path
                await conn.execute("SET search_path = ag_catalog, \"$user\", public")
                
                # Test if graph exists
                result = await conn.fetchval(
                    "SELECT count(*) FROM ag_catalog.ag_graph WHERE name = $1",
                    self.graph_name
                )
                
                if result == 0:
                    logger.warning(f"Graph '{self.graph_name}' not found. Run setup_apache_age.sql as superuser.")
                else:
                    logger.info(f"Graph '{self.graph_name}' found and accessible")
                    
        except Exception as e:
            logger.warning(f"Apache AGE test failed: {e}")
            # Don't raise here - graph might not be set up yet
    
    async def execute_cypher(self, cypher_query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Execute a Cypher query.
        
        Args:
            cypher_query: Cypher query string
            parameters: Query parameters
            
        Returns:
            List of query results
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                # Set search path
                await conn.execute("SET search_path = ag_catalog, \"$user\", public")
                
                # Execute cypher query
                query = f"SELECT * FROM cypher('{self.graph_name}', $${cypher_query}$$) AS (result agtype)"
                
                rows = await conn.fetch(query)
                
                # Parse agtype results
                results = []
                for row in rows:
                    if row['result']:
                        # Parse agtype to Python object
                        result_data = self._parse_agtype(row['result'])
                        results.append(result_data)
                        
                return results
                
        except Exception as e:
            logger.error(f"Failed to execute Cypher query: {e}")
            raise
    
    def _parse_agtype(self, agtype_value: str) -> Dict[str, Any]:
        """
        Parse Apache AGE agtype value to Python object.
        
        Args:
            agtype_value: AGE agtype string
            
        Returns:
            Parsed Python object
        """
        try:
            # AGE returns JSON-like strings that need parsing
            if isinstance(agtype_value, str):
                return json.loads(agtype_value)
            return agtype_value
        except (json.JSONDecodeError, TypeError):
            return {"raw_value": str(agtype_value)}
    
    async def create_node(self, label: str, properties: Dict[str, Any]) -> str:
        """
        Create a graph node.
        
        Args:
            label: Node label
            properties: Node properties
            
        Returns:
            Created node ID
        """
        # Build properties string
        props_str = self._build_properties_string(properties)
        
        cypher_query = f"CREATE (n:{label} {props_str}) RETURN id(n)"
        
        results = await self.execute_cypher(cypher_query)
        
        if results:
            return str(results[0].get('id', ''))
        
        raise RuntimeError("Failed to create node")
    
    async def create_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        properties: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Create a relationship between two nodes.
        
        Args:
            source_id: Source node ID
            target_id: Target node ID
            relationship_type: Relationship type/label
            properties: Relationship properties
        """
        props_str = ""
        if properties:
            props_str = self._build_properties_string(properties)
            
        cypher_query = f"""
        MATCH (a), (b)
        WHERE id(a) = {source_id} AND id(b) = {target_id}
        CREATE (a)-[r:{relationship_type} {props_str}]->(b)
        """
        
        await self.execute_cypher(cypher_query)
    
    async def find_nodes(self, label: str, properties: Optional[Dict[str, Any]] = None) -> List[GraphNode]:
        """
        Find nodes by label and properties.
        
        Args:
            label: Node label
            properties: Properties to match
            
        Returns:
            List of matching nodes
        """
        where_clause = ""
        if properties:
            conditions = []
            for key, value in properties.items():
                if isinstance(value, str):
                    conditions.append(f"n.{key} = '{value}'")
                else:
                    conditions.append(f"n.{key} = {value}")
            where_clause = "WHERE " + " AND ".join(conditions)
            
        cypher_query = f"MATCH (n:{label}) {where_clause} RETURN n, id(n)"
        
        results = await self.execute_cypher(cypher_query)
        
        nodes = []
        for result in results:
            node_data = result.get('n', {})
            node_id = str(result.get('id', ''))
            
            node = GraphNode(
                id=node_id,
                label=label,
                properties=node_data.get('properties', {})
            )
            nodes.append(node)
            
        return nodes
    
    async def find_relationships(
        self,
        source_label: Optional[str] = None,
        target_label: Optional[str] = None,
        relationship_type: Optional[str] = None
    ) -> List[GraphRelationship]:
        """
        Find relationships between nodes.
        
        Args:
            source_label: Source node label filter
            target_label: Target node label filter
            relationship_type: Relationship type filter
            
        Returns:
            List of relationships
        """
        # Build match pattern
        source_pattern = f":{source_label}" if source_label else ""
        target_pattern = f":{target_label}" if target_label else ""
        rel_pattern = f":{relationship_type}" if relationship_type else ""
        
        cypher_query = f"""
        MATCH (a{source_pattern})-[r{rel_pattern}]->(b{target_pattern})
        RETURN a, r, b, id(a), id(b)
        """
        
        results = await self.execute_cypher(cypher_query)
        
        relationships = []
        for result in results:
            source_data = result.get('a', {})
            target_data = result.get('b', {})
            edge_data = result.get('r', {})
            source_id = str(result.get('id(a)', ''))
            target_id = str(result.get('id(b)', ''))
            
            source_node = GraphNode(
                id=source_id,
                label=source_data.get('label', ''),
                properties=source_data.get('properties', {})
            )
            
            target_node = GraphNode(
                id=target_id,
                label=target_data.get('label', ''),
                properties=target_data.get('properties', {})
            )
            
            edge = GraphEdge(
                source_id=source_id,
                target_id=target_id,
                label=edge_data.get('label', ''),
                properties=edge_data.get('properties', {})
            )
            
            relationship = GraphRelationship(
                source=source_node,
                target=target_node,
                edge=edge
            )
            relationships.append(relationship)
            
        return relationships
    
    def _build_properties_string(self, properties: Dict[str, Any]) -> str:
        """
        Build properties string for Cypher queries.
        
        Args:
            properties: Properties dictionary
            
        Returns:
            Formatted properties string
        """
        if not properties:
            return ""
            
        prop_parts = []
        for key, value in properties.items():
            if isinstance(value, str):
                prop_parts.append(f"{key}: '{value}'")
            elif isinstance(value, (int, float)):
                prop_parts.append(f"{key}: {value}")
            elif isinstance(value, bool):
                prop_parts.append(f"{key}: {str(value).lower()}")
            else:
                # Convert complex types to JSON string
                prop_parts.append(f"{key}: '{json.dumps(value)}'")
                
        return "{" + ", ".join(prop_parts) + "}"
    
    async def get_graph_statistics(self) -> Dict[str, Any]:
        """
        Get graph statistics.
        
        Returns:
            Dictionary with graph statistics
        """
        try:
            # Count nodes by label
            node_counts = {}
            labels = ['SoilParameter', 'SensorReading', 'Estate', 'Block', 'Prediction', 'Anomaly']
            
            for label in labels:
                cypher_query = f"MATCH (n:{label}) RETURN count(n)"
                results = await self.execute_cypher(cypher_query)
                count = results[0].get('count', 0) if results else 0
                node_counts[label] = count
            
            # Count relationships by type
            rel_counts = {}
            rel_types = ['CORRELATES_WITH', 'CAUSES', 'MEASURED_AT', 'LOCATED_IN', 'PREDICTS', 'INDICATES']
            
            for rel_type in rel_types:
                cypher_query = f"MATCH ()-[r:{rel_type}]->() RETURN count(r)"
                results = await self.execute_cypher(cypher_query)
                count = results[0].get('count', 0) if results else 0
                rel_counts[rel_type] = count
            
            return {
                'graph_name': self.graph_name,
                'node_counts': node_counts,
                'relationship_counts': rel_counts,
                'total_nodes': sum(node_counts.values()),
                'total_relationships': sum(rel_counts.values())
            }
            
        except Exception as e:
            logger.error(f"Failed to get graph statistics: {e}")
            return {
                'graph_name': self.graph_name,
                'error': str(e)
            }
