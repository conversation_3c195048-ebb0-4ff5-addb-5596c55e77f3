"""
BGE-Large-EN embedding model implementation for soil-ai system.

This module provides the bge-large-en embedding model as specified in PRD.md
for semantic representation and vector similarity search.
"""

import logging
import numpy as np
import torch
from typing import List, Union, Optional, Dict, Any
from sentence_transformers import SentenceTransformer
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class BGEEmbedder:
    """
    BGE-Large-EN embedding model for generating 1536-dimensional vectors.
    
    This class implements the bge-large-en model as specified in the PRD for:
    - Semantic representation of soil data
    - Chat message embeddings for Mem0 AI
    - Vector similarity search capabilities
    """
    
    def __init__(
        self,
        model_name: str = "BAAI/bge-large-en-v1.5",
        device: Optional[str] = None,
        batch_size: int = 32,
        max_length: int = 512
    ):
        """
        Initialize BGE embedder.
        
        Args:
            model_name: HuggingFace model name for BGE
            device: Device to run model on (auto-detect if None)
            batch_size: Batch size for processing
            max_length: Maximum sequence length
        """
        self.model_name = model_name
        self.batch_size = batch_size
        self.max_length = max_length
        
        # Auto-detect device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
            
        logger.info(f"Initializing BGE embedder on device: {self.device}")
        
        # Load model
        self.model = None
        self.executor = ThreadPoolExecutor(max_workers=2)
        self._load_model()
        
    def _load_model(self) -> None:
        """Load the BGE model."""
        try:
            self.model = SentenceTransformer(
                self.model_name,
                device=self.device
            )
            
            # Set model parameters
            self.model.max_seq_length = self.max_length
            
            # Get embedding dimension
            self.embedding_dim = self.model.get_sentence_embedding_dimension()
            
            logger.info(f"BGE model loaded successfully. Embedding dimension: {self.embedding_dim}")
            
        except Exception as e:
            logger.error(f"Failed to load BGE model: {e}")
            raise
    
    def embed_text(self, text: Union[str, List[str]]) -> np.ndarray:
        """
        Generate embeddings for text(s).
        
        Args:
            text: Single text string or list of texts
            
        Returns:
            Numpy array of embeddings
        """
        if self.model is None:
            raise RuntimeError("Model not loaded")
            
        # Ensure input is a list
        if isinstance(text, str):
            texts = [text]
            single_input = True
        else:
            texts = text
            single_input = False
            
        # Generate embeddings
        embeddings = self.model.encode(
            texts,
            batch_size=self.batch_size,
            show_progress_bar=len(texts) > 10,
            convert_to_numpy=True,
            normalize_embeddings=True  # Normalize for cosine similarity
        )
        
        # Return single embedding if single input
        if single_input:
            return embeddings[0]
        
        return embeddings
    
    async def embed_text_async(self, text: Union[str, List[str]]) -> np.ndarray:
        """
        Asynchronously generate embeddings for text(s).
        
        Args:
            text: Single text string or list of texts
            
        Returns:
            Numpy array of embeddings
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.embed_text,
            text
        )
    
    def embed_soil_data(self, soil_data: Dict[str, Any]) -> np.ndarray:
        """
        Generate embeddings for soil data.
        
        Args:
            soil_data: Dictionary containing soil parameters
            
        Returns:
            Embedding vector for the soil data
        """
        # Convert soil data to text representation
        text_repr = self._soil_data_to_text(soil_data)
        return self.embed_text(text_repr)
    
    def embed_chat_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> np.ndarray:
        """
        Generate embeddings for chat messages with optional context.
        
        Args:
            message: Chat message text
            context: Optional context information
            
        Returns:
            Embedding vector for the message
        """
        # Combine message with context if provided
        if context:
            context_text = self._context_to_text(context)
            full_text = f"{message} [Context: {context_text}]"
        else:
            full_text = message
            
        return self.embed_text(full_text)
    
    def _soil_data_to_text(self, soil_data: Dict[str, Any]) -> str:
        """
        Convert soil data dictionary to text representation.
        
        Args:
            soil_data: Soil parameters dictionary
            
        Returns:
            Text representation of soil data
        """
        text_parts = []
        
        # Standard soil parameters
        param_mapping = {
            'soil_ph': 'pH',
            'soil_moisture': 'moisture',
            'soil_temperature': 'temperature',
            'soil_ec': 'electrical conductivity',
            'soil_nitrogen': 'nitrogen',
            'soil_phosphorus': 'phosphorus',
            'soil_potassium': 'potassium'
        }
        
        for key, value in soil_data.items():
            if key in param_mapping and value is not None:
                param_name = param_mapping[key]
                text_parts.append(f"{param_name}: {value}")
        
        # Add location if available
        if 'location' in soil_data:
            location = soil_data['location']
            if isinstance(location, dict):
                lat = location.get('latitude', location.get('lat'))
                lon = location.get('longitude', location.get('lon'))
                if lat and lon:
                    text_parts.append(f"location: {lat}, {lon}")
        
        # Add timestamp if available
        if 'timestamp' in soil_data:
            text_parts.append(f"time: {soil_data['timestamp']}")
            
        return "Soil conditions: " + ", ".join(text_parts)
    
    def _context_to_text(self, context: Dict[str, Any]) -> str:
        """
        Convert context dictionary to text representation.
        
        Args:
            context: Context information dictionary
            
        Returns:
            Text representation of context
        """
        text_parts = []
        
        # Add estate information
        if 'estate_name' in context:
            text_parts.append(f"estate: {context['estate_name']}")
            
        if 'block_name' in context:
            text_parts.append(f"block: {context['block_name']}")
            
        # Add user role
        if 'user_role' in context:
            text_parts.append(f"user role: {context['user_role']}")
            
        return ", ".join(text_parts)
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compute cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score (0-1)
        """
        # Ensure embeddings are normalized
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return np.dot(embedding1, embedding2) / (norm1 * norm2)
    
    def get_embedding_dimension(self) -> int:
        """Get the embedding dimension."""
        return self.embedding_dim
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
