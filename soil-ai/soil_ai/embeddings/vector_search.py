"""
Vector similarity search service using pgvector database.

This module provides semantic search capabilities for soil data,
chat messages, and other content using vector embeddings.
"""

import logging
import asyncio
import numpy as np
import json
from typing import List, Dict, Any, Optional, Tuple, Union
import asyncpg
from dataclasses import dataclass

from .bge_embedder import BGEEmbedder

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """Search result with content and similarity score."""
    content_id: str
    content_type: str
    content_text: str
    similarity_score: float
    metadata: Dict[str, Any]


class VectorSearchService:
    """
    Vector similarity search service using pgvector.
    
    This service provides semantic search capabilities for:
    - Soil data and predictions
    - Chat messages and conversations
    - General content embeddings
    """
    
    def __init__(
        self,
        database_url: str,
        embedder: Optional[BGEEmbedder] = None,
        similarity_threshold: float = 0.7
    ):
        """
        Initialize vector search service.
        
        Args:
            database_url: PostgreSQL database URL
            embedder: BGE embedder instance (creates new if None)
            similarity_threshold: Minimum similarity score for results
        """
        self.database_url = database_url
        self.similarity_threshold = similarity_threshold
        
        # Initialize embedder
        if embedder is None:
            self.embedder = BGEEmbedder()
        else:
            self.embedder = embedder
            
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize database connection pool."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=10,
                command_timeout=30
            )
            logger.info("Vector search service initialized")
        except Exception as e:
            logger.error(f"Failed to initialize vector search service: {e}")
            raise
    
    async def close(self) -> None:
        """Close database connections."""
        if self.connection_pool:
            await self.connection_pool.close()
            
    async def search_similar_content(
        self,
        query_text: str,
        content_types: Optional[List[str]] = None,
        limit: int = 10,
        similarity_threshold: Optional[float] = None
    ) -> List[SearchResult]:
        """
        Search for similar content using semantic similarity.
        
        Args:
            query_text: Text to search for
            content_types: Filter by content types
            limit: Maximum number of results
            similarity_threshold: Override default similarity threshold
            
        Returns:
            List of search results ordered by similarity
        """
        if not self.connection_pool:
            await self.initialize()
            
        # Generate query embedding
        query_embedding = await self.embedder.embed_text_async(query_text)
        
        # Use provided threshold or default
        threshold = similarity_threshold or self.similarity_threshold
        
        # Build query
        query = """
        SELECT 
            content_id,
            content_type,
            content_text,
            1 - (embedding <=> $1) as similarity_score,
            metadata
        FROM vector_embeddings
        WHERE 1 - (embedding <=> $1) >= $2
        """
        
        params = [query_embedding.tolist(), threshold]
        
        # Add content type filter if specified
        if content_types:
            placeholders = ','.join(f'${i+3}' for i in range(len(content_types)))
            query += f" AND content_type = ANY(ARRAY[{placeholders}])"
            params.extend(content_types)
            
        query += " ORDER BY embedding <=> $1 LIMIT $" + str(len(params) + 1)
        params.append(limit)
        
        # Execute search
        async with self.connection_pool.acquire() as conn:
            # Convert embedding to string format for asyncpg
            params[0] = str(params[0])
            rows = await conn.fetch(query, *params)
            
        # Convert to search results
        results = []
        for row in rows:
            result = SearchResult(
                content_id=str(row['content_id']),
                content_type=row['content_type'],
                content_text=row['content_text'],
                similarity_score=float(row['similarity_score']),
                metadata=row['metadata'] or {}
            )
            results.append(result)
            
        logger.info(f"Found {len(results)} similar content items for query")
        return results
    
    async def search_similar_soil_data(
        self,
        soil_conditions: Dict[str, Any],
        limit: int = 10
    ) -> List[SearchResult]:
        """
        Search for similar soil conditions.
        
        Args:
            soil_conditions: Soil parameters to search for
            limit: Maximum number of results
            
        Returns:
            List of similar soil data results
        """
        # Generate embedding for soil conditions
        soil_embedding = self.embedder.embed_soil_data(soil_conditions)
        
        # Search in vector embeddings table
        query = """
        SELECT 
            content_id,
            content_type,
            content_text,
            1 - (embedding <=> $1) as similarity_score,
            metadata
        FROM vector_embeddings
        WHERE content_type IN ('sensor_data', 'prediction', 'soil_analysis')
        AND 1 - (embedding <=> $1) >= $2
        ORDER BY embedding <=> $1
        LIMIT $3
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(
                query,
                str(soil_embedding.tolist()),
                self.similarity_threshold,
                limit
            )
            
        results = []
        for row in rows:
            result = SearchResult(
                content_id=str(row['content_id']),
                content_type=row['content_type'],
                content_text=row['content_text'],
                similarity_score=float(row['similarity_score']),
                metadata=row['metadata'] or {}
            )
            results.append(result)
            
        return results
    
    async def search_chat_history(
        self,
        user_id: str,
        query_text: str,
        limit: int = 5
    ) -> List[SearchResult]:
        """
        Search user's chat history for relevant messages.
        
        Args:
            user_id: User ID to search for
            query_text: Query text
            limit: Maximum number of results
            
        Returns:
            List of relevant chat messages
        """
        # Generate query embedding
        query_embedding = await self.embedder.embed_text_async(query_text)
        
        # Search in chat messages
        query = """
        SELECT 
            cm.id::text as content_id,
            'chat_message' as content_type,
            cm.message as content_text,
            1 - (cm.embedding <=> $1) as similarity_score,
            jsonb_build_object(
                'user_id', cm.user_id,
                'session_id', cm.session_id,
                'message_type', cm.message_type,
                'created_at', cm.created_at
            ) as metadata
        FROM chat_messages cm
        WHERE cm.user_id = $2
        AND cm.embedding IS NOT NULL
        AND 1 - (cm.embedding <=> $1) >= $3
        ORDER BY cm.embedding <=> $1
        LIMIT $4
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(
                query,
                str(query_embedding.tolist()),
                user_id,
                self.similarity_threshold,
                limit
            )
            
        results = []
        for row in rows:
            result = SearchResult(
                content_id=row['content_id'],
                content_type=row['content_type'],
                content_text=row['content_text'],
                similarity_score=float(row['similarity_score']),
                metadata=row['metadata'] or {}
            )
            results.append(result)
            
        return results
    
    async def add_content_embedding(
        self,
        content_id: str,
        content_type: str,
        content_text: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add content embedding to the vector database.
        
        Args:
            content_id: Unique content identifier
            content_type: Type of content
            content_text: Text content to embed
            metadata: Optional metadata
        """
        if not self.connection_pool:
            await self.initialize()
            
        # Generate embedding
        embedding = await self.embedder.embed_text_async(content_text)
        
        # Insert into database
        query = """
        INSERT INTO vector_embeddings (content_id, content_type, content_text, embedding, metadata)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT ON CONSTRAINT vector_embeddings_content_type_content_id_key
        DO UPDATE SET
            content_text = EXCLUDED.content_text,
            embedding = EXCLUDED.embedding,
            metadata = EXCLUDED.metadata,
            updated_at = NOW()
        """
        
        async with self.connection_pool.acquire() as conn:
            await conn.execute(
                query,
                content_id,
                content_type,
                content_text,
                str(embedding.tolist()),
                json.dumps(metadata or {})
            )
            
        logger.debug(f"Added embedding for {content_type}:{content_id}")
    
    async def update_chat_message_embedding(
        self,
        message_id: str,
        message_text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Update chat message embedding.
        
        Args:
            message_id: Chat message ID
            message_text: Message text
            context: Optional context information
        """
        if not self.connection_pool:
            await self.initialize()
            
        # Generate embedding with context
        embedding = self.embedder.embed_chat_message(message_text, context)
        
        # Update chat message
        query = """
        UPDATE chat_messages 
        SET embedding = $1
        WHERE id = $2
        """
        
        async with self.connection_pool.acquire() as conn:
            await conn.execute(query, str(embedding.tolist()), message_id)
            
        logger.debug(f"Updated embedding for chat message {message_id}")
    
    async def get_content_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about embedded content.
        
        Returns:
            Dictionary with content statistics
        """
        if not self.connection_pool:
            await self.initialize()
            
        query = """
        SELECT 
            content_type,
            COUNT(*) as count,
            AVG(array_length(embedding, 1)) as avg_dimension
        FROM vector_embeddings
        WHERE embedding IS NOT NULL
        GROUP BY content_type
        ORDER BY count DESC
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
        stats = {
            'total_embeddings': sum(row['count'] for row in rows),
            'by_content_type': {
                row['content_type']: {
                    'count': row['count'],
                    'avg_dimension': int(row['avg_dimension']) if row['avg_dimension'] else 0
                }
                for row in rows
            }
        }
        
        return stats
