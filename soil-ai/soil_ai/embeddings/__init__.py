"""
Embedding generation and vector search functionality for soil-ai system.

This module provides:
- bge-large-en embedding model integration
- Vector similarity search
- Semantic search capabilities
- Integration with pgvector database
"""

from .bge_embedder import BGEEmbedder
from .vector_search import VectorSearchService
from .embedding_pipeline import EmbeddingPipeline

__all__ = [
    "BGEEmbedder",
    "VectorSearchService", 
    "EmbeddingPipeline"
]
