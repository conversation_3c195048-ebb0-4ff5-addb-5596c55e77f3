"""
Embedding generation pipeline for periodic processing.

This module implements the periodic embedding strategy as specified in PRD.md
for optimal performance-to-accuracy ratio.
"""

import logging
import asyncio
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import asyncpg
from dataclasses import dataclass

from .bge_embedder import BGEEmbedder
from .vector_search import VectorSearchService

logger = logging.getLogger(__name__)


@dataclass
class EmbeddingTask:
    """Embedding generation task."""
    content_id: str
    content_type: str
    content_text: str
    metadata: Dict[str, Any]
    priority: int = 1  # 1=high, 2=medium, 3=low


class EmbeddingPipeline:
    """
    Periodic embedding generation pipeline.
    
    This pipeline implements the embedding strategy from PRD.md:
    - Run periodically (hourly or post-ingest)
    - Process new content for vector search
    - Maintain embedding freshness
    """
    
    def __init__(
        self,
        database_url: str,
        embedder: Optional[BGEEmbedder] = None,
        vector_service: Optional[VectorSearchService] = None,
        batch_size: int = 100,
        processing_interval: int = 3600  # 1 hour in seconds
    ):
        """
        Initialize embedding pipeline.
        
        Args:
            database_url: PostgreSQL database URL
            embedder: BGE embedder instance
            vector_service: Vector search service
            batch_size: Number of items to process per batch
            processing_interval: Processing interval in seconds
        """
        self.database_url = database_url
        self.batch_size = batch_size
        self.processing_interval = processing_interval
        
        # Initialize services
        if embedder is None:
            self.embedder = BGEEmbedder()
        else:
            self.embedder = embedder
            
        if vector_service is None:
            self.vector_service = VectorSearchService(database_url, self.embedder)
        else:
            self.vector_service = vector_service
            
        self.connection_pool = None
        self.is_running = False
        
    async def initialize(self) -> None:
        """Initialize the pipeline."""
        try:
            # Initialize database connection
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=5,
                command_timeout=60
            )
            
            # Initialize vector service
            await self.vector_service.initialize()
            
            logger.info("Embedding pipeline initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding pipeline: {e}")
            raise
    
    async def close(self) -> None:
        """Close pipeline resources."""
        self.is_running = False
        
        if self.connection_pool:
            await self.connection_pool.close()
            
        await self.vector_service.close()
        
    async def start_periodic_processing(self) -> None:
        """Start periodic embedding processing."""
        if not self.connection_pool:
            await self.initialize()
            
        self.is_running = True
        logger.info(f"Starting periodic embedding processing (interval: {self.processing_interval}s)")
        
        while self.is_running:
            try:
                await self.process_pending_embeddings()
                await asyncio.sleep(self.processing_interval)
                
            except Exception as e:
                logger.error(f"Error in periodic processing: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def process_pending_embeddings(self) -> None:
        """Process all pending embedding tasks."""
        logger.info("Processing pending embeddings")
        
        # Get pending tasks
        tasks = await self._get_pending_tasks()
        
        if not tasks:
            logger.debug("No pending embedding tasks")
            return
            
        logger.info(f"Found {len(tasks)} pending embedding tasks")
        
        # Process in batches
        for i in range(0, len(tasks), self.batch_size):
            batch = tasks[i:i + self.batch_size]
            await self._process_batch(batch)
            
        logger.info("Completed pending embedding processing")
    
    async def _get_pending_tasks(self) -> List[EmbeddingTask]:
        """Get pending embedding tasks from database."""
        tasks = []
        
        # Get new sensor readings
        sensor_tasks = await self._get_sensor_reading_tasks()
        tasks.extend(sensor_tasks)
        
        # Get new predictions
        prediction_tasks = await self._get_prediction_tasks()
        tasks.extend(prediction_tasks)
        
        # Get new chat messages
        chat_tasks = await self._get_chat_message_tasks()
        tasks.extend(chat_tasks)
        
        # Sort by priority
        tasks.sort(key=lambda x: x.priority)
        
        return tasks
    
    async def _get_sensor_reading_tasks(self) -> List[EmbeddingTask]:
        """Get sensor readings that need embeddings."""
        query = """
        SELECT 
            sr.sensor_id::text || '_' || extract(epoch from sr.time)::text as content_id,
            'sensor_data' as content_type,
            s.device_id,
            sr.soil_ph,
            sr.soil_moisture,
            sr.soil_temperature,
            sr.soil_ec,
            sr.soil_nitrogen,
            sr.soil_phosphorus,
            sr.soil_potassium,
            sr.time,
            ST_X(s.location) as longitude,
            ST_Y(s.location) as latitude
        FROM sensor_readings sr
        JOIN sensors s ON sr.sensor_id = s.id
        LEFT JOIN vector_embeddings ve ON ve.content_id = sr.sensor_id::text || '_' || extract(epoch from sr.time)::text
            AND ve.content_type = 'sensor_data'
        WHERE ve.id IS NULL
        AND sr.time > NOW() - INTERVAL '24 hours'
        ORDER BY sr.time DESC
        LIMIT $1
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, self.batch_size)
            
        tasks = []
        for row in rows:
            # Create text representation
            soil_data = {
                'soil_ph': row['soil_ph'],
                'soil_moisture': row['soil_moisture'],
                'soil_temperature': row['soil_temperature'],
                'soil_ec': row['soil_ec'],
                'soil_nitrogen': row['soil_nitrogen'],
                'soil_phosphorus': row['soil_phosphorus'],
                'soil_potassium': row['soil_potassium'],
                'location': {'latitude': row['latitude'], 'longitude': row['longitude']},
                'timestamp': row['time']
            }
            
            content_text = self.embedder._soil_data_to_text(soil_data)
            
            task = EmbeddingTask(
                content_id=row['content_id'],
                content_type=row['content_type'],
                content_text=content_text,
                metadata={
                    'device_id': row['device_id'],
                    'timestamp': row['time'].isoformat(),
                    'location': {'latitude': row['latitude'], 'longitude': row['longitude']}
                },
                priority=1  # High priority for sensor data
            )
            tasks.append(task)
            
        return tasks
    
    async def _get_prediction_tasks(self) -> List[EmbeddingTask]:
        """Get predictions that need embeddings."""
        query = """
        SELECT 
            p.id::text as content_id,
            'prediction' as content_type,
            p.predicted_parameters,
            p.confidence_scores,
            p.model_version,
            p.prediction_method,
            p.prediction_timestamp,
            ST_X(p.location) as longitude,
            ST_Y(p.location) as latitude
        FROM predictions p
        LEFT JOIN vector_embeddings ve ON ve.content_id = p.id::text
            AND ve.content_type = 'prediction'
        WHERE ve.id IS NULL
        AND p.prediction_timestamp > NOW() - INTERVAL '24 hours'
        ORDER BY p.prediction_timestamp DESC
        LIMIT $1
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, self.batch_size)
            
        tasks = []
        for row in rows:
            # Create text representation
            content_text = f"Soil prediction: {row['predicted_parameters']}, confidence: {row['confidence_scores']}, method: {row['prediction_method']}, model: {row['model_version']}"
            
            task = EmbeddingTask(
                content_id=row['content_id'],
                content_type=row['content_type'],
                content_text=content_text,
                metadata={
                    'model_version': row['model_version'],
                    'prediction_method': row['prediction_method'],
                    'timestamp': row['prediction_timestamp'].isoformat(),
                    'location': {'latitude': row['latitude'], 'longitude': row['longitude']}
                },
                priority=2  # Medium priority for predictions
            )
            tasks.append(task)
            
        return tasks
    
    async def _get_chat_message_tasks(self) -> List[EmbeddingTask]:
        """Get chat messages that need embeddings."""
        query = """
        SELECT 
            cm.id::text as content_id,
            'chat_message' as content_type,
            cm.message,
            cm.message_type,
            cm.created_at,
            cm.user_id
        FROM chat_messages cm
        WHERE cm.embedding IS NULL
        AND cm.created_at > NOW() - INTERVAL '24 hours'
        ORDER BY cm.created_at DESC
        LIMIT $1
        """
        
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, self.batch_size)
            
        tasks = []
        for row in rows:
            task = EmbeddingTask(
                content_id=row['content_id'],
                content_type=row['content_type'],
                content_text=row['message'],
                metadata={
                    'message_type': row['message_type'],
                    'user_id': str(row['user_id']),
                    'timestamp': row['created_at'].isoformat()
                },
                priority=3  # Low priority for chat messages
            )
            tasks.append(task)
            
        return tasks
    
    async def _process_batch(self, tasks: List[EmbeddingTask]) -> None:
        """Process a batch of embedding tasks."""
        logger.debug(f"Processing batch of {len(tasks)} embedding tasks")
        
        for task in tasks:
            try:
                if task.content_type == 'chat_message':
                    # Update chat message embedding directly
                    await self.vector_service.update_chat_message_embedding(
                        task.content_id,
                        task.content_text
                    )
                else:
                    # Add to vector embeddings table
                    await self.vector_service.add_content_embedding(
                        task.content_id,
                        task.content_type,
                        task.content_text,
                        task.metadata
                    )
                    
            except Exception as e:
                logger.error(f"Failed to process embedding task {task.content_id}: {e}")
                
        logger.debug(f"Completed batch processing")
    
    async def trigger_immediate_processing(self) -> None:
        """Trigger immediate embedding processing."""
        logger.info("Triggering immediate embedding processing")
        await self.process_pending_embeddings()
    
    def schedule_periodic_processing(self) -> None:
        """Schedule periodic processing using the schedule library."""
        # Schedule hourly processing
        schedule.every().hour.do(lambda: asyncio.create_task(self.process_pending_embeddings()))
        
        # Schedule daily cleanup
        schedule.every().day.at("02:00").do(lambda: asyncio.create_task(self._cleanup_old_embeddings()))
        
        logger.info("Scheduled periodic embedding processing")
    
    async def _cleanup_old_embeddings(self) -> None:
        """Clean up old embeddings to maintain performance."""
        logger.info("Cleaning up old embeddings")
        
        # Remove embeddings older than 30 days for non-critical content
        query = """
        DELETE FROM vector_embeddings 
        WHERE content_type IN ('chat_message', 'temporary_analysis')
        AND created_at < NOW() - INTERVAL '30 days'
        """
        
        async with self.connection_pool.acquire() as conn:
            result = await conn.execute(query)
            
        logger.info(f"Cleaned up old embeddings: {result}")
    
    async def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline processing statistics."""
        stats = await self.vector_service.get_content_statistics()
        
        # Add pipeline-specific stats
        pending_tasks = await self._get_pending_tasks()
        stats['pending_tasks'] = len(pending_tasks)
        stats['is_running'] = self.is_running
        stats['processing_interval'] = self.processing_interval
        
        return stats
