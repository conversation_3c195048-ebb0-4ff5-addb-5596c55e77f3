"""
Soil-specific memory service for contextual AI interactions.

This service provides soil domain-specific memory functionality
for enhanced user interactions and recommendations.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncpg
from dataclasses import dataclass

from .mem0_adapter import Mem0Adapter
from ..embeddings.vector_search import VectorSearchService

logger = logging.getLogger(__name__)


@dataclass
class SoilContext:
    """Soil-specific context information."""
    estate_name: Optional[str] = None
    block_name: Optional[str] = None
    user_role: Optional[str] = None
    recent_readings: Optional[Dict[str, Any]] = None
    active_alerts: Optional[List[Dict[str, Any]]] = None
    current_season: Optional[str] = None


@dataclass
class SoilMemory:
    """Soil-specific memory entry."""
    id: str
    user_id: str
    memory_text: str
    memory_type: str  # 'observation', 'preference', 'insight', 'problem'
    soil_context: SoilContext
    relevance_score: float
    created_at: datetime
    updated_at: datetime


class SoilMemoryService:
    """
    Soil-specific memory service.
    
    This service provides contextual memory for soil-related interactions,
    storing user preferences, observations, and insights about soil management.
    """
    
    def __init__(
        self,
        database_url: str,
        mem0_adapter: Optional[Mem0Adapter] = None,
        vector_service: Optional[VectorSearchService] = None
    ):
        """
        Initialize soil memory service.
        
        Args:
            database_url: PostgreSQL database URL
            mem0_adapter: Mem0 adapter instance
            vector_service: Vector search service
        """
        self.database_url = database_url
        
        # Initialize services
        if mem0_adapter is None:
            self.mem0_adapter = Mem0Adapter(database_url)
        else:
            self.mem0_adapter = mem0_adapter
            
        if vector_service is None:
            self.vector_service = VectorSearchService(database_url)
        else:
            self.vector_service = vector_service
            
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize the service."""
        try:
            # Initialize database connection
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=5,
                command_timeout=30
            )
            
            # Initialize vector service
            await self.vector_service.initialize()
            
            logger.info("Soil memory service initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize soil memory service: {e}")
            raise
    
    async def close(self) -> None:
        """Close the service."""
        if self.connection_pool:
            await self.connection_pool.close()
            
        await self.vector_service.close()
    
    async def add_soil_observation(
        self,
        user_id: str,
        observation: str,
        soil_context: SoilContext,
        observation_type: str = "observation"
    ) -> str:
        """
        Add a soil observation to memory.
        
        Args:
            user_id: User identifier
            observation: Observation text
            soil_context: Soil context information
            observation_type: Type of observation
            
        Returns:
            Memory ID
        """
        try:
            # Create contextual memory text
            context_text = self._build_context_text(observation, soil_context)
            
            # Add to Mem0
            memory_id = await self.mem0_adapter.add_memory(
                context_text,
                user_id,
                metadata={
                    "type": observation_type,
                    "estate": soil_context.estate_name,
                    "block": soil_context.block_name,
                    "user_role": soil_context.user_role,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Store in our soil-specific memory table
            await self._store_soil_memory(
                memory_id,
                user_id,
                observation,
                observation_type,
                soil_context
            )
            
            logger.debug(f"Added soil observation {memory_id} for user {user_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"Failed to add soil observation: {e}")
            raise
    
    async def add_user_preference(
        self,
        user_id: str,
        preference: str,
        soil_context: SoilContext
    ) -> str:
        """
        Add user preference to memory.
        
        Args:
            user_id: User identifier
            preference: Preference description
            soil_context: Soil context
            
        Returns:
            Memory ID
        """
        return await self.add_soil_observation(
            user_id,
            f"User preference: {preference}",
            soil_context,
            "preference"
        )
    
    async def add_soil_insight(
        self,
        user_id: str,
        insight: str,
        soil_context: SoilContext
    ) -> str:
        """
        Add soil management insight to memory.
        
        Args:
            user_id: User identifier
            insight: Insight description
            soil_context: Soil context
            
        Returns:
            Memory ID
        """
        return await self.add_soil_observation(
            user_id,
            f"Soil insight: {insight}",
            soil_context,
            "insight"
        )
    
    async def get_relevant_memories(
        self,
        user_id: str,
        query: str,
        soil_context: SoilContext,
        limit: int = 5
    ) -> List[SoilMemory]:
        """
        Get relevant memories for a query and context.
        
        Args:
            user_id: User identifier
            query: Search query
            soil_context: Current soil context
            limit: Maximum number of results
            
        Returns:
            List of relevant soil memories
        """
        try:
            # Build contextual query
            contextual_query = self._build_contextual_query(query, soil_context)
            
            # Search memories using Mem0
            mem0_results = await self.mem0_adapter.search_memories(
                contextual_query,
                user_id,
                limit
            )
            
            # Convert to soil memories
            soil_memories = []
            for result in mem0_results:
                soil_memory = await self._convert_to_soil_memory(result, user_id)
                if soil_memory:
                    soil_memories.append(soil_memory)
            
            # Sort by relevance and context match
            soil_memories.sort(
                key=lambda m: self._calculate_context_relevance(m, soil_context),
                reverse=True
            )
            
            return soil_memories[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get relevant memories: {e}")
            return []
    
    async def get_estate_memories(
        self,
        user_id: str,
        estate_name: str,
        memory_types: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[SoilMemory]:
        """
        Get memories specific to an estate.
        
        Args:
            user_id: User identifier
            estate_name: Estate name
            memory_types: Filter by memory types
            limit: Maximum number of results
            
        Returns:
            List of estate-specific memories
        """
        try:
            query = """
            SELECT 
                memory_id,
                memory_text,
                memory_type,
                soil_context,
                relevance_score,
                created_at,
                updated_at
            FROM mem0_memories
            WHERE user_id = $1
            AND (metadata->>'estate' = $2 OR soil_context->>'estate_name' = $2)
            """
            
            params = [user_id, estate_name]
            
            if memory_types:
                placeholders = ','.join(f'${i+3}' for i in range(len(memory_types)))
                query += f" AND memory_type = ANY(ARRAY[{placeholders}])"
                params.extend(memory_types)
                
            query += " ORDER BY created_at DESC LIMIT $" + str(len(params) + 1)
            params.append(limit)
            
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                
            memories = []
            for row in rows:
                memory = self._row_to_soil_memory(row, user_id)
                memories.append(memory)
                
            return memories
            
        except Exception as e:
            logger.error(f"Failed to get estate memories: {e}")
            return []
    
    async def update_memory_relevance(
        self,
        memory_id: str,
        user_id: str,
        feedback_score: float
    ) -> bool:
        """
        Update memory relevance based on user feedback.
        
        Args:
            memory_id: Memory identifier
            user_id: User identifier
            feedback_score: Feedback score (0.0 to 1.0)
            
        Returns:
            Success status
        """
        try:
            # Update relevance score in our table
            query = """
            UPDATE mem0_memories
            SET relevance_score = (relevance_score + $3) / 2,
                updated_at = NOW()
            WHERE memory_id = $1 AND user_id = $2
            """
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query, memory_id, user_id, feedback_score)
                
            return "UPDATE" in result
            
        except Exception as e:
            logger.error(f"Failed to update memory relevance: {e}")
            return False
    
    def _build_context_text(self, observation: str, soil_context: SoilContext) -> str:
        """Build contextual memory text."""
        context_parts = [observation]
        
        if soil_context.estate_name:
            context_parts.append(f"Estate: {soil_context.estate_name}")
            
        if soil_context.block_name:
            context_parts.append(f"Block: {soil_context.block_name}")
            
        if soil_context.user_role:
            context_parts.append(f"Role: {soil_context.user_role}")
            
        if soil_context.current_season:
            context_parts.append(f"Season: {soil_context.current_season}")
            
        return " | ".join(context_parts)
    
    def _build_contextual_query(self, query: str, soil_context: SoilContext) -> str:
        """Build contextual search query."""
        context_parts = [query]
        
        if soil_context.estate_name:
            context_parts.append(soil_context.estate_name)
            
        if soil_context.block_name:
            context_parts.append(soil_context.block_name)
            
        return " ".join(context_parts)
    
    async def _store_soil_memory(
        self,
        memory_id: str,
        user_id: str,
        memory_text: str,
        memory_type: str,
        soil_context: SoilContext
    ) -> None:
        """Store soil memory in our database."""
        try:
            query = """
            INSERT INTO mem0_memories (
                id, user_id, memory_text, metadata,
                relevance_score, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            ON CONFLICT (id) DO UPDATE SET
                memory_text = EXCLUDED.memory_text,
                metadata = EXCLUDED.metadata,
                updated_at = NOW()
            """

            # Convert soil context to JSON metadata
            metadata_json = {
                "memory_type": memory_type,
                "estate_name": soil_context.estate_name,
                "block_name": soil_context.block_name,
                "user_role": soil_context.user_role,
                "current_season": soil_context.current_season
            }

            async with self.connection_pool.acquire() as conn:
                await conn.execute(
                    query,
                    memory_id,
                    user_id,
                    memory_text,
                    metadata_json,
                    1.0  # Initial relevance score
                )
                
        except Exception as e:
            logger.error(f"Failed to store soil memory: {e}")
    
    async def _convert_to_soil_memory(self, mem0_result: Dict[str, Any], user_id: str) -> Optional[SoilMemory]:
        """Convert Mem0 result to SoilMemory."""
        try:
            # Get additional data from our table
            query = """
            SELECT memory_type, soil_context, relevance_score, created_at, updated_at
            FROM mem0_memories
            WHERE memory_id = $1 AND user_id = $2
            """
            
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow(query, mem0_result["id"], user_id)
                
            if not row:
                return None
                
            # Build soil context
            context_data = row["soil_context"] or {}
            soil_context = SoilContext(
                estate_name=context_data.get("estate_name"),
                block_name=context_data.get("block_name"),
                user_role=context_data.get("user_role"),
                current_season=context_data.get("current_season")
            )
            
            return SoilMemory(
                id=mem0_result["id"],
                user_id=user_id,
                memory_text=mem0_result["memory"],
                memory_type=row["memory_type"],
                soil_context=soil_context,
                relevance_score=float(row["relevance_score"]),
                created_at=row["created_at"],
                updated_at=row["updated_at"]
            )
            
        except Exception as e:
            logger.error(f"Failed to convert to soil memory: {e}")
            return None
    
    def _row_to_soil_memory(self, row: Any, user_id: str) -> SoilMemory:
        """Convert database row to SoilMemory."""
        context_data = row["soil_context"] or {}
        soil_context = SoilContext(
            estate_name=context_data.get("estate_name"),
            block_name=context_data.get("block_name"),
            user_role=context_data.get("user_role"),
            current_season=context_data.get("current_season")
        )
        
        return SoilMemory(
            id=row["memory_id"],
            user_id=user_id,
            memory_text=row["memory_text"],
            memory_type=row["memory_type"],
            soil_context=soil_context,
            relevance_score=float(row["relevance_score"]),
            created_at=row["created_at"],
            updated_at=row["updated_at"]
        )
    
    def _calculate_context_relevance(self, memory: SoilMemory, current_context: SoilContext) -> float:
        """Calculate context relevance score."""
        score = memory.relevance_score
        
        # Boost score for matching estate
        if (memory.soil_context.estate_name and current_context.estate_name and
            memory.soil_context.estate_name == current_context.estate_name):
            score += 0.2
            
        # Boost score for matching block
        if (memory.soil_context.block_name and current_context.block_name and
            memory.soil_context.block_name == current_context.block_name):
            score += 0.3
            
        # Boost score for matching role
        if (memory.soil_context.user_role and current_context.user_role and
            memory.soil_context.user_role == current_context.user_role):
            score += 0.1
            
        return min(score, 1.0)
