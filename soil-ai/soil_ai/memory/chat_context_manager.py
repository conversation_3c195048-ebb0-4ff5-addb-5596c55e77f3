"""
Chat context manager for soil-ai conversations.

This module manages chat context, session state, and conversation
continuity for the soil-ai system using Mem0 AI integration.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import uuid
import json
import asyncpg

from .soil_memory_service import SoilMemoryService
from ..embeddings.vector_search import VectorSearchService

logger = logging.getLogger(__name__)


class ChatContextManager:
    """
    Chat context manager for soil-ai conversations.
    
    This manager provides:
    - Session management and continuity
    - Context-aware conversation handling
    - Memory integration for personalized responses
    - Estate and block context tracking
    """
    
    def __init__(
        self,
        database_url: str,
        memory_service: Optional[SoilMemoryService] = None,
        vector_service: Optional[VectorSearchService] = None
    ):
        """
        Initialize chat context manager.
        
        Args:
            database_url: PostgreSQL database URL
            memory_service: Soil memory service instance
            vector_service: Vector search service instance
        """
        self.database_url = database_url
        
        # Initialize services
        if memory_service is None:
            self.memory_service = SoilMemoryService(database_url)
        else:
            self.memory_service = memory_service
            
        if vector_service is None:
            self.vector_service = VectorSearchService(database_url)
        else:
            self.vector_service = vector_service
            
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize the context manager."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=5,
                command_timeout=30
            )
            
            await self.memory_service.initialize()
            await self.vector_service.initialize()
            
            logger.info("Chat context manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize chat context manager: {e}")
            raise
    
    async def close(self) -> None:
        """Close the context manager."""
        if self.connection_pool:
            await self.connection_pool.close()
            
        await self.memory_service.close()
        await self.vector_service.close()
    
    async def create_chat_session(
        self,
        user_id: str,
        estate_id: Optional[str] = None,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new chat session.
        
        Args:
            user_id: User identifier
            estate_id: Optional estate context
            initial_context: Initial context data
            
        Returns:
            Session ID
        """
        if not self.connection_pool:
            await self.initialize()
            
        session_id = str(uuid.uuid4())
        
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO chat_sessions (
                        user_id, session_id, estate_id, context_data, is_active
                    ) VALUES ($1, $2, $3, $4, $5)
                """, user_id, session_id, estate_id, json.dumps(initial_context or {}), True)
            
            logger.debug(f"Created chat session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create chat session: {e}")
            raise
    
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """
        Get context for a chat session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session context
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT 
                        cs.user_id,
                        cs.estate_id,
                        cs.context_data,
                        cs.last_activity,
                        e.name as estate_name,
                        u.email as user_email,
                        u.role as user_role
                    FROM chat_sessions cs
                    LEFT JOIN estates e ON cs.estate_id = e.id
                    LEFT JOIN users u ON cs.user_id = u.id
                    WHERE cs.session_id = $1 AND cs.is_active = true
                """, session_id)
            
            if not row:
                return {}
            
            context = {
                'session_id': session_id,
                'user_id': str(row['user_id']),
                'estate_id': str(row['estate_id']) if row['estate_id'] else None,
                'estate_name': row['estate_name'],
                'user_email': row['user_email'],
                'user_role': row['user_role'],
                'context_data': row['context_data'] or {},
                'last_activity': row['last_activity'].isoformat() if row['last_activity'] else None
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get session context: {e}")
            return {}
    
    async def add_message_to_session(
        self,
        session_id: str,
        message: str,
        message_type: str = 'user',
        response: Optional[str] = None
    ) -> str:
        """
        Add a message to a chat session.
        
        Args:
            session_id: Session identifier
            message: Message content
            message_type: Type of message ('user', 'assistant', 'system')
            response: Optional response content
            
        Returns:
            Message ID
        """
        if not self.connection_pool:
            await self.initialize()
            
        # Get session context
        session_context = await self.get_session_context(session_id)
        if not session_context:
            raise ValueError(f"Session {session_id} not found")
        
        user_id = session_context['user_id']
        message_id = str(uuid.uuid4())
        
        try:
            # Generate embedding for the message
            context_info = {
                'estate_name': session_context.get('estate_name'),
                'user_role': session_context.get('user_role')
            }
            
            # Add message to database
            async with self.connection_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO chat_messages (
                        id, session_id, user_id, message, response, message_type, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """, message_id, session_id, user_id, message, response, message_type, json.dumps(context_info))
                
                # Update session activity
                await conn.execute("""
                    UPDATE chat_sessions 
                    SET last_activity = NOW()
                    WHERE session_id = $1
                """, session_id)
            
            # Update message embedding asynchronously
            await self.vector_service.update_chat_message_embedding(
                message_id, message, context_info
            )
            
            logger.debug(f"Added message {message_id} to session {session_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to add message to session: {e}")
            raise
    
    async def get_conversation_context(
        self,
        session_id: str,
        message_limit: int = 10
    ) -> Dict[str, Any]:
        """
        Get conversation context for AI response generation.
        
        Args:
            session_id: Session identifier
            message_limit: Maximum number of recent messages
            
        Returns:
            Conversation context
        """
        if not self.connection_pool:
            await self.initialize()
            
        # Get session context
        session_context = await self.get_session_context(session_id)
        if not session_context:
            return {}
        
        user_id = session_context['user_id']
        
        try:
            # Get recent messages
            async with self.connection_pool.acquire() as conn:
                messages = await conn.fetch("""
                    SELECT message, response, message_type, created_at
                    FROM chat_messages
                    WHERE session_id = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                """, session_id, message_limit)
            
            # Reverse to get chronological order
            messages = list(reversed(messages))
            
            # Get user preferences
            user_preferences = await self.memory_service.get_user_preferences(user_id)
            
            # Get farm context if estate is available
            farm_context = {}
            if session_context.get('estate_name'):
                farm_context = await self.memory_service.get_farm_context(
                    user_id, session_context['estate_name']
                )
            
            # Build conversation context
            conversation_context = {
                'session': session_context,
                'recent_messages': [
                    {
                        'message': msg['message'],
                        'response': msg['response'],
                        'type': msg['message_type'],
                        'timestamp': msg['created_at'].isoformat()
                    }
                    for msg in messages
                ],
                'user_preferences': user_preferences,
                'farm_context': farm_context,
                'message_count': len(messages)
            }
            
            return conversation_context
            
        except Exception as e:
            logger.error(f"Failed to get conversation context: {e}")
            return {}
    
    async def get_relevant_memories(
        self,
        session_id: str,
        current_message: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get relevant memories for the current conversation.
        
        Args:
            session_id: Session identifier
            current_message: Current user message
            limit: Maximum number of memories
            
        Returns:
            List of relevant memories
        """
        session_context = await self.get_session_context(session_id)
        if not session_context:
            return []
        
        user_id = session_context['user_id']
        
        # Search for relevant memories
        memories = await self.memory_service.get_relevant_context(
            user_id, current_message, limit=limit
        )
        
        # Also search chat history
        chat_memories = await self.vector_service.search_chat_history(
            user_id, current_message, limit=3
        )
        
        # Combine and deduplicate
        all_memories = memories + [
            {
                'id': result.content_id,
                'memory': result.content_text,
                'score': result.similarity_score,
                'metadata': result.metadata
            }
            for result in chat_memories
        ]
        
        # Sort by relevance score
        all_memories.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return all_memories[:limit]
    
    async def update_session_context(
        self,
        session_id: str,
        context_updates: Dict[str, Any]
    ) -> bool:
        """
        Update session context data.
        
        Args:
            session_id: Session identifier
            context_updates: Context updates
            
        Returns:
            True if updated successfully
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                # Get current context
                current_context = await conn.fetchval("""
                    SELECT context_data FROM chat_sessions 
                    WHERE session_id = $1
                """, session_id)
                
                if current_context is None:
                    return False
                
                # Merge updates
                updated_context = {**(current_context or {}), **context_updates}
                
                # Update database
                await conn.execute("""
                    UPDATE chat_sessions
                    SET context_data = $1, last_activity = NOW()
                    WHERE session_id = $2
                """, json.dumps(updated_context), session_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update session context: {e}")
            return False
    
    async def close_session(self, session_id: str) -> bool:
        """
        Close a chat session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if closed successfully
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute("""
                    UPDATE chat_sessions 
                    SET is_active = false
                    WHERE session_id = $1
                """, session_id)
            
            success = result.split()[-1] == '1'
            if success:
                logger.debug(f"Closed chat session {session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to close session: {e}")
            return False
    
    async def cleanup_inactive_sessions(self, hours_threshold: int = 24) -> int:
        """
        Clean up inactive sessions.
        
        Args:
            hours_threshold: Hours of inactivity threshold
            
        Returns:
            Number of sessions cleaned up
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute("""
                    UPDATE chat_sessions 
                    SET is_active = false
                    WHERE is_active = true
                    AND last_activity < NOW() - INTERVAL '%s hours'
                """, hours_threshold)
            
            cleanup_count = int(result.split()[-1])
            logger.info(f"Cleaned up {cleanup_count} inactive sessions")
            
            return cleanup_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup inactive sessions: {e}")
            return 0
