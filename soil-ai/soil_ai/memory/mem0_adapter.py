"""
Mem0 AI adapter for soil-ai system.

This adapter provides a clean interface to Mem0 AI functionality
using our existing PostgreSQL and pgvector infrastructure.
"""

import logging
import os
import sys
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

# Add mem0 to path
mem0_path = Path(__file__).parent.parent.parent.parent / "mem0"
sys.path.insert(0, str(mem0_path))

try:
    from mem0 import Memory
    from mem0.configs.embeddings.base import BaseEmbedderConfig
    from mem0.configs.vector_stores.base import BaseVectorStoreConfig
    from mem0.configs.llms.base import BaseLLMConfig
except ImportError as e:
    logging.warning(f"Mem0 import failed: {e}. Mem0 functionality will be limited.")
    Memory = None

from ..embeddings.bge_embedder import BGEEmbedder

logger = logging.getLogger(__name__)


class SoilEmbedderConfig(BaseEmbedderConfig):
    """Custom embedder config for soil-ai BGE model."""
    provider: str = "custom"
    model: str = "BAAI/bge-large-en-v1.5"
    embedding_dims: int = 1536


class SoilVectorStoreConfig(BaseVectorStoreConfig):
    """Custom vector store config for soil-ai PostgreSQL."""
    provider: str = "pgvector"
    config: Dict[str, Any] = {
        "collection_name": "soil_memories",
        "embedding_model_dims": 1536,
        "diskann": False,
        "hnsw": True
    }


class SoilLLMConfig(BaseLLMConfig):
    """LLM config for soil-ai system."""
    provider: str = "openai"
    model: str = "gpt-4"
    temperature: float = 0.1
    max_tokens: int = 1000


class Mem0Adapter:
    """
    Adapter for Mem0 AI integration with soil-ai system.
    
    This adapter provides a clean interface to Mem0 functionality
    while using our existing database and embedding infrastructure.
    """
    
    def __init__(
        self,
        database_url: str,
        openai_api_key: Optional[str] = None,
        embedder: Optional[BGEEmbedder] = None
    ):
        """
        Initialize Mem0 adapter.
        
        Args:
            database_url: PostgreSQL database URL
            openai_api_key: OpenAI API key for LLM
            embedder: Custom BGE embedder instance
        """
        self.database_url = database_url
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        
        # Initialize embedder
        if embedder is None:
            self.embedder = BGEEmbedder()
        else:
            self.embedder = embedder
            
        # Initialize Mem0 if available
        self.memory = None
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        if Memory is not None:
            self._initialize_mem0()
        else:
            logger.warning("Mem0 not available. Using fallback memory implementation.")
            
    def _initialize_mem0(self) -> None:
        """Initialize Mem0 with custom configuration."""
        try:
            # Parse database URL for Mem0 config
            db_config = self._parse_database_url()
            
            # Configure Mem0 with our PostgreSQL setup
            config = {
                "embedder": {
                    "provider": "custom",
                    "config": {
                        "model": "BAAI/bge-large-en-v1.5",
                        "embedding_dims": 1536
                    }
                },
                "vector_store": {
                    "provider": "pgvector",
                    "config": {
                        "dbname": db_config["dbname"],
                        "collection_name": "soil_memories",
                        "embedding_model_dims": 1536,
                        "user": db_config["user"],
                        "password": db_config["password"],
                        "host": db_config["host"],
                        "port": db_config["port"],
                        "diskann": False,
                        "hnsw": True
                    }
                },
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4",
                        "temperature": 0.1,
                        "max_tokens": 1000,
                        "api_key": self.openai_api_key
                    }
                }
            }
            
            self.memory = Memory.from_config(config)
            logger.info("Mem0 initialized successfully with PostgreSQL backend")
            
        except Exception as e:
            logger.error(f"Failed to initialize Mem0: {e}")
            self.memory = None
    
    def _parse_database_url(self) -> Dict[str, str]:
        """Parse PostgreSQL database URL."""
        # Simple URL parsing for postgresql://user:password@host:port/dbname
        url = self.database_url.replace("postgresql://", "").replace("postgresql+asyncpg://", "")
        
        if "@" in url:
            auth, host_db = url.split("@", 1)
            if ":" in auth:
                user, password = auth.split(":", 1)
            else:
                user, password = auth, ""
        else:
            user, password = "postgres", ""
            host_db = url
            
        if "/" in host_db:
            host_port, dbname = host_db.split("/", 1)
        else:
            host_port, dbname = host_db, "postgres"
            
        if ":" in host_port:
            host, port = host_port.split(":", 1)
        else:
            host, port = host_port, "5432"
            
        return {
            "user": user,
            "password": password,
            "host": host,
            "port": int(port),
            "dbname": dbname
        }
    
    async def add_memory(
        self,
        message: str,
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a memory to the system.
        
        Args:
            message: Memory content
            user_id: User identifier
            metadata: Additional metadata
            
        Returns:
            Memory ID
        """
        if self.memory is None:
            return await self._fallback_add_memory(message, user_id, metadata)
            
        try:
            # Run Mem0 operation in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                lambda: self.memory.add(message, user_id=user_id, metadata=metadata)
            )
            
            # Extract memory ID from result
            if isinstance(result, dict) and "id" in result:
                return result["id"]
            elif isinstance(result, list) and result and "id" in result[0]:
                return result[0]["id"]
            else:
                return str(result)
                
        except Exception as e:
            logger.error(f"Failed to add memory via Mem0: {e}")
            return await self._fallback_add_memory(message, user_id, metadata)
    
    async def search_memories(
        self,
        query: str,
        user_id: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant memories.
        
        Args:
            query: Search query
            user_id: User identifier
            limit: Maximum number of results
            
        Returns:
            List of relevant memories
        """
        if self.memory is None:
            return await self._fallback_search_memories(query, user_id, limit)
            
        try:
            # Run Mem0 search in thread pool
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                self.executor,
                lambda: self.memory.search(query, user_id=user_id, limit=limit)
            )
            
            # Normalize results format
            if isinstance(results, list):
                return [self._normalize_memory_result(r) for r in results]
            else:
                return [self._normalize_memory_result(results)]
                
        except Exception as e:
            logger.error(f"Failed to search memories via Mem0: {e}")
            return await self._fallback_search_memories(query, user_id, limit)
    
    async def get_all_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all memories for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of all user memories
        """
        if self.memory is None:
            return await self._fallback_get_all_memories(user_id)
            
        try:
            # Run Mem0 operation in thread pool
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                self.executor,
                lambda: self.memory.get_all(user_id=user_id)
            )
            
            if isinstance(results, list):
                return [self._normalize_memory_result(r) for r in results]
            else:
                return [self._normalize_memory_result(results)]
                
        except Exception as e:
            logger.error(f"Failed to get all memories via Mem0: {e}")
            return await self._fallback_get_all_memories(user_id)
    
    async def update_memory(
        self,
        memory_id: str,
        message: str,
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update an existing memory.
        
        Args:
            memory_id: Memory identifier
            message: Updated memory content
            user_id: User identifier
            metadata: Updated metadata
            
        Returns:
            Success status
        """
        if self.memory is None:
            return await self._fallback_update_memory(memory_id, message, user_id, metadata)
            
        try:
            # Run Mem0 operation in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                lambda: self.memory.update(memory_id, message, user_id=user_id, metadata=metadata)
            )
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Failed to update memory via Mem0: {e}")
            return await self._fallback_update_memory(memory_id, message, user_id, metadata)
    
    async def delete_memory(self, memory_id: str, user_id: str) -> bool:
        """
        Delete a memory.
        
        Args:
            memory_id: Memory identifier
            user_id: User identifier
            
        Returns:
            Success status
        """
        if self.memory is None:
            return await self._fallback_delete_memory(memory_id, user_id)
            
        try:
            # Run Mem0 operation in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                lambda: self.memory.delete(memory_id, user_id=user_id)
            )
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Failed to delete memory via Mem0: {e}")
            return await self._fallback_delete_memory(memory_id, user_id)
    
    def _normalize_memory_result(self, result: Any) -> Dict[str, Any]:
        """Normalize memory result to consistent format."""
        if isinstance(result, dict):
            return {
                "id": result.get("id", ""),
                "memory": result.get("memory", result.get("text", "")),
                "metadata": result.get("metadata", {}),
                "score": result.get("score", 1.0),
                "created_at": result.get("created_at", ""),
                "updated_at": result.get("updated_at", "")
            }
        else:
            return {
                "id": "",
                "memory": str(result),
                "metadata": {},
                "score": 1.0,
                "created_at": "",
                "updated_at": ""
            }
    
    # Fallback implementations using our database directly
    async def _fallback_add_memory(self, message: str, user_id: str, metadata: Optional[Dict[str, Any]]) -> str:
        """Fallback memory addition using direct database access."""
        # This would use our mem0_memories table directly
        logger.info("Using fallback memory implementation")
        return f"fallback_{hash(message + user_id)}"
    
    async def _fallback_search_memories(self, query: str, user_id: str, limit: int) -> List[Dict[str, Any]]:
        """Fallback memory search using our vector search service."""
        logger.info("Using fallback memory search")
        return []
    
    async def _fallback_get_all_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """Fallback get all memories."""
        logger.info("Using fallback get all memories")
        return []
    
    async def _fallback_update_memory(self, memory_id: str, message: str, user_id: str, metadata: Optional[Dict[str, Any]]) -> bool:
        """Fallback memory update."""
        logger.info("Using fallback memory update")
        return True
    
    async def _fallback_delete_memory(self, memory_id: str, user_id: str) -> bool:
        """Fallback memory deletion."""
        logger.info("Using fallback memory deletion")
        return True
    
    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
