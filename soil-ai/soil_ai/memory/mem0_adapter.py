"""
Mem0 AI adapter for soil-ai system.

This adapter provides a clean interface to Mem0 AI functionality
using our existing PostgreSQL and pgvector infrastructure.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
import uuid
from datetime import datetime
import asyncpg

from ..embeddings.bge_embedder import BGEEmbedder

logger = logging.getLogger(__name__)


class Mem0Adapter:
    """
    Adapter for Mem0 AI integration with soil-ai system.
    
    This adapter provides Mem0-like functionality using our existing
    PostgreSQL database with pgvector for vector storage.
    """
    
    def __init__(
        self,
        database_url: str,
        embedder: Optional[BGEEmbedder] = None,
        collection_name: str = "soil_memories"
    ):
        """
        Initialize Mem0 adapter.
        
        Args:
            database_url: PostgreSQL database URL
            embedder: BGE embedder instance
            collection_name: Memory collection name
        """
        self.database_url = database_url
        self.collection_name = collection_name
        
        # Initialize embedder
        if embedder is None:
            self.embedder = BGEEmbedder()
        else:
            self.embedder = embedder
            
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize the adapter."""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=10,
                command_timeout=30
            )
            
            # Ensure memory table exists
            await self._ensure_memory_table()
            
            logger.info("Mem0 adapter initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Mem0 adapter: {e}")
            raise
    
    async def close(self) -> None:
        """Close the adapter."""
        if self.connection_pool:
            await self.connection_pool.close()
    
    async def _ensure_memory_table(self) -> None:
        """Ensure the memory table exists."""
        async with self.connection_pool.acquire() as conn:
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS mem0_memories (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    user_id UUID NOT NULL,
                    memory_text TEXT NOT NULL,
                    embedding vector(1536),
                    metadata JSONB DEFAULT '{}',
                    relevance_score DECIMAL(5,4) DEFAULT 1.0,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    updated_at TIMESTAMPTZ DEFAULT NOW()
                );
                
                CREATE INDEX IF NOT EXISTS idx_mem0_memories_user 
                ON mem0_memories(user_id);
                
                CREATE INDEX IF NOT EXISTS idx_mem0_memories_embedding 
                ON mem0_memories USING ivfflat (embedding vector_cosine_ops) 
                WITH (lists = 100);
            """)
    
    async def add_memory(
        self,
        user_id: str,
        memory_text: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a memory for a user.
        
        Args:
            user_id: User identifier
            memory_text: Memory content
            metadata: Optional metadata
            
        Returns:
            Memory ID
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            # Generate embedding
            embedding = await self.embedder.embed_text_async(memory_text)
            
            # Insert memory
            memory_id = str(uuid.uuid4())
            
            async with self.connection_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO mem0_memories (id, user_id, memory_text, embedding, metadata)
                    VALUES ($1, $2, $3, $4, $5)
                """, memory_id, user_id, memory_text, embedding.tolist(), metadata or {})
            
            logger.debug(f"Added memory {memory_id} for user {user_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"Failed to add memory: {e}")
            raise
    
    async def search_memories(
        self,
        user_id: str,
        query: str,
        limit: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search memories for a user.
        
        Args:
            user_id: User identifier
            query: Search query
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of relevant memories
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            # Generate query embedding
            query_embedding = await self.embedder.embed_text_async(query)
            
            # Search memories
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT 
                        id,
                        memory_text,
                        metadata,
                        relevance_score,
                        1 - (embedding <=> $1) as similarity,
                        created_at,
                        updated_at
                    FROM mem0_memories
                    WHERE user_id = $2
                    AND 1 - (embedding <=> $1) >= $3
                    ORDER BY embedding <=> $1
                    LIMIT $4
                """, query_embedding.tolist(), user_id, similarity_threshold, limit)
            
            # Convert to memory objects
            memories = []
            for row in rows:
                memory = {
                    'id': str(row['id']),
                    'memory': row['memory_text'],
                    'metadata': row['metadata'] or {},
                    'score': float(row['similarity']),
                    'relevance_score': float(row['relevance_score']),
                    'created_at': row['created_at'].isoformat(),
                    'updated_at': row['updated_at'].isoformat()
                }
                memories.append(memory)
            
            logger.debug(f"Found {len(memories)} memories for user {user_id}")
            return memories
            
        except Exception as e:
            logger.error(f"Failed to search memories: {e}")
            return []
    
    async def get_all_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all memories for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of all user memories
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT 
                        id,
                        memory_text,
                        metadata,
                        relevance_score,
                        created_at,
                        updated_at
                    FROM mem0_memories
                    WHERE user_id = $1
                    ORDER BY created_at DESC
                """, user_id)
            
            memories = []
            for row in rows:
                memory = {
                    'id': str(row['id']),
                    'memory': row['memory_text'],
                    'metadata': row['metadata'] or {},
                    'relevance_score': float(row['relevance_score']),
                    'created_at': row['created_at'].isoformat(),
                    'updated_at': row['updated_at'].isoformat()
                }
                memories.append(memory)
            
            return memories
            
        except Exception as e:
            logger.error(f"Failed to get all memories: {e}")
            return []
    
    async def update_memory(
        self,
        memory_id: str,
        memory_text: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update a memory.
        
        Args:
            memory_id: Memory identifier
            memory_text: New memory text
            metadata: New metadata
            
        Returns:
            True if updated successfully
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            updates = []
            params = []
            param_count = 1
            
            if memory_text is not None:
                # Generate new embedding
                embedding = await self.embedder.embed_text_async(memory_text)
                updates.append(f"memory_text = ${param_count}")
                params.append(memory_text)
                param_count += 1
                
                updates.append(f"embedding = ${param_count}")
                params.append(embedding.tolist())
                param_count += 1
            
            if metadata is not None:
                updates.append(f"metadata = ${param_count}")
                params.append(metadata)
                param_count += 1
            
            if not updates:
                return True
                
            updates.append(f"updated_at = NOW()")
            params.append(memory_id)
            
            query = f"""
                UPDATE mem0_memories 
                SET {', '.join(updates)}
                WHERE id = ${param_count}
            """
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query, *params)
            
            success = result.split()[-1] == '1'
            if success:
                logger.debug(f"Updated memory {memory_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to update memory: {e}")
            return False
    
    async def delete_memory(self, memory_id: str) -> bool:
        """
        Delete a memory.
        
        Args:
            memory_id: Memory identifier
            
        Returns:
            True if deleted successfully
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute("""
                    DELETE FROM mem0_memories WHERE id = $1
                """, memory_id)
            
            success = result.split()[-1] == '1'
            if success:
                logger.debug(f"Deleted memory {memory_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete memory: {e}")
            return False
    
    async def delete_all_memories(self, user_id: str) -> bool:
        """
        Delete all memories for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if deleted successfully
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute("""
                    DELETE FROM mem0_memories WHERE user_id = $1
                """, user_id)
            
            logger.debug(f"Deleted all memories for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete all memories: {e}")
            return False
    
    async def get_memory_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get memory statistics.
        
        Args:
            user_id: Optional user ID to filter by
            
        Returns:
            Memory statistics
        """
        if not self.connection_pool:
            await self.initialize()
            
        try:
            if user_id:
                async with self.connection_pool.acquire() as conn:
                    row = await conn.fetchrow("""
                        SELECT 
                            COUNT(*) as total_memories,
                            AVG(relevance_score) as avg_relevance,
                            MIN(created_at) as oldest_memory,
                            MAX(created_at) as newest_memory
                        FROM mem0_memories
                        WHERE user_id = $1
                    """, user_id)
            else:
                async with self.connection_pool.acquire() as conn:
                    row = await conn.fetchrow("""
                        SELECT 
                            COUNT(*) as total_memories,
                            COUNT(DISTINCT user_id) as total_users,
                            AVG(relevance_score) as avg_relevance,
                            MIN(created_at) as oldest_memory,
                            MAX(created_at) as newest_memory
                        FROM mem0_memories
                    """)
            
            stats = {
                'total_memories': row['total_memories'],
                'avg_relevance': float(row['avg_relevance']) if row['avg_relevance'] else 0.0,
                'oldest_memory': row['oldest_memory'].isoformat() if row['oldest_memory'] else None,
                'newest_memory': row['newest_memory'].isoformat() if row['newest_memory'] else None
            }
            
            if not user_id and 'total_users' in row:
                stats['total_users'] = row['total_users']
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {}
