"""
Chat memory service for contextual conversations.

This service manages chat history and contextual memory
for enhanced conversational AI interactions.
"""

import logging
import async<PERSON>
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncpg
from dataclasses import dataclass
import uuid

from .mem0_adapter import Mem0Adapter
from .soil_memory import SoilMemoryService, SoilContext
from ..embeddings.vector_search import VectorSearchService

logger = logging.getLogger(__name__)


@dataclass
class ChatMessage:
    """Chat message with context."""
    id: str
    session_id: str
    user_id: str
    message: str
    response: Optional[str]
    message_type: str  # 'user', 'assistant', 'system'
    context: Dict[str, Any]
    created_at: datetime


@dataclass
class ChatSession:
    """Chat session information."""
    id: str
    session_id: str
    user_id: str
    estate_id: Optional[str]
    context_data: Dict[str, Any]
    is_active: bool
    last_activity: datetime
    created_at: datetime


class ChatMemoryService:
    """
    Chat memory service for contextual conversations.
    
    This service manages chat sessions, message history,
    and contextual memory for enhanced AI interactions.
    """
    
    def __init__(
        self,
        database_url: str,
        mem0_adapter: Optional[Mem0Adapter] = None,
        soil_memory: Optional[SoilMemoryService] = None,
        vector_service: Optional[VectorSearchService] = None
    ):
        """
        Initialize chat memory service.
        
        Args:
            database_url: PostgreSQL database URL
            mem0_adapter: Mem0 adapter instance
            soil_memory: Soil memory service
            vector_service: Vector search service
        """
        self.database_url = database_url
        
        # Initialize services
        if mem0_adapter is None:
            self.mem0_adapter = Mem0Adapter(database_url)
        else:
            self.mem0_adapter = mem0_adapter
            
        if soil_memory is None:
            self.soil_memory = SoilMemoryService(database_url, mem0_adapter)
        else:
            self.soil_memory = soil_memory
            
        if vector_service is None:
            self.vector_service = VectorSearchService(database_url)
        else:
            self.vector_service = vector_service
            
        self.connection_pool = None
        
    async def initialize(self) -> None:
        """Initialize the service."""
        try:
            # Initialize database connection
            self.connection_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=2,
                max_size=5,
                command_timeout=30
            )
            
            # Initialize other services
            await self.soil_memory.initialize()
            await self.vector_service.initialize()
            
            logger.info("Chat memory service initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize chat memory service: {e}")
            raise
    
    async def close(self) -> None:
        """Close the service."""
        if self.connection_pool:
            await self.connection_pool.close()
            
        await self.soil_memory.close()
        await self.vector_service.close()
    
    async def create_chat_session(
        self,
        user_id: str,
        estate_id: Optional[str] = None,
        context_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new chat session.
        
        Args:
            user_id: User identifier
            estate_id: Optional estate identifier
            context_data: Optional context information
            
        Returns:
            Session ID
        """
        try:
            session_id = str(uuid.uuid4())
            
            query = """
            INSERT INTO chat_sessions (
                user_id, session_id, estate_id, context_data,
                is_active, last_activity, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, true, NOW(), NOW(), NOW())
            RETURNING id
            """
            
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow(
                    query,
                    user_id,
                    session_id,
                    estate_id,
                    context_data or {}
                )
                
            logger.debug(f"Created chat session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create chat session: {e}")
            raise
    
    async def add_chat_message(
        self,
        session_id: str,
        user_id: str,
        message: str,
        message_type: str = "user",
        response: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a chat message to the session.
        
        Args:
            session_id: Chat session ID
            user_id: User identifier
            message: Message content
            message_type: Type of message
            response: Optional response content
            context: Optional context information
            
        Returns:
            Message ID
        """
        try:
            message_id = str(uuid.uuid4())
            
            # Store message in database
            query = """
            INSERT INTO chat_messages (
                id, session_id, user_id, message, response,
                message_type, metadata, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
            RETURNING id
            """
            
            async with self.connection_pool.acquire() as conn:
                await conn.execute(
                    query,
                    message_id,
                    session_id,
                    user_id,
                    message,
                    response,
                    message_type,
                    context or {}
                )
                
                # Update session last activity
                await conn.execute(
                    "UPDATE chat_sessions SET last_activity = NOW() WHERE session_id = $1",
                    session_id
                )
            
            # Generate and store embedding asynchronously
            asyncio.create_task(
                self.vector_service.update_chat_message_embedding(
                    message_id, message, context
                )
            )
            
            logger.debug(f"Added chat message {message_id} to session {session_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to add chat message: {e}")
            raise
    
    async def get_chat_history(
        self,
        session_id: str,
        limit: int = 20,
        include_context: bool = True
    ) -> List[ChatMessage]:
        """
        Get chat history for a session.
        
        Args:
            session_id: Chat session ID
            limit: Maximum number of messages
            include_context: Include context information
            
        Returns:
            List of chat messages
        """
        try:
            query = """
            SELECT 
                id, session_id, user_id, message, response,
                message_type, metadata, created_at
            FROM chat_messages
            WHERE session_id = $1
            ORDER BY created_at DESC
            LIMIT $2
            """
            
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, session_id, limit)
                
            messages = []
            for row in rows:
                message = ChatMessage(
                    id=str(row['id']),
                    session_id=row['session_id'],
                    user_id=str(row['user_id']),
                    message=row['message'],
                    response=row['response'],
                    message_type=row['message_type'],
                    context=row['metadata'] or {},
                    created_at=row['created_at']
                )
                messages.append(message)
                
            # Reverse to get chronological order
            messages.reverse()
            return messages
            
        except Exception as e:
            logger.error(f"Failed to get chat history: {e}")
            return []
    
    async def get_contextual_memories(
        self,
        user_id: str,
        current_message: str,
        session_context: Dict[str, Any],
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get contextual memories for enhanced conversation.
        
        Args:
            user_id: User identifier
            current_message: Current message content
            session_context: Session context information
            limit: Maximum number of memories
            
        Returns:
            List of relevant memories
        """
        try:
            # Build soil context from session
            soil_context = SoilContext(
                estate_name=session_context.get('estate_name'),
                block_name=session_context.get('block_name'),
                user_role=session_context.get('user_role'),
                current_season=session_context.get('current_season')
            )
            
            # Get relevant soil memories
            soil_memories = await self.soil_memory.get_relevant_memories(
                user_id, current_message, soil_context, limit
            )
            
            # Get relevant chat history
            chat_memories = await self.vector_service.search_chat_history(
                user_id, current_message, limit
            )
            
            # Combine and format results
            all_memories = []
            
            # Add soil memories
            for memory in soil_memories:
                all_memories.append({
                    "type": "soil_memory",
                    "content": memory.memory_text,
                    "context": {
                        "estate": memory.soil_context.estate_name,
                        "block": memory.soil_context.block_name,
                        "memory_type": memory.memory_type
                    },
                    "relevance": memory.relevance_score,
                    "timestamp": memory.created_at.isoformat()
                })
            
            # Add chat memories
            for memory in chat_memories:
                all_memories.append({
                    "type": "chat_memory",
                    "content": memory.content_text,
                    "context": memory.metadata,
                    "relevance": memory.similarity_score,
                    "timestamp": memory.metadata.get('created_at', '')
                })
            
            # Sort by relevance
            all_memories.sort(key=lambda x: x['relevance'], reverse=True)
            
            return all_memories[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get contextual memories: {e}")
            return []
    
    async def update_session_context(
        self,
        session_id: str,
        context_updates: Dict[str, Any]
    ) -> bool:
        """
        Update session context information.
        
        Args:
            session_id: Chat session ID
            context_updates: Context updates to apply
            
        Returns:
            Success status
        """
        try:
            query = """
            UPDATE chat_sessions
            SET context_data = context_data || $2,
                updated_at = NOW()
            WHERE session_id = $1
            """
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query, session_id, context_updates)
                
            return "UPDATE" in result
            
        except Exception as e:
            logger.error(f"Failed to update session context: {e}")
            return False
    
    async def close_session(self, session_id: str) -> bool:
        """
        Close a chat session.
        
        Args:
            session_id: Chat session ID
            
        Returns:
            Success status
        """
        try:
            query = """
            UPDATE chat_sessions
            SET is_active = false,
                updated_at = NOW()
            WHERE session_id = $1
            """
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query, session_id)
                
            return "UPDATE" in result
            
        except Exception as e:
            logger.error(f"Failed to close session: {e}")
            return False
    
    async def get_active_sessions(self, user_id: str) -> List[ChatSession]:
        """
        Get active chat sessions for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of active chat sessions
        """
        try:
            query = """
            SELECT 
                id, session_id, user_id, estate_id, context_data,
                is_active, last_activity, created_at
            FROM chat_sessions
            WHERE user_id = $1 AND is_active = true
            ORDER BY last_activity DESC
            """
            
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, user_id)
                
            sessions = []
            for row in rows:
                session = ChatSession(
                    id=str(row['id']),
                    session_id=row['session_id'],
                    user_id=str(row['user_id']),
                    estate_id=str(row['estate_id']) if row['estate_id'] else None,
                    context_data=row['context_data'] or {},
                    is_active=row['is_active'],
                    last_activity=row['last_activity'],
                    created_at=row['created_at']
                )
                sessions.append(session)
                
            return sessions
            
        except Exception as e:
            logger.error(f"Failed to get active sessions: {e}")
            return []
    
    async def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """
        Clean up old inactive sessions.
        
        Args:
            days_old: Number of days to keep sessions
            
        Returns:
            Number of sessions cleaned up
        """
        try:
            query = """
            DELETE FROM chat_sessions
            WHERE is_active = false
            AND last_activity < NOW() - INTERVAL '%s days'
            """
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query % days_old)
                
            # Extract number from result like "DELETE 5"
            count = int(result.split()[-1]) if result.split() else 0
            
            logger.info(f"Cleaned up {count} old chat sessions")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {e}")
            return 0
