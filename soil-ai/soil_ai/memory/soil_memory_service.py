"""
Soil-specific memory service for contextual AI interactions.

This service provides soil domain-specific memory management
for chat interactions and user context retention.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncio

from .mem0_adapter import Mem0Adapter
from ..embeddings.bge_embedder import BGEEmbedder

logger = logging.getLogger(__name__)


class SoilMemoryService:
    """
    Soil-specific memory service for AI interactions.
    
    This service provides domain-specific memory management for:
    - User preferences and context
    - Farm-specific terminology and history
    - Soil management patterns and insights
    - Chat conversation context
    """
    
    def __init__(
        self,
        database_url: str,
        embedder: Optional[BGEEmbedder] = None
    ):
        """
        Initialize soil memory service.
        
        Args:
            database_url: PostgreSQL database URL
            embedder: BGE embedder instance
        """
        self.mem0_adapter = Mem0Adapter(database_url, embedder)
        
    async def initialize(self) -> None:
        """Initialize the service."""
        await self.mem0_adapter.initialize()
        logger.info("Soil memory service initialized")
        
    async def close(self) -> None:
        """Close the service."""
        await self.mem0_adapter.close()
    
    async def store_user_preference(
        self,
        user_id: str,
        preference_type: str,
        preference_value: Any,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Store user preference in memory.
        
        Args:
            user_id: User identifier
            preference_type: Type of preference (e.g., 'notification', 'units', 'role')
            preference_value: Preference value
            context: Optional context information
            
        Returns:
            Memory ID
        """
        memory_text = f"User prefers {preference_type}: {preference_value}"
        
        metadata = {
            'type': 'user_preference',
            'preference_type': preference_type,
            'preference_value': str(preference_value),
            'context': context or {}
        }
        
        return await self.mem0_adapter.add_memory(user_id, memory_text, metadata)
    
    async def store_farm_context(
        self,
        user_id: str,
        estate_name: str,
        farm_details: Dict[str, Any]
    ) -> str:
        """
        Store farm-specific context and terminology.
        
        Args:
            user_id: User identifier
            estate_name: Estate name
            farm_details: Farm details and context
            
        Returns:
            Memory ID
        """
        # Create descriptive memory text
        details_text = []
        for key, value in farm_details.items():
            if value:
                details_text.append(f"{key}: {value}")
        
        memory_text = f"Farm {estate_name} details: {', '.join(details_text)}"
        
        metadata = {
            'type': 'farm_context',
            'estate_name': estate_name,
            'farm_details': farm_details
        }
        
        return await self.mem0_adapter.add_memory(user_id, memory_text, metadata)
    
    async def store_soil_insight(
        self,
        user_id: str,
        insight_text: str,
        soil_parameters: Dict[str, float],
        location: Optional[Dict[str, Any]] = None,
        confidence: float = 1.0
    ) -> str:
        """
        Store soil management insight or pattern.
        
        Args:
            user_id: User identifier
            insight_text: Insight description
            soil_parameters: Related soil parameters
            location: Location information
            confidence: Confidence score for the insight
            
        Returns:
            Memory ID
        """
        # Enhance memory text with soil parameters
        param_text = ", ".join([f"{k}: {v}" for k, v in soil_parameters.items() if v is not None])
        memory_text = f"Soil insight: {insight_text}. Parameters: {param_text}"
        
        metadata = {
            'type': 'soil_insight',
            'soil_parameters': soil_parameters,
            'location': location,
            'confidence': confidence,
            'insight_category': self._categorize_insight(insight_text)
        }
        
        return await self.mem0_adapter.add_memory(user_id, memory_text, metadata)
    
    async def store_conversation_context(
        self,
        user_id: str,
        conversation_summary: str,
        topics: List[str],
        estate_context: Optional[str] = None
    ) -> str:
        """
        Store conversation context for future reference.
        
        Args:
            user_id: User identifier
            conversation_summary: Summary of the conversation
            topics: List of topics discussed
            estate_context: Estate context if applicable
            
        Returns:
            Memory ID
        """
        memory_text = f"Conversation about: {', '.join(topics)}. Summary: {conversation_summary}"
        
        if estate_context:
            memory_text += f" (Estate: {estate_context})"
        
        metadata = {
            'type': 'conversation_context',
            'topics': topics,
            'estate_context': estate_context,
            'conversation_date': datetime.now().isoformat()
        }
        
        return await self.mem0_adapter.add_memory(user_id, memory_text, metadata)
    
    async def get_relevant_context(
        self,
        user_id: str,
        query: str,
        context_types: Optional[List[str]] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get relevant context for a user query.
        
        Args:
            user_id: User identifier
            query: User query or context
            context_types: Filter by context types
            limit: Maximum number of results
            
        Returns:
            List of relevant context memories
        """
        # Search memories
        memories = await self.mem0_adapter.search_memories(user_id, query, limit)
        
        # Filter by context types if specified
        if context_types:
            filtered_memories = []
            for memory in memories:
                memory_type = memory.get('metadata', {}).get('type')
                if memory_type in context_types:
                    filtered_memories.append(memory)
            memories = filtered_memories
        
        return memories
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get all user preferences.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary of user preferences
        """
        all_memories = await self.mem0_adapter.get_all_memories(user_id)
        
        preferences = {}
        for memory in all_memories:
            metadata = memory.get('metadata', {})
            if metadata.get('type') == 'user_preference':
                pref_type = metadata.get('preference_type')
                pref_value = metadata.get('preference_value')
                if pref_type and pref_value:
                    preferences[pref_type] = pref_value
        
        return preferences
    
    async def get_farm_context(self, user_id: str, estate_name: str) -> Dict[str, Any]:
        """
        Get farm context for a specific estate.
        
        Args:
            user_id: User identifier
            estate_name: Estate name
            
        Returns:
            Farm context information
        """
        memories = await self.get_relevant_context(
            user_id, 
            f"farm {estate_name}",
            context_types=['farm_context'],
            limit=10
        )
        
        # Combine farm details from all relevant memories
        combined_context = {}
        for memory in memories:
            metadata = memory.get('metadata', {})
            if metadata.get('estate_name') == estate_name:
                farm_details = metadata.get('farm_details', {})
                combined_context.update(farm_details)
        
        return combined_context
    
    async def get_soil_insights(
        self,
        user_id: str,
        soil_parameters: Optional[Dict[str, float]] = None,
        location: Optional[Dict[str, Any]] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get relevant soil insights.
        
        Args:
            user_id: User identifier
            soil_parameters: Current soil parameters for context
            location: Location for context
            limit: Maximum number of results
            
        Returns:
            List of relevant soil insights
        """
        # Build query based on parameters
        query_parts = []
        if soil_parameters:
            for param, value in soil_parameters.items():
                if value is not None:
                    query_parts.append(f"{param} {value}")
        
        if location:
            if 'estate' in location:
                query_parts.append(f"estate {location['estate']}")
            if 'block' in location:
                query_parts.append(f"block {location['block']}")
        
        query = " ".join(query_parts) if query_parts else "soil insight"
        
        return await self.get_relevant_context(
            user_id,
            query,
            context_types=['soil_insight'],
            limit=limit
        )
    
    async def update_memory_relevance(
        self,
        user_id: str,
        memory_id: str,
        relevance_score: float
    ) -> bool:
        """
        Update memory relevance score based on user feedback.
        
        Args:
            user_id: User identifier
            memory_id: Memory identifier
            relevance_score: New relevance score (0.0 to 1.0)
            
        Returns:
            True if updated successfully
        """
        metadata_update = {'relevance_score': relevance_score}
        return await self.mem0_adapter.update_memory(memory_id, metadata=metadata_update)
    
    async def cleanup_old_memories(
        self,
        user_id: str,
        days_threshold: int = 90,
        keep_important: bool = True
    ) -> int:
        """
        Clean up old memories to maintain performance.
        
        Args:
            user_id: User identifier
            days_threshold: Age threshold in days
            keep_important: Whether to keep important memories
            
        Returns:
            Number of memories cleaned up
        """
        all_memories = await self.mem0_adapter.get_all_memories(user_id)
        
        cutoff_date = datetime.now() - timedelta(days=days_threshold)
        cleanup_count = 0
        
        for memory in all_memories:
            created_at = datetime.fromisoformat(memory['created_at'].replace('Z', '+00:00'))
            
            if created_at < cutoff_date:
                # Check if memory should be kept
                if keep_important:
                    metadata = memory.get('metadata', {})
                    memory_type = metadata.get('type')
                    relevance = memory.get('relevance_score', 1.0)
                    
                    # Keep important memory types or high relevance
                    if memory_type in ['user_preference', 'farm_context'] or relevance > 0.8:
                        continue
                
                # Delete the memory
                if await self.mem0_adapter.delete_memory(memory['id']):
                    cleanup_count += 1
        
        logger.info(f"Cleaned up {cleanup_count} old memories for user {user_id}")
        return cleanup_count
    
    def _categorize_insight(self, insight_text: str) -> str:
        """
        Categorize soil insight based on content.
        
        Args:
            insight_text: Insight text
            
        Returns:
            Insight category
        """
        insight_lower = insight_text.lower()
        
        if any(word in insight_lower for word in ['ph', 'acid', 'alkaline']):
            return 'ph_management'
        elif any(word in insight_lower for word in ['nitrogen', 'phosphorus', 'potassium', 'nutrient']):
            return 'nutrient_management'
        elif any(word in insight_lower for word in ['moisture', 'water', 'irrigation']):
            return 'water_management'
        elif any(word in insight_lower for word in ['temperature', 'heat', 'cold']):
            return 'temperature_management'
        elif any(word in insight_lower for word in ['fertilizer', 'application', 'treatment']):
            return 'fertilizer_management'
        else:
            return 'general_insight'
    
    async def get_memory_statistics(self, user_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Memory statistics
        """
        stats = await self.mem0_adapter.get_memory_stats(user_id)
        
        # Add soil-specific statistics
        all_memories = await self.mem0_adapter.get_all_memories(user_id)
        
        type_counts = {}
        category_counts = {}
        
        for memory in all_memories:
            metadata = memory.get('metadata', {})
            memory_type = metadata.get('type', 'unknown')
            type_counts[memory_type] = type_counts.get(memory_type, 0) + 1
            
            if memory_type == 'soil_insight':
                category = metadata.get('insight_category', 'unknown')
                category_counts[category] = category_counts.get(category, 0) + 1
        
        stats.update({
            'memory_types': type_counts,
            'insight_categories': category_counts
        })
        
        return stats
