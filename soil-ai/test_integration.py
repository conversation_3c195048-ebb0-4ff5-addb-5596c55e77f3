#!/usr/bin/env python3
"""
Integration test for soil-ai component.

This script tests the core functionality including:
- BGE embedding generation
- Vector search capabilities
- Mem0 AI integration
- Database connectivity
"""

import asyncio
import sys
import os
import logging
import uuid
from datetime import datetime

# Add the soil_ai module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'soil_ai'))

from soil_ai.embeddings.bge_embedder import BGEEmbedder
from soil_ai.embeddings.vector_search import VectorSearchService
from soil_ai.memory.mem0_adapter import Mem0Adapter
from soil_ai.memory.soil_memory_service import SoilMemoryService
from soil_ai.memory.chat_context_manager import ChatContextManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_bge_embedder():
    """Test BGE embedder functionality."""
    logger.info("Testing BGE embedder...")
    
    try:
        embedder = BGEEmbedder()
        
        # Test text embedding
        test_text = "Soil pH is 6.5 with high nitrogen content"
        embedding = await embedder.embed_text_async(test_text)
        
        assert embedding is not None
        assert len(embedding) == 1024  # BGE-large-en-v1.5 dimension
        logger.info(f"✅ BGE embedder working. Embedding dimension: {len(embedding)}")
        
        # Test soil data embedding
        soil_data = {
            'soil_ph': 6.5,
            'soil_moisture': 45.0,
            'soil_temperature': 25.0,
            'soil_nitrogen': 35.0
        }
        
        soil_embedding = embedder.embed_soil_data(soil_data)
        assert soil_embedding is not None
        assert len(soil_embedding) == 1024
        logger.info("✅ Soil data embedding working")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ BGE embedder test failed: {e}")
        return False


async def test_vector_search():
    """Test vector search service."""
    logger.info("Testing vector search service...")
    
    try:
        embedder = BGEEmbedder()
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Add test content
        test_content_id = str(uuid.uuid4())
        test_content = "Soil analysis shows pH 6.8, moisture 50%, nitrogen 40ppm"
        
        await vector_service.add_content_embedding(
            test_content_id,
            "soil_analysis",
            test_content,
            {"test": True, "timestamp": datetime.now().isoformat()}
        )
        
        # Search for similar content
        results = await vector_service.search_similar_content(
            "soil pH nitrogen analysis",
            limit=5
        )
        
        assert len(results) > 0
        logger.info(f"✅ Vector search working. Found {len(results)} results")
        
        # Get statistics
        stats = await vector_service.get_content_statistics()
        logger.info(f"✅ Vector database stats: {stats}")
        
        await vector_service.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector search test failed: {e}")
        return False


async def test_mem0_adapter():
    """Test Mem0 adapter functionality."""
    logger.info("Testing Mem0 adapter...")
    
    try:
        mem0_adapter = Mem0Adapter(DATABASE_URL)
        await mem0_adapter.initialize()
        
        # Add test memory
        test_user_id = str(uuid.uuid4())
        memory_text = "User prefers organic fertilizers for their tomato crops"
        
        memory_id = await mem0_adapter.add_memory(
            test_user_id,
            memory_text,
            {"type": "preference", "crop": "tomato"}
        )
        
        assert memory_id is not None
        logger.info(f"✅ Memory added with ID: {memory_id}")
        
        # Search memories
        memories = await mem0_adapter.search_memories(
            test_user_id,
            "fertilizer preferences",
            limit=5
        )
        
        assert len(memories) > 0
        logger.info(f"✅ Memory search working. Found {len(memories)} memories")
        
        # Get all memories
        all_memories = await mem0_adapter.get_all_memories(test_user_id)
        logger.info(f"✅ Total memories for user: {len(all_memories)}")
        
        await mem0_adapter.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Mem0 adapter test failed: {e}")
        return False


async def test_soil_memory_service():
    """Test soil memory service."""
    logger.info("Testing soil memory service...")
    
    try:
        memory_service = SoilMemoryService(DATABASE_URL)
        await memory_service.initialize()
        
        test_user_id = str(uuid.uuid4())
        
        # Store user preference
        pref_id = await memory_service.store_user_preference(
            test_user_id,
            "notification_frequency",
            "daily",
            {"estate": "test_farm"}
        )
        
        assert pref_id is not None
        logger.info("✅ User preference stored")
        
        # Store soil insight
        insight_id = await memory_service.store_soil_insight(
            test_user_id,
            "Low pH detected, recommend lime application",
            {"soil_ph": 5.8, "soil_nitrogen": 25.0},
            {"estate": "test_farm", "block": "A1"}
        )
        
        assert insight_id is not None
        logger.info("✅ Soil insight stored")
        
        # Get relevant context
        context = await memory_service.get_relevant_context(
            test_user_id,
            "pH lime application",
            limit=5
        )
        
        assert len(context) > 0
        logger.info(f"✅ Context retrieval working. Found {len(context)} items")
        
        await memory_service.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Soil memory service test failed: {e}")
        return False


async def test_chat_context_manager():
    """Test chat context manager."""
    logger.info("Testing chat context manager...")
    
    try:
        chat_manager = ChatContextManager(DATABASE_URL)
        await chat_manager.initialize()
        
        test_user_id = str(uuid.uuid4())
        
        # Create chat session
        session_id = await chat_manager.create_chat_session(
            test_user_id,
            initial_context={"estate": "test_farm"}
        )
        
        assert session_id is not None
        logger.info(f"✅ Chat session created: {session_id}")
        
        # Add message to session
        message_id = await chat_manager.add_message_to_session(
            session_id,
            "What's the optimal pH for tomatoes?",
            "user"
        )
        
        assert message_id is not None
        logger.info("✅ Message added to session")
        
        # Get conversation context
        context = await chat_manager.get_conversation_context(session_id)
        assert context is not None
        assert len(context.get('recent_messages', [])) > 0
        logger.info("✅ Conversation context retrieved")
        
        await chat_manager.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Chat context manager test failed: {e}")
        return False


async def run_integration_tests():
    """Run all integration tests."""
    logger.info("Starting soil-ai integration tests...")
    
    tests = [
        ("BGE Embedder", test_bge_embedder),
        ("Vector Search", test_vector_search),
        ("Mem0 Adapter", test_mem0_adapter),
        ("Soil Memory Service", test_soil_memory_service),
        ("Chat Context Manager", test_chat_context_manager),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All integration tests PASSED!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests FAILED!")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_integration_tests())
    sys.exit(0 if success else 1)
