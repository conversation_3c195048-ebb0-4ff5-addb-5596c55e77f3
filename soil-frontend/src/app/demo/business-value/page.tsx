// Business Value Demonstration Hub - Enhanced UX with Progressive Disclosure
// Soil Master v1.0.6 - Improved User Experience

'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  DollarSign, 
  Target, 
  Award,
  BarChart3,
  CheckCircle,
  Zap,
  Users,
  Calculator,
  Download,
  Share2,
  Play,
  Clock,
  Shield
} from 'lucide-react';

// Import UX components
import {
  CollapsibleSection,
  MetricCard
} from '@/components/ui/UXComponents';
import {
  DashboardLayout,
  RoleContent,
  QuickActionBar,
  Breadcrumb
} from '@/components/ui/DashboardLayout';

export default function BusinessValueDemonstrationPage() {
  const [activeDemo, setActiveDemo] = useState<'overview' | 'cases' | 'stories' | 'calculator'>('overview');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentRole, setCurrentRole] = useState<'executive' | 'demonstration' | 'operational' | 'financial'>('demonstration');

  // Widget visibility state for customization
  const [widgets, setWidgets] = useState([
    { id: 'key-metrics', name: 'Key Value Propositions', visible: true, priority: 'high' as const },
    { id: 'business-cases', name: 'Business Cases', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'success-stories', name: 'Success Stories', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'charts', name: 'Financial Charts', visible: currentRole === 'financial', priority: 'low' as const }
  ]);

  const startDemo = () => {
    setIsPlaying(true);
    setTimeout(() => setIsPlaying(false), 3000);
  };

  // Quick actions for the dashboard
  const quickActions = [
    {
      id: 'demo',
      label: isPlaying ? 'Demo Running...' : 'Start Demo',
      icon: isPlaying ? Clock : Play,
      onClick: startDemo,
      variant: 'primary' as const,
      disabled: isPlaying
    },
    {
      id: 'download',
      label: 'Download Report',
      icon: Download,
      onClick: () => alert('Download functionality coming soon!'),
      variant: 'outline' as const
    },
    {
      id: 'share',
      label: 'Share Analysis',
      icon: Share2,
      onClick: () => alert('Share functionality coming soon!'),
      variant: 'outline' as const
    }
  ];

  // Handle widget visibility changes
  const handleWidgetVisibilityChange = (widgetId: string, visible: boolean) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === widgetId ? { ...widget, visible } : widget
    ));
  };

  return (
    <DashboardLayout
      title="Business Value Demonstration"
      subtitle="Quantify and visualize the clear ROI and business impact of precision agriculture"
      currentRole={currentRole}
      onRoleChange={(role) => setCurrentRole(role as any)}
      availableRoles={['executive', 'demonstration', 'operational', 'financial']}
      widgets={widgets}
      onWidgetVisibilityChange={handleWidgetVisibilityChange}
      headerActions={<QuickActionBar actions={quickActions} />}
    >
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={[
        { label: 'Demo', href: '/demo' },
        { label: 'Business Value', current: true }
      ]} />

      {/* Tab Navigation - Simplified */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {[
            { id: 'overview', name: 'Value Overview', icon: Target },
            { id: 'cases', name: 'Business Cases', icon: BarChart3 },
            { id: 'stories', name: 'Success Stories', icon: Award },
            { id: 'calculator', name: 'ROI Calculator', icon: Calculator }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveDemo(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  activeDemo === tab.id
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Role-Based Content */}
      <RoleContent
        role={currentRole}
        mode="overview"
      >
        {{
          executive: (
            <ExecutiveBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          demonstration: (
            <DemonstrationBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          operational: (
            <OperationalBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          financial: (
            <FinancialBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          )
        }}
      </RoleContent>
    </DashboardLayout>
  );
}

// Executive Business Value View - High-level value propositions only
const ExecutiveBusinessValueView: React.FC<{
  activeDemo: string;
  widgets: any[];
}> = ({ activeDemo, widgets }) => {
  if (activeDemo === 'overview') {
    return (
      <div className="space-y-6">
        {/* Key Value Propositions Only */}
        {widgets.find(w => w.id === 'key-metrics')?.visible && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <MetricCard
              title="Cost Reduction"
              value="15-30%"
              priority="high"
              icon={DollarSign}
              color="green"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Reduce fertilizer and operational costs through precision agriculture</p>
                </div>
              }
            />
            <MetricCard
              title="Yield Increase"
              value="20-35%"
              priority="high"
              icon={TrendingUp}
              color="blue"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Optimize growing conditions for maximum crop productivity</p>
                </div>
              }
            />
            <MetricCard
              title="Payback Period"
              value="2-4 Years"
              priority="high"
              icon={Clock}
              color="purple"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Fast return on investment with proven results</p>
                </div>
              }
            />
            <MetricCard
              title="Compliance"
              value="MPOB Ready"
              priority="medium"
              icon={CheckCircle}
              color="orange"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Meet MPOB standards and environmental compliance</p>
                </div>
              }
            />
          </div>
        )}

        {/* Executive Summary */}
        <CollapsibleSection
          title="Executive Summary"
          defaultExpanded={true}
          priority="high"
          icon={Target}
        >
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Strategic Business Impact</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Financial Benefits</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Immediate cost reduction through optimized resource usage</li>
                  <li>• Increased revenue from higher yields and quality</li>
                  <li>• Predictable ROI with 2-4 year payback period</li>
                  <li>• Reduced operational risks and waste</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Strategic Advantages</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Market leadership in sustainable agriculture</li>
                  <li>• Compliance with environmental regulations</li>
                  <li>• Enhanced brand reputation and ESG credentials</li>
                  <li>• Future-ready technology infrastructure</li>
                </ul>
              </div>
            </div>
          </div>
        </CollapsibleSection>
      </div>
    );
  }

  return <div className="text-center py-12 text-gray-500">Select Value Overview to view executive summary</div>;
};

// Demonstration Business Value View - Full showcase
const DemonstrationBusinessValueView: React.FC<{
  activeDemo: string;
  widgets: any[];
}> = ({ activeDemo, widgets }) => {
  if (activeDemo === 'overview') {
    return (
      <div className="space-y-6">
        {/* Business Value Showcase */}
        {widgets.find(w => w.id === 'key-metrics')?.visible && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-6">
              <div className="flex items-center mb-3">
                <DollarSign className="w-8 h-8 mr-3" />
                <h3 className="text-lg font-semibold">15-30% Cost Reduction</h3>
              </div>
              <p className="text-green-100 text-sm">Reduce fertilizer and operational costs through precision agriculture</p>
            </div>

            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6">
              <div className="flex items-center mb-3">
                <TrendingUp className="w-8 h-8 mr-3" />
                <h3 className="text-lg font-semibold">20-35% Yield Increase</h3>
              </div>
              <p className="text-blue-100 text-sm">Optimize growing conditions for maximum crop productivity</p>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg p-6">
              <div className="flex items-center mb-3">
                <Clock className="w-8 h-8 mr-3" />
                <h3 className="text-lg font-semibold">2-4 Year Payback</h3>
              </div>
              <p className="text-purple-100 text-sm">Fast return on investment with proven results</p>
            </div>

            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg p-6">
              <div className="flex items-center mb-3">
                <CheckCircle className="w-8 h-8 mr-3" />
                <h3 className="text-lg font-semibold">MPOB Compliant</h3>
              </div>
              <p className="text-orange-100 text-sm">Meet MPOB standards and environmental compliance</p>
            </div>
          </div>
        )}

        {/* Demo Business Cases */}
        {widgets.find(w => w.id === 'business-cases')?.visible && (
          <CollapsibleSection
            title="Featured Business Cases"
            defaultExpanded={false}
            priority="medium"
            icon={BarChart3}
            badge="Interactive"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Medium Palm Oil Estate</h3>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    Featured
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">500 hectares in Peninsular Malaysia with comprehensive precision agriculture implementation</p>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">245%</div>
                    <div className="text-xs text-gray-600">ROI</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">3.2y</div>
                    <div className="text-xs text-gray-600">Payback</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span>500 hectares</span>
                  <span>Palm Oil</span>
                </div>

                <button className="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                  View Details
                </button>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Large Rubber Plantation</h3>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                    Featured
                  </span>
                </div>

                <p className="text-gray-600 text-sm mb-4">1200 hectares in Sabah with smart irrigation and soil monitoring</p>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">313%</div>
                    <div className="text-xs text-gray-600">ROI</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">2.8y</div>
                    <div className="text-xs text-gray-600">Payback</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span>1200 hectares</span>
                  <span>Rubber</span>
                </div>

                <button className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                  View Details
                </button>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Boutique Cocoa Farm</h3>
                </div>

                <p className="text-gray-600 text-sm mb-4">150 hectares with precision nutrient management for premium cocoa</p>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">185%</div>
                    <div className="text-xs text-gray-600">ROI</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">2.5y</div>
                    <div className="text-xs text-gray-600">Payback</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <span>150 hectares</span>
                  <span>Cocoa</span>
                </div>

                <button className="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors">
                  View Details
                </button>
              </div>
            </div>
          </CollapsibleSection>
        )}
      </div>
    );
  }

  return <div className="text-center py-12 text-gray-500">Select a tab to view demonstration content</div>;
};

// Operational Business Value View - Implementation focused
const OperationalBusinessValueView: React.FC<{
  activeDemo: string;
  widgets: any[];
}> = ({ activeDemo, widgets }) => {
  if (activeDemo === 'overview') {
    return (
      <div className="space-y-6">
        {/* Operational Benefits */}
        {widgets.find(w => w.id === 'key-metrics')?.visible && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MetricCard
              title="Implementation Time"
              value="2-4 Weeks"
              priority="high"
              icon={Clock}
              color="blue"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Rapid deployment with minimal disruption to operations</p>
                </div>
              }
            />
            <MetricCard
              title="Training Required"
              value="1-2 Days"
              priority="high"
              icon={Users}
              color="green"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Comprehensive training program for farm operators</p>
                </div>
              }
            />
            <MetricCard
              title="Maintenance"
              value="Minimal"
              priority="medium"
              icon={Zap}
              color="purple"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Self-monitoring system with automated alerts</p>
                </div>
              }
            />
          </div>
        )}

        {/* Implementation Roadmap */}
        <CollapsibleSection
          title="Implementation Roadmap"
          defaultExpanded={true}
          priority="high"
          icon={Target}
        >
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Site Assessment & Planning</h4>
                <p className="text-sm text-gray-600">Soil analysis, infrastructure evaluation, and system design (Week 1)</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Equipment Installation</h4>
                <p className="text-sm text-gray-600">Sensor deployment, connectivity setup, and system integration (Week 2-3)</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Training & Go-Live</h4>
                <p className="text-sm text-gray-600">Staff training, system calibration, and operational handover (Week 4)</p>
              </div>
            </div>
          </div>
        </CollapsibleSection>
      </div>
    );
  }

  return <div className="text-center py-12 text-gray-500">Select Value Overview to view operational details</div>;
};

// Financial Business Value View - Detailed financial analysis
const FinancialBusinessValueView: React.FC<{
  activeDemo: string;
  widgets: any[];
}> = ({ activeDemo, widgets }) => {
  if (activeDemo === 'overview') {
    return (
      <div className="space-y-6">
        {/* Financial Metrics */}
        {widgets.find(w => w.id === 'key-metrics')?.visible && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Average ROI"
              value="245%"
              priority="high"
              icon={TrendingUp}
              color="green"
              details={
                <div className="text-sm text-gray-600">
                  <p>Based on 50+ implementations</p>
                  <p>Range: 180% - 320%</p>
                </div>
              }
            />
            <MetricCard
              title="NPV (5-year)"
              value="RM 425K"
              priority="high"
              icon={DollarSign}
              color="blue"
              details={
                <div className="text-sm text-gray-600">
                  <p>Net Present Value at 10% discount rate</p>
                  <p>Average 100ha palm oil estate</p>
                </div>
              }
            />
            <MetricCard
              title="IRR"
              value="68%"
              priority="high"
              icon={BarChart3}
              color="purple"
              details={
                <div className="text-sm text-gray-600">
                  <p>Internal Rate of Return</p>
                  <p>Well above industry hurdle rates</p>
                </div>
              }
            />
            <MetricCard
              title="Risk Score"
              value="Low"
              priority="medium"
              icon={Shield}
              color="orange"
              details={
                <div className="text-sm text-gray-600">
                  <p>Proven technology with 95% success rate</p>
                  <p>Comprehensive warranty coverage</p>
                </div>
              }
            />
          </div>
        )}

        {/* Detailed Financial Analysis */}
        {widgets.find(w => w.id === 'charts')?.visible && (
          <CollapsibleSection
            title="Detailed Financial Analysis"
            defaultExpanded={true}
            priority="high"
            icon={BarChart3}
            badge="Comprehensive"
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Investment Breakdown</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Hardware & Sensors:</span>
                    <span className="font-medium">RM 45,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Software & Platform:</span>
                    <span className="font-medium">RM 15,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Installation & Setup:</span>
                    <span className="font-medium">RM 10,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Training & Support:</span>
                    <span className="font-medium">RM 5,000</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-semibold">
                    <span>Total Investment:</span>
                    <span>RM 75,000</span>
                  </div>
                </div>
              </div>

              <div className="bg-white border rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Annual Savings</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Fertilizer Optimization:</span>
                    <span className="font-medium text-green-600">RM 30,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Water Management:</span>
                    <span className="font-medium text-green-600">RM 15,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Labor Efficiency:</span>
                    <span className="font-medium text-green-600">RM 10,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Equipment Optimization:</span>
                    <span className="font-medium text-green-600">RM 5,000</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-semibold">
                    <span>Total Annual Savings:</span>
                    <span className="text-green-600">RM 60,000</span>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleSection>
        )}
      </div>
    );
  }

  return <div className="text-center py-12 text-gray-500">Select Value Overview to view financial analysis</div>;
};
