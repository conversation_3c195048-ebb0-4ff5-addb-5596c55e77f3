// Executive Cost Savings Dashboard - Enhanced UX with Progressive Disclosure
// Soil Master v1.0.6 - Improved User Experience

'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingDown, 
  DollarSign, 
  Target, 
  Award,
  BarChart3,
  PieChart,
  ArrowDown,
  CheckCircle,
  Zap,
  Calendar,
  Globe,
  Leaf,
  Calculator,
  Download,
  Share2,
  RefreshCw,
  TrendingUp,
  AlertCircle,
  Users
} from 'lucide-react';

// Import professional chart components
import {
  ROIProjection<PERSON><PERSON>,
  CostSavingsChart,
  PaybackPeriodChart,
  YieldImprovementChart,
  FinancialImpactChart
} from '@/components/charts/ChartComponents';

// Import UX components
import {
  CollapsibleSection,
  MetricCard
} from '@/components/ui/UXComponents';
import {
  DashboardLayout,
  RoleContent,
  QuickActionBar,
  Breadcrumb
} from '@/components/ui/DashboardLayout';

interface CostSavingsData {
  totalAnnualSavings: number;
  savingsPerHectare: number;
  fertilizerSavings: number;
  waterSavings: number;
  laborSavings: number;
  equipmentSavings: number;
  wasteReduction: number;
  yieldIncrease: number;
  revenueIncrease: number;
  paybackMonths: number;
  roi: number;
  npv: number;
}

interface ExecutiveMetric {
  title: string;
  value: string;
  change: number;
  trend: 'up' | 'down';
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

export default function ExecutiveCostSavingsDashboard() {
  const [timeframe, setTimeframe] = useState<'monthly' | 'quarterly' | 'annual'>('annual');
  const [farmSize, setFarmSize] = useState(1000);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [currentRole, setCurrentRole] = useState<'executive' | 'demonstration' | 'operational' | 'financial'>('executive');

  // Widget visibility state for customization
  const [widgets, setWidgets] = useState([
    { id: 'key-metrics', name: 'Key Cost Savings', visible: true, priority: 'high' as const },
    { id: 'breakdown', name: 'Savings Breakdown', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'charts', name: 'Visual Analytics', visible: currentRole === 'financial', priority: 'medium' as const },
    { id: 'sustainability', name: 'Sustainability Metrics', visible: false, priority: 'low' as const }
  ]);

  // Sample cost savings data
  const costSavingsData: CostSavingsData = {
    totalAnnualSavings: 285000,
    savingsPerHectare: 285,
    fertilizerSavings: 125000,
    waterSavings: 75000,
    laborSavings: 55000,
    equipmentSavings: 30000,
    wasteReduction: 22.5,
    yieldIncrease: 18.3,
    revenueIncrease: 420000,
    paybackMonths: 28,
    roi: 245.8,
    npv: 1250000
  };

  // Executive metrics for dashboard
  const executiveMetrics: ExecutiveMetric[] = [
    {
      title: 'Annual Cost Savings',
      value: `RM ${(costSavingsData.totalAnnualSavings / 1000).toFixed(0)}K`,
      change: 18.5,
      trend: 'up',
      icon: TrendingDown,
      color: 'green',
      description: 'Total operational cost reduction achieved'
    },
    {
      title: 'Return on Investment',
      value: `${costSavingsData.roi.toFixed(1)}%`,
      change: 12.3,
      trend: 'up',
      icon: TrendingUp,
      color: 'blue',
      description: 'Financial return on precision agriculture investment'
    },
    {
      title: 'Revenue Increase',
      value: `RM ${(costSavingsData.revenueIncrease / 1000).toFixed(0)}K`,
      change: 22.1,
      trend: 'up',
      icon: DollarSign,
      color: 'purple',
      description: 'Additional revenue from improved yields'
    },
    {
      title: 'Payback Period',
      value: `${Math.floor(costSavingsData.paybackMonths / 12)}.${costSavingsData.paybackMonths % 12} years`,
      change: -15.2,
      trend: 'down',
      icon: Calendar,
      color: 'orange',
      description: 'Time to recover initial investment'
    }
  ];

  const refreshData = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1500);
  };

  // Quick actions for the dashboard
  const quickActions = [
    {
      id: 'refresh',
      label: isLoading ? 'Refreshing...' : 'Refresh Data',
      icon: RefreshCw,
      onClick: refreshData,
      variant: 'primary' as const,
      disabled: isLoading
    },
    {
      id: 'download',
      label: 'Download Report',
      icon: Download,
      onClick: () => alert('Download functionality coming soon!'),
      variant: 'outline' as const
    },
    {
      id: 'share',
      label: 'Share Dashboard',
      icon: Share2,
      onClick: () => alert('Share functionality coming soon!'),
      variant: 'outline' as const
    }
  ];

  // Handle widget visibility changes
  const handleWidgetVisibilityChange = (widgetId: string, visible: boolean) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === widgetId ? { ...widget, visible } : widget
    ));
  };

  return (
    <DashboardLayout
      title="Executive Cost Savings Dashboard"
      subtitle={`Real-time financial impact analysis • Last updated: ${lastUpdated.toLocaleTimeString()}`}
      currentRole={currentRole}
      onRoleChange={(role) => setCurrentRole(role as any)}
      availableRoles={['executive', 'demonstration', 'operational', 'financial']}
      widgets={widgets}
      onWidgetVisibilityChange={handleWidgetVisibilityChange}
      headerActions={<QuickActionBar actions={quickActions} />}
    >
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={[
        { label: 'Demo', href: '/demo' },
        { label: 'Cost Savings', current: true }
      ]} />

      {/* Farm Size Selector */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 bg-white rounded-lg p-4 border border-gray-200">
          <label className="text-sm font-medium text-gray-700">Farm Size:</label>
          <select
            value={farmSize}
            onChange={(e) => setFarmSize(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value={500}>500 hectares</option>
            <option value={1000}>1,000 hectares</option>
            <option value={1500}>1,500 hectares</option>
            <option value={2000}>2,000 hectares</option>
          </select>
          <label className="text-sm font-medium text-gray-700">Timeframe:</label>
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="annual">Annual</option>
          </select>
        </div>
      </div>

      {/* Role-Based Content */}
      <RoleContent
        role={currentRole}
        mode="overview"
      >
        {{
          executive: (
            <ExecutiveCostSavingsView 
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          demonstration: (
            <DemonstrationCostSavingsView 
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          operational: (
            <OperationalCostSavingsView 
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          financial: (
            <FinancialCostSavingsView 
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          )
        }}
      </RoleContent>
    </DashboardLayout>
  );
}
