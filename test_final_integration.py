#!/usr/bin/env python3
"""
Final integration test for the complete soil system stack.

Tests seamless communication between all components:
- Backend API (FastAPI on port 8000)
- Database Layer (PostgreSQL with extensions)
- Soil-AI Component (BGE embeddings, vector search, Mem0)
"""

import asyncio
import sys
import os
import logging
import uuid
import json
import httpx
import time
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BACKEND_URL = "http://localhost:8000"
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_api_backend_integration():
    """Test API backend integration and health."""
    logger.info("🌐 Testing API Backend Integration...")
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            health_response = await client.get(f"{BACKEND_URL}/health")
            assert health_response.status_code == 200
            health_data = health_response.json()
            assert health_data["status"] == "healthy"
            
            # Test API documentation
            docs_response = await client.get(f"{BACKEND_URL}/docs")
            assert docs_response.status_code == 200
            assert "text/html" in docs_response.headers.get("content-type", "")
            
            # Test concurrent API calls
            api_tasks = [client.get(f"{BACKEND_URL}/health") for _ in range(5)]
            start_time = time.time()
            api_responses = await asyncio.gather(*api_tasks)
            api_time = time.time() - start_time
            
            concurrent_success = all(r.status_code == 200 for r in api_responses)
            
            logger.info(f"✅ Health Status: {health_data}")
            logger.info(f"✅ API Documentation: Accessible")
            logger.info(f"✅ Concurrent API Calls: {len(api_responses)} in {api_time:.3f}s")
            
            return {
                "success": True,
                "health_data": health_data,
                "docs_accessible": True,
                "concurrent_performance": api_time,
                "concurrent_success": concurrent_success
            }
            
    except Exception as e:
        logger.error(f"❌ API backend integration failed: {e}")
        return {"success": False, "error": str(e)}


async def test_database_extensions_integration():
    """Test database extensions integration."""
    logger.info("🗄️ Testing Database Extensions Integration...")
    
    try:
        import asyncpg
        
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test all required extensions
        extensions = await conn.fetch("""
            SELECT extname, extversion 
            FROM pg_extension 
            WHERE extname IN ('vector', 'timescaledb', 'age', 'postgis')
            ORDER BY extname
        """)
        
        extension_dict = {ext['extname']: ext['extversion'] for ext in extensions}
        
        # Test vector operations
        vector_test = await conn.fetchval("SELECT '[1,2,3]'::vector(3) <-> '[4,5,6]'::vector(3)")
        
        # Test vector table existence
        vector_tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name IN ('vector_embeddings', 'mem0_memories', 'chat_messages')
        """)
        
        table_names = [table['table_name'] for table in vector_tables]
        
        # Test concurrent database operations
        tasks = []
        for i in range(5):
            task = conn.fetchval("SELECT $1::text", f"concurrent_test_{i}")
            tasks.append(task)
        
        start_time = time.time()
        concurrent_results = await asyncio.gather(*tasks)
        db_concurrent_time = time.time() - start_time
        
        await conn.close()
        
        logger.info(f"✅ Extensions: {extension_dict}")
        logger.info(f"✅ Vector Operations: {vector_test}")
        logger.info(f"✅ Vector Tables: {table_names}")
        logger.info(f"✅ Concurrent DB Ops: {len(concurrent_results)} in {db_concurrent_time:.3f}s")
        
        return {
            "success": True,
            "extensions": extension_dict,
            "vector_operations": True,
            "vector_tables": table_names,
            "concurrent_performance": db_concurrent_time
        }
        
    except Exception as e:
        logger.error(f"❌ Database extensions integration failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def test_soil_ai_components():
    """Test soil-ai components directly."""
    logger.info("🤖 Testing Soil-AI Components...")
    
    try:
        # Run the existing soil-ai test
        import subprocess
        
        result = subprocess.run([
            'bash', '-c', 
            'cd /home/<USER>/soil-master/soil-ai && source venv/bin/activate && python test_simple_integration.py'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            # Parse output for metrics
            output_lines = result.stdout.split('\n')
            
            # Extract key metrics
            embeddings_working = "BGE embedder working correctly" in result.stdout
            vector_search_working = "Vector search working" in result.stdout
            memory_working = "Memory search working" in result.stdout
            
            logger.info("✅ BGE Embedder: Working correctly")
            logger.info("✅ Vector Search: Operational")
            logger.info("✅ Memory Operations: Functional")
            
            return {
                "success": True,
                "bge_embedder": embeddings_working,
                "vector_search": vector_search_working,
                "memory_operations": memory_working,
                "test_output": result.stdout[-500:]  # Last 500 chars
            }
        else:
            logger.error(f"❌ Soil-AI test failed with return code: {result.returncode}")
            return {"success": False, "error": result.stderr}
            
    except Exception as e:
        logger.error(f"❌ Soil-AI components test failed: {e}")
        return {"success": False, "error": str(e)}


async def test_performance_metrics():
    """Test system performance metrics."""
    logger.info("⚡ Testing Performance Metrics...")
    
    try:
        # Test API response times
        async with httpx.AsyncClient() as client:
            api_times = []
            for _ in range(10):
                start_time = time.time()
                response = await client.get(f"{BACKEND_URL}/health")
                end_time = time.time()
                
                if response.status_code == 200:
                    api_times.append(end_time - start_time)
            
            avg_api_time = sum(api_times) / len(api_times) if api_times else 0
            max_api_time = max(api_times) if api_times else 0
        
        # Test database query performance
        import asyncpg
        conn = await asyncpg.connect(DATABASE_URL)
        
        db_times = []
        for _ in range(10):
            start_time = time.time()
            await conn.fetchval("SELECT 1")
            end_time = time.time()
            db_times.append(end_time - start_time)
        
        avg_db_time = sum(db_times) / len(db_times)
        max_db_time = max(db_times)
        
        await conn.close()
        
        # Performance criteria
        api_performance_good = avg_api_time < 0.1  # <100ms
        db_performance_good = avg_db_time < 0.01   # <10ms
        
        logger.info(f"✅ API Response Time: {avg_api_time:.3f}s avg, {max_api_time:.3f}s max")
        logger.info(f"✅ DB Query Time: {avg_db_time:.3f}s avg, {max_db_time:.3f}s max")
        logger.info(f"✅ API Performance: {'GOOD' if api_performance_good else 'NEEDS IMPROVEMENT'}")
        logger.info(f"✅ DB Performance: {'GOOD' if db_performance_good else 'NEEDS IMPROVEMENT'}")
        
        return {
            "success": True,
            "api_avg_time": avg_api_time,
            "api_max_time": max_api_time,
            "db_avg_time": avg_db_time,
            "db_max_time": max_db_time,
            "api_performance_good": api_performance_good,
            "db_performance_good": db_performance_good
        }
        
    except Exception as e:
        logger.error(f"❌ Performance metrics test failed: {e}")
        return {"success": False, "error": str(e)}


async def run_final_integration_tests():
    """Run final integration tests for the complete soil system."""
    logger.info("🚀 Starting Final Soil System Integration Tests...")
    
    tests = [
        ("API Backend Integration", test_api_backend_integration),
        ("Database Extensions Integration", test_database_extensions_integration),
        ("Soil-AI Components", test_soil_ai_components),
        ("Performance Metrics", test_performance_metrics),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"Running {test_name}...")
        logger.info(f"{'='*80}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result.get("success", False):
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # Calculate success rate
    passed = sum(1 for result in results.values() if result.get("success", False))
    total = len(results)
    success_rate = (passed / total) * 100
    
    # Summary
    logger.info(f"\n{'='*80}")
    logger.info("FINAL INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*80}")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({success_rate:.1f}% success rate)")
    
    # Final verdict
    if success_rate >= 90:
        logger.info("🎉 COMPLETE SOIL SYSTEM DEPLOYMENT SUCCESSFUL!")
        logger.info("✅ All components integrated and working seamlessly!")
        logger.info("🚀 SYSTEM IS PRODUCTION READY!")
    else:
        logger.error(f"💥 INTEGRATION ISSUES DETECTED! ({success_rate:.1f}% success rate)")
        logger.error("❌ System needs attention before production deployment")
    
    return success_rate >= 90, results, success_rate


if __name__ == "__main__":
    success, results, success_rate = asyncio.run(run_final_integration_tests())
    
    # Print final deployment status
    print(f"\n{'='*80}")
    print("FINAL DEPLOYMENT STATUS")
    print(f"{'='*80}")
    print(f"Success Rate: {success_rate:.1f}%")
    print(f"Tests Passed: {sum(1 for r in results.values() if r.get('success', False))}/{len(results)}")
    print(f"System Status: {'✅ PRODUCTION READY' if success_rate >= 90 else '❌ NEEDS ATTENTION'}")
    print(f"Deployment: {'✅ SUCCESSFUL' if success else '❌ INCOMPLETE'}")
    
    sys.exit(0 if success else 1)
