#!/usr/bin/env python3
"""
Comprehensive test runner for achieving 100% test coverage.

This script runs all test suites in the correct order and generates
comprehensive coverage reports for the Soil AI platform.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Any


class ComprehensiveTestRunner:
    """Comprehensive test runner for the Soil AI platform."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_results: Dict[str, Any] = {}
        
    def run_command(self, command: List[str], description: str) -> bool:
        """Run a command and capture results."""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            success = result.returncode == 0
            self.test_results[description] = {
                "success": success,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            if success:
                print(f"✅ {description} completed successfully")
            else:
                print(f"❌ {description} failed with return code {result.returncode}")
            
            return success
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} timed out after 30 minutes")
            self.test_results[description] = {
                "success": False,
                "error": "timeout"
            }
            return False
        except Exception as e:
            print(f"💥 {description} failed with exception: {e}")
            self.test_results[description] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def run_unit_tests(self) -> bool:
        """Run all unit tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "unit", "-v", "--tb=short"],
            "Unit Tests"
        )
    
    def run_integration_tests(self) -> bool:
        """Run all integration tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "integration", "-v", "--tb=short"],
            "Integration Tests"
        )
    
    def run_security_tests(self) -> bool:
        """Run all security tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "security", "-v", "--tb=short"],
            "Security Tests"
        )
    
    def run_mpob_tests(self) -> bool:
        """Run MPOB compliance tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "mpob", "-v", "--tb=short"],
            "MPOB Compliance Tests"
        )
    
    def run_authorization_tests(self) -> bool:
        """Run authorization system tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "authorization", "-v", "--tb=short"],
            "Authorization System Tests"
        )
    
    def run_model_performance_tests(self) -> bool:
        """Run model performance tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "model_performance", "-v", "--tb=short"],
            "Model Performance Tests"
        )
    
    def run_performance_tests(self) -> bool:
        """Run performance tests."""
        return self.run_command(
            ["python", "-m", "pytest", "-m", "performance", "-v", "--tb=short"],
            "Performance Tests"
        )
    
    def run_all_tests_with_coverage(self) -> bool:
        """Run all tests with comprehensive coverage."""
        return self.run_command(
            [
                "python", "-m", "pytest",
                "--cov=app",
                "--cov-report=html:htmlcov",
                "--cov-report=xml:coverage.xml",
                "--cov-report=term-missing",
                "--cov-fail-under=95",
                "--cov-branch",
                "-v",
                "--tb=short",
                "--durations=20"
            ],
            "All Tests with Coverage"
        )
    
    def run_specific_test_files(self) -> bool:
        """Run specific test files for new functionality."""
        test_files = [
            "tests/test_authorization_system.py",
            "tests/test_mpob_compliance.py",
            "tests/test_security_system.py",
            "tests/test_model_performance.py"
        ]
        
        success = True
        for test_file in test_files:
            if (self.project_root / test_file).exists():
                file_success = self.run_command(
                    ["python", "-m", "pytest", test_file, "-v", "--tb=short"],
                    f"Test File: {test_file}"
                )
                success = success and file_success
            else:
                print(f"⚠️  Test file {test_file} not found, skipping...")
        
        return success
    
    def check_test_coverage(self) -> bool:
        """Check test coverage and generate reports."""
        return self.run_command(
            ["python", "-m", "coverage", "report", "--show-missing"],
            "Coverage Report"
        )
    
    def run_linting(self) -> bool:
        """Run code linting checks."""
        commands = [
            (["python", "-m", "flake8", "app", "--max-line-length=100"], "Flake8 Linting"),
            (["python", "-m", "black", "--check", "app"], "Black Code Formatting Check"),
            (["python", "-m", "isort", "--check-only", "app"], "Import Sorting Check"),
            (["python", "-m", "mypy", "app"], "Type Checking")
        ]
        
        success = True
        for command, description in commands:
            try:
                result = self.run_command(command, description)
                success = success and result
            except Exception:
                print(f"⚠️  {description} tool not available, skipping...")
        
        return success
    
    def generate_summary_report(self):
        """Generate a summary report of all test results."""
        print(f"\n{'='*80}")
        print("COMPREHENSIVE TEST SUMMARY REPORT")
        print(f"{'='*80}")
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        print(f"Total Test Suites: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {total_tests - successful_tests}")
        print(f"Success Rate: {(successful_tests / total_tests * 100):.1f}%" if total_tests > 0 else "N/A")
        
        print(f"\n{'='*40}")
        print("DETAILED RESULTS:")
        print(f"{'='*40}")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            print(f"{status} {test_name}")
            
            if not result.get("success", False):
                if "error" in result:
                    print(f"    Error: {result['error']}")
                elif "returncode" in result:
                    print(f"    Return code: {result['returncode']}")
        
        # Coverage information
        coverage_file = self.project_root / "htmlcov" / "index.html"
        if coverage_file.exists():
            print(f"\n📊 Coverage report available at: {coverage_file}")
        
        print(f"\n{'='*80}")
        
        return successful_tests == total_tests
    
    def run_comprehensive_tests(self, test_types: List[str] = None) -> bool:
        """Run comprehensive test suite."""
        print("🚀 Starting Comprehensive Test Suite for Soil AI Platform")
        print(f"Project Root: {self.project_root}")
        
        # Default test types if none specified
        if test_types is None:
            test_types = [
                "unit",
                "integration", 
                "security",
                "mpob",
                "authorization",
                "model_performance",
                "specific_files",
                "all_with_coverage",
                "linting"
            ]
        
        # Run tests based on specified types
        overall_success = True
        
        if "unit" in test_types:
            overall_success = self.run_unit_tests() and overall_success
        
        if "integration" in test_types:
            overall_success = self.run_integration_tests() and overall_success
        
        if "security" in test_types:
            overall_success = self.run_security_tests() and overall_success
        
        if "mpob" in test_types:
            overall_success = self.run_mpob_tests() and overall_success
        
        if "authorization" in test_types:
            overall_success = self.run_authorization_tests() and overall_success
        
        if "model_performance" in test_types:
            overall_success = self.run_model_performance_tests() and overall_success
        
        if "performance" in test_types:
            overall_success = self.run_performance_tests() and overall_success
        
        if "specific_files" in test_types:
            overall_success = self.run_specific_test_files() and overall_success
        
        if "all_with_coverage" in test_types:
            overall_success = self.run_all_tests_with_coverage() and overall_success
        
        if "linting" in test_types:
            # Linting failures don't fail the overall test suite
            self.run_linting()
        
        # Generate coverage report
        self.check_test_coverage()
        
        # Generate summary
        summary_success = self.generate_summary_report()
        
        return overall_success and summary_success


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(description="Comprehensive Test Runner for Soil AI Platform")
    parser.add_argument(
        "--test-types",
        nargs="+",
        choices=[
            "unit", "integration", "security", "mpob", "authorization", 
            "model_performance", "performance", "specific_files", 
            "all_with_coverage", "linting"
        ],
        help="Specify which test types to run"
    )
    parser.add_argument(
        "--project-root",
        type=Path,
        default=Path.cwd(),
        help="Project root directory"
    )
    
    args = parser.parse_args()
    
    # Ensure we're in the correct directory
    if not (args.project_root / "app").exists():
        print("❌ Error: app directory not found. Please run from the project root.")
        sys.exit(1)
    
    # Create test runner and run tests
    runner = ComprehensiveTestRunner(args.project_root)
    success = runner.run_comprehensive_tests(args.test_types)
    
    if success:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the results above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
