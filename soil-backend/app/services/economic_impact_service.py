"""
Economic impact analysis service for ROI calculations and investment analysis.

This service implements comprehensive economic impact assessment capabilities
including ROI calculations, NPV analysis, competitiveness metrics, and scenario modeling.
"""

import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
from decimal import Decimal

from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.economic_impact import (
    EconomicImpactAnalysis,
    CompetitivenessMetric,
    InvestmentScenario,
    EconomicIndicator
)
from app.models.user import User
from app.utils.logging import get_logger

logger = get_logger(__name__)


class EconomicImpactService:
    """Economic impact analysis service."""
    
    def __init__(self):
        self.logger = logger
        self.default_discount_rate = 0.08  # 8% discount rate
        self.default_inflation_rate = 0.03  # 3% inflation rate
    
    def calculate_roi(
        self,
        initial_investment: float,
        annual_returns: List[float],
        investment_period_years: int
    ) -> Dict[str, float]:
        """Calculate comprehensive ROI metrics."""
        
        if not annual_returns or investment_period_years <= 0:
            return {"error": "Invalid input parameters"}
        
        # Ensure we have returns for the full period
        if len(annual_returns) < investment_period_years:
            # Extend with the last known return
            last_return = annual_returns[-1] if annual_returns else 0
            annual_returns.extend([last_return] * (investment_period_years - len(annual_returns)))
        
        # Calculate total returns
        total_returns = sum(annual_returns[:investment_period_years])
        
        # Simple ROI
        simple_roi = ((total_returns - initial_investment) / initial_investment) * 100
        
        # Annualized ROI
        annualized_roi = (((total_returns / initial_investment) ** (1 / investment_period_years)) - 1) * 100
        
        # Net Present Value (NPV)
        npv = -initial_investment
        for year, annual_return in enumerate(annual_returns[:investment_period_years], 1):
            npv += annual_return / ((1 + self.default_discount_rate) ** year)
        
        # Internal Rate of Return (IRR) - simplified calculation
        irr = self._calculate_irr(initial_investment, annual_returns[:investment_period_years])
        
        # Payback period
        payback_period = self._calculate_payback_period(initial_investment, annual_returns)
        
        return {
            "simple_roi_percentage": round(simple_roi, 2),
            "annualized_roi_percentage": round(annualized_roi, 2),
            "npv": round(npv, 2),
            "irr_percentage": round(irr, 2) if irr else None,
            "payback_period_years": round(payback_period, 2) if payback_period else None,
            "total_returns": round(total_returns, 2),
            "net_profit": round(total_returns - initial_investment, 2)
        }
    
    def _calculate_irr(self, initial_investment: float, annual_returns: List[float]) -> Optional[float]:
        """Calculate Internal Rate of Return using Newton-Raphson method."""
        
        def npv_function(rate):
            npv = -initial_investment
            for year, cash_flow in enumerate(annual_returns, 1):
                npv += cash_flow / ((1 + rate) ** year)
            return npv
        
        def npv_derivative(rate):
            derivative = 0
            for year, cash_flow in enumerate(annual_returns, 1):
                derivative -= year * cash_flow / ((1 + rate) ** (year + 1))
            return derivative
        
        # Newton-Raphson method
        rate = 0.1  # Initial guess: 10%
        tolerance = 1e-6
        max_iterations = 100
        
        for _ in range(max_iterations):
            npv = npv_function(rate)
            if abs(npv) < tolerance:
                return rate * 100  # Convert to percentage
            
            derivative = npv_derivative(rate)
            if abs(derivative) < tolerance:
                break
            
            rate = rate - npv / derivative
            
            # Ensure rate stays within reasonable bounds
            if rate < -0.99 or rate > 10:
                break
        
        return None  # Could not converge
    
    def _calculate_payback_period(self, initial_investment: float, annual_returns: List[float]) -> Optional[float]:
        """Calculate payback period in years."""
        
        cumulative_returns = 0
        for year, annual_return in enumerate(annual_returns, 1):
            cumulative_returns += annual_return
            if cumulative_returns >= initial_investment:
                # Linear interpolation for more precise payback period
                previous_cumulative = cumulative_returns - annual_return
                remaining_investment = initial_investment - previous_cumulative
                fraction_of_year = remaining_investment / annual_return
                return (year - 1) + fraction_of_year
        
        return None  # Payback period exceeds the analysis period
    
    async def create_economic_impact_analysis(
        self,
        user: User,
        analysis_name: str,
        analysis_type: str,
        investment_amount: float,
        investment_period_years: int,
        baseline_productivity: Optional[float] = None,
        projected_productivity_increase: Optional[float] = None,
        baseline_costs: Optional[float] = None,
        projected_cost_reduction: Optional[float] = None,
        estate_id: Optional[UUID] = None,
        db: AsyncSession = None
    ) -> EconomicImpactAnalysis:
        """Create comprehensive economic impact analysis."""
        
        # Generate annual returns based on productivity improvements and cost reductions
        annual_returns = []
        for year in range(1, investment_period_years + 1):
            # Calculate returns based on productivity gains and cost savings
            productivity_gain = 0
            if baseline_productivity and projected_productivity_increase:
                productivity_gain = baseline_productivity * (projected_productivity_increase / 100) * year * 0.8
            
            cost_savings = 0
            if baseline_costs and projected_cost_reduction:
                cost_savings = baseline_costs * (projected_cost_reduction / 100) * year * 0.6
            
            annual_return = productivity_gain + cost_savings
            annual_returns.append(annual_return)
        
        # Calculate ROI metrics
        roi_metrics = self.calculate_roi(investment_amount, annual_returns, investment_period_years)
        
        # Estimate job creation (simplified model)
        job_creation = int(investment_amount / 50000)  # Assume $50k per job created
        
        # Estimate GDP impact (simplified model)
        gdp_impact = investment_amount * 1.5  # Multiplier effect
        
        # Risk assessment
        risk_factors = [
            "Market volatility",
            "Climate change impacts",
            "Technology adoption resistance",
            "Regulatory changes",
            "Currency fluctuation"
        ]
        
        # Create analysis results
        analysis_results = {
            "roi_metrics": roi_metrics,
            "annual_projections": [
                {
                    "year": year,
                    "projected_return": annual_returns[year - 1],
                    "cumulative_return": sum(annual_returns[:year])
                }
                for year in range(1, investment_period_years + 1)
            ],
            "sensitivity_analysis": {
                "optimistic_scenario": {
                    "roi_increase": 25,
                    "assumptions": "Higher adoption rates, favorable market conditions"
                },
                "pessimistic_scenario": {
                    "roi_decrease": 30,
                    "assumptions": "Lower adoption rates, market challenges"
                }
            }
        }
        
        # Create economic impact analysis record
        analysis = EconomicImpactAnalysis(
            analysis_name=analysis_name,
            analysis_type=analysis_type,
            analysis_scope="estate" if estate_id else "regional",
            investment_amount_usd=Decimal(str(investment_amount)),
            investment_period_years=investment_period_years,
            estate_id=estate_id,
            baseline_productivity=Decimal(str(baseline_productivity)) if baseline_productivity else None,
            projected_productivity_increase=Decimal(str(projected_productivity_increase)) if projected_productivity_increase else None,
            baseline_costs_per_hectare=Decimal(str(baseline_costs)) if baseline_costs else None,
            projected_cost_reduction=Decimal(str(projected_cost_reduction)) if projected_cost_reduction else None,
            roi_percentage=Decimal(str(roi_metrics.get("simple_roi_percentage", 0))),
            npv_usd=Decimal(str(roi_metrics.get("npv", 0))),
            irr_percentage=Decimal(str(roi_metrics.get("irr_percentage", 0))) if roi_metrics.get("irr_percentage") else None,
            payback_period_years=Decimal(str(roi_metrics.get("payback_period_years", 0))) if roi_metrics.get("payback_period_years") else None,
            job_creation_estimate=job_creation,
            gdp_impact_usd=Decimal(str(gdp_impact)),
            risk_factors=risk_factors,
            success_probability=Decimal("75.0"),  # Default 75% success probability
            analysis_results=analysis_results,
            key_findings=[
                f"Projected ROI of {roi_metrics.get('simple_roi_percentage', 0):.1f}% over {investment_period_years} years",
                f"Estimated job creation: {job_creation} positions",
                f"GDP impact: ${gdp_impact:,.0f}",
                f"Payback period: {roi_metrics.get('payback_period_years', 'N/A')} years"
            ],
            recommendations=[
                "Implement phased rollout to minimize risk",
                "Establish monitoring and evaluation framework",
                "Develop stakeholder engagement strategy",
                "Create contingency plans for identified risks"
            ],
            created_by_id=user.id,
            analysis_date=date.today()
        )
        
        db.add(analysis)
        await db.commit()
        await db.refresh(analysis)
        
        self.logger.info(
            "Economic impact analysis created",
            analysis_id=str(analysis.id),
            analysis_name=analysis_name,
            investment_amount=investment_amount,
            roi_percentage=float(analysis.roi_percentage or 0)
        )
        
        return analysis
    
    async def create_investment_scenario(
        self,
        user: User,
        scenario_name: str,
        scenario_type: str,
        total_investment: float,
        timeline_years: int,
        funding_sources: Dict[str, float],
        implementation_phases: List[Dict[str, Any]],
        db: AsyncSession = None
    ) -> InvestmentScenario:
        """Create investment scenario for policy analysis."""
        
        # Generate milestone targets
        milestone_targets = {
            "year_1": {
                "technology_adoption_rate": 15,
                "productivity_increase": 5,
                "job_creation": int(total_investment * 0.1 / 50000)
            },
            "year_3": {
                "technology_adoption_rate": 45,
                "productivity_increase": 15,
                "job_creation": int(total_investment * 0.5 / 50000)
            },
            "year_5": {
                "technology_adoption_rate": 75,
                "productivity_increase": 25,
                "job_creation": int(total_investment / 50000)
            }
        }
        
        # Generate impact projections
        productivity_impact = {
            "baseline_productivity": 18500,  # kg/hectare
            "projected_increase_percentage": 25,
            "affected_area_hectares": 100000,
            "annual_value_increase": total_investment * 0.15
        }
        
        employment_impact = {
            "direct_jobs": int(total_investment / 50000),
            "indirect_jobs": int(total_investment / 25000),
            "job_categories": ["technical", "agricultural", "administrative", "research"],
            "average_salary_increase": 15
        }
        
        environmental_impact = {
            "carbon_footprint_reduction": 20,
            "water_usage_efficiency": 15,
            "biodiversity_improvement": 10,
            "soil_health_enhancement": 25
        }
        
        social_impact = {
            "farmer_income_increase": 20,
            "technology_access_improvement": 40,
            "education_and_training_programs": 50,
            "community_development_projects": 25
        }
        
        # Generate financial projections
        revenue_projections = {}
        cost_projections = {}
        cash_flow_projections = {}
        
        for year in range(1, timeline_years + 1):
            # Revenue grows with adoption and productivity
            revenue = total_investment * 0.2 * year * 0.8
            revenue_projections[f"year_{year}"] = revenue
            
            # Costs decrease over time due to efficiency gains
            cost = total_investment * 0.15 * (1 - (year - 1) * 0.05)
            cost_projections[f"year_{year}"] = cost
            
            # Cash flow
            cash_flow = revenue - cost
            cash_flow_projections[f"year_{year}"] = cash_flow
        
        # Risk assessment
        risk_assessment = {
            "market_risks": {
                "probability": 0.3,
                "impact": "medium",
                "mitigation": "Diversification and market analysis"
            },
            "technology_risks": {
                "probability": 0.2,
                "impact": "high",
                "mitigation": "Pilot testing and gradual rollout"
            },
            "regulatory_risks": {
                "probability": 0.25,
                "impact": "medium",
                "mitigation": "Stakeholder engagement and compliance monitoring"
            }
        }
        
        # Sensitivity analysis
        sensitivity_analysis = {
            "investment_amount": {
                "plus_20_percent": {"roi_change": 15, "npv_change": 25},
                "minus_20_percent": {"roi_change": -18, "npv_change": -22}
            },
            "adoption_rate": {
                "plus_30_percent": {"roi_change": 35, "npv_change": 40},
                "minus_30_percent": {"roi_change": -40, "npv_change": -45}
            }
        }
        
        # Scenario results
        scenario_results = {
            "expected_roi": 22.5,
            "expected_npv": total_investment * 0.3,
            "break_even_year": 3.2,
            "risk_adjusted_return": 18.7
        }
        
        success_metrics = {
            "financial": {"roi_target": 20, "npv_target": total_investment * 0.25},
            "operational": {"adoption_rate_target": 70, "productivity_increase_target": 20},
            "social": {"job_creation_target": int(total_investment / 50000), "income_increase_target": 15}
        }
        
        # Create investment scenario
        scenario = InvestmentScenario(
            scenario_name=scenario_name,
            scenario_type=scenario_type,
            total_investment_usd=Decimal(str(total_investment)),
            investment_timeline_years=timeline_years,
            funding_sources=funding_sources,
            implementation_phases=implementation_phases,
            milestone_targets=milestone_targets,
            discount_rate=Decimal(str(self.default_discount_rate)),
            inflation_rate=Decimal(str(self.default_inflation_rate)),
            productivity_impact=productivity_impact,
            employment_impact=employment_impact,
            environmental_impact=environmental_impact,
            social_impact=social_impact,
            revenue_projections=revenue_projections,
            cost_projections=cost_projections,
            cash_flow_projections=cash_flow_projections,
            risk_assessment=risk_assessment,
            sensitivity_analysis=sensitivity_analysis,
            scenario_results=scenario_results,
            success_metrics=success_metrics,
            created_by_id=user.id
        )
        
        db.add(scenario)
        await db.commit()
        await db.refresh(scenario)
        
        self.logger.info(
            "Investment scenario created",
            scenario_id=str(scenario.id),
            scenario_name=scenario_name,
            total_investment=total_investment,
            expected_roi=scenario_results["expected_roi"]
        )
        
        return scenario
    
    async def get_competitiveness_analysis(
        self,
        country: str = "Malaysia",
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get competitiveness analysis for a country."""
        
        # Get latest competitiveness metrics
        query = select(CompetitivenessMetric).where(
            CompetitivenessMetric.country == country
        ).order_by(desc(CompetitivenessMetric.measurement_date))
        
        result = await db.execute(query)
        metrics = result.scalars().all()
        
        if not metrics:
            # Return mock data for demonstration
            return self._generate_mock_competitiveness_data(country)
        
        # Process metrics by category
        metrics_by_category = {}
        for metric in metrics:
            category = metric.metric_category
            if category not in metrics_by_category:
                metrics_by_category[category] = []
            
            metrics_by_category[category].append({
                "metric_name": metric.metric_name,
                "current_value": float(metric.current_value),
                "global_rank": metric.global_rank,
                "regional_rank": metric.regional_rank,
                "trend_direction": metric.trend_direction,
                "year_over_year_change": float(metric.year_over_year_change or 0)
            })
        
        return {
            "country": country,
            "analysis_date": date.today().isoformat(),
            "metrics_by_category": metrics_by_category,
            "overall_ranking": self._calculate_overall_ranking(metrics),
            "key_strengths": self._identify_key_strengths(metrics),
            "improvement_areas": self._identify_improvement_areas(metrics)
        }
    
    def _generate_mock_competitiveness_data(self, country: str) -> Dict[str, Any]:
        """Generate mock competitiveness data for demonstration."""
        
        return {
            "country": country,
            "analysis_date": date.today().isoformat(),
            "metrics_by_category": {
                "productivity": [
                    {
                        "metric_name": "Yield per Hectare",
                        "current_value": 18.5,
                        "global_rank": 3,
                        "regional_rank": 2,
                        "trend_direction": "improving",
                        "year_over_year_change": 4.2
                    }
                ],
                "sustainability": [
                    {
                        "metric_name": "Sustainability Certification Rate",
                        "current_value": 68.2,
                        "global_rank": 5,
                        "regional_rank": 2,
                        "trend_direction": "improving",
                        "year_over_year_change": 8.5
                    }
                ],
                "technology": [
                    {
                        "metric_name": "Technology Adoption Rate",
                        "current_value": 45.3,
                        "global_rank": 8,
                        "regional_rank": 3,
                        "trend_direction": "improving",
                        "year_over_year_change": 12.1
                    }
                ]
            },
            "overall_ranking": {
                "global_rank": 3,
                "regional_rank": 2,
                "total_countries_assessed": 45
            },
            "key_strengths": [
                "High productivity per hectare",
                "Strong regulatory framework",
                "Advanced research capabilities",
                "Strategic geographic location"
            ],
            "improvement_areas": [
                "Technology adoption acceleration",
                "Smallholder integration",
                "Supply chain optimization",
                "Digital transformation"
            ]
        }
    
    def _calculate_overall_ranking(self, metrics: List) -> Dict[str, Any]:
        """Calculate overall ranking from individual metrics."""
        
        if not metrics:
            return {"global_rank": None, "regional_rank": None}
        
        # Simple average of rankings (weighted by importance could be added)
        global_ranks = [m.global_rank for m in metrics if m.global_rank]
        regional_ranks = [m.regional_rank for m in metrics if m.regional_rank]
        
        avg_global_rank = sum(global_ranks) / len(global_ranks) if global_ranks else None
        avg_regional_rank = sum(regional_ranks) / len(regional_ranks) if regional_ranks else None
        
        return {
            "global_rank": int(avg_global_rank) if avg_global_rank else None,
            "regional_rank": int(avg_regional_rank) if avg_regional_rank else None,
            "total_countries_assessed": max([m.total_countries for m in metrics if m.total_countries], default=None)
        }
    
    def _identify_key_strengths(self, metrics: List) -> List[str]:
        """Identify key strengths based on rankings."""
        
        strengths = []
        for metric in metrics:
            if metric.global_rank and metric.global_rank <= 5:
                strengths.append(f"Top 5 globally in {metric.metric_name}")
            elif metric.trend_direction == "improving" and metric.year_over_year_change and metric.year_over_year_change > 5:
                strengths.append(f"Strong improvement in {metric.metric_name}")
        
        return strengths[:4]  # Return top 4 strengths
    
    def _identify_improvement_areas(self, metrics: List) -> List[str]:
        """Identify areas for improvement based on rankings."""
        
        improvement_areas = []
        for metric in metrics:
            if metric.global_rank and metric.global_rank > 10:
                improvement_areas.append(f"Enhance {metric.metric_name}")
            elif metric.trend_direction == "declining":
                improvement_areas.append(f"Address declining {metric.metric_name}")
        
        return improvement_areas[:4]  # Return top 4 improvement areas


# Global service instance
economic_impact_service = EconomicImpactService()
