"""
Enhanced security service for enterprise-grade authentication and audit logging.

This service implements multi-factor authentication, comprehensive audit logging,
encryption management, and GDPR compliance features.
"""

import secrets
import hashlib
import pyotp
import qrcode
from io import BytesIO
import base64
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.models.user import User
from app.models.security import (
    UserMFA, MFAVerificationAttempt, SecurityAuditLog, LoginAttempt,
    DataEncryption, GDPRDataRequest, AuditEventType, MFAMethod
)
from app.core.config import settings
from app.utils.logging import get_logger

logger = get_logger(__name__)


class SecurityService:
    """Enhanced security service for enterprise features."""
    
    def __init__(self):
        self.logger = logger
        self._encryption_key = self._derive_encryption_key()
        self._cipher_suite = Fernet(self._encryption_key)
    
    def _derive_encryption_key(self) -> bytes:
        """Derive encryption key from settings."""
        password = settings.SECRET_KEY.encode()
        salt = b'soil_ai_salt_2024'  # In production, use a proper salt management system
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    async def setup_mfa_totp(self, user: User, db: AsyncSession) -> Dict[str, Any]:
        """Set up TOTP-based MFA for a user."""
        
        # Generate TOTP secret
        secret = pyotp.random_base32()
        
        # Create or update MFA settings
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        if not mfa_settings:
            mfa_settings = UserMFA(user_id=user.id)
            db.add(mfa_settings)
        
        mfa_settings.totp_secret = secret
        mfa_settings.totp_verified = False
        
        await db.commit()
        
        # Generate QR code
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user.email,
            issuer_name="Soil AI Platform"
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        await self.log_security_event(
            user_id=user.id,
            event_type=AuditEventType.MFA_ENABLED,
            description="TOTP MFA setup initiated",
            success=True,
            db=db
        )
        
        return {
            "secret": secret,
            "qr_code": qr_code_base64,
            "manual_entry_key": secret,
            "provisioning_uri": provisioning_uri
        }
    
    async def verify_totp(self, user: User, token: str, db: AsyncSession) -> bool:
        """Verify TOTP token for MFA."""
        
        # Get MFA settings
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        if not mfa_settings or not mfa_settings.totp_secret:
            await self._log_mfa_attempt(user.id, MFAMethod.TOTP, False, "No TOTP secret configured", db)
            return False
        
        # Verify token
        totp = pyotp.TOTP(mfa_settings.totp_secret)
        is_valid = totp.verify(token, valid_window=1)  # Allow 1 window tolerance
        
        if is_valid:
            # Mark as verified if this is the first successful verification
            if not mfa_settings.totp_verified:
                mfa_settings.totp_verified = True
                mfa_settings.is_enabled = True
                mfa_settings.primary_method = MFAMethod.TOTP
                mfa_settings.enabled_at = datetime.utcnow()
            
            mfa_settings.last_verification = datetime.utcnow()
            await db.commit()
            
            await self._log_mfa_attempt(user.id, MFAMethod.TOTP, True, None, db)
            await self.log_security_event(
                user_id=user.id,
                event_type=AuditEventType.MFA_VERIFICATION_SUCCESS,
                description="TOTP verification successful",
                success=True,
                db=db
            )
        else:
            await self._log_mfa_attempt(user.id, MFAMethod.TOTP, False, "Invalid token", db)
            await self.log_security_event(
                user_id=user.id,
                event_type=AuditEventType.MFA_VERIFICATION_FAILURE,
                description="TOTP verification failed",
                success=False,
                failure_reason="Invalid token",
                db=db
            )
        
        return is_valid
    
    async def generate_backup_codes(self, user: User, db: AsyncSession) -> List[str]:
        """Generate backup codes for MFA recovery."""
        
        # Generate 10 backup codes
        backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]
        
        # Hash the codes for storage
        hashed_codes = [hashlib.sha256(code.encode()).hexdigest() for code in backup_codes]
        
        # Update MFA settings
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        if not mfa_settings:
            mfa_settings = UserMFA(user_id=user.id)
            db.add(mfa_settings)
        
        mfa_settings.backup_codes = hashed_codes
        mfa_settings.backup_codes_used = []
        
        await db.commit()
        
        await self.log_security_event(
            user_id=user.id,
            event_type=AuditEventType.MFA_ENABLED,
            description="Backup codes generated",
            success=True,
            db=db
        )
        
        return backup_codes
    
    async def verify_backup_code(self, user: User, code: str, db: AsyncSession) -> bool:
        """Verify backup code for MFA recovery."""
        
        # Get MFA settings
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        if not mfa_settings or not mfa_settings.backup_codes:
            await self._log_mfa_attempt(user.id, MFAMethod.BACKUP_CODES, False, "No backup codes configured", db)
            return False
        
        # Hash the provided code
        code_hash = hashlib.sha256(code.upper().encode()).hexdigest()
        
        # Check if code exists and hasn't been used
        if code_hash in mfa_settings.backup_codes and code_hash not in (mfa_settings.backup_codes_used or []):
            # Mark code as used
            if not mfa_settings.backup_codes_used:
                mfa_settings.backup_codes_used = []
            mfa_settings.backup_codes_used.append(code_hash)
            mfa_settings.last_verification = datetime.utcnow()
            
            await db.commit()
            
            await self._log_mfa_attempt(user.id, MFAMethod.BACKUP_CODES, True, None, db)
            await self.log_security_event(
                user_id=user.id,
                event_type=AuditEventType.MFA_VERIFICATION_SUCCESS,
                description="Backup code verification successful",
                success=True,
                db=db
            )
            return True
        else:
            await self._log_mfa_attempt(user.id, MFAMethod.BACKUP_CODES, False, "Invalid or used backup code", db)
            await self.log_security_event(
                user_id=user.id,
                event_type=AuditEventType.MFA_VERIFICATION_FAILURE,
                description="Backup code verification failed",
                success=False,
                failure_reason="Invalid or used backup code",
                db=db
            )
            return False
    
    async def disable_mfa(self, user: User, db: AsyncSession) -> bool:
        """Disable MFA for a user."""
        
        # Get MFA settings
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        if mfa_settings:
            mfa_settings.is_enabled = False
            mfa_settings.totp_secret = None
            mfa_settings.totp_verified = False
            mfa_settings.backup_codes = None
            mfa_settings.backup_codes_used = None
            mfa_settings.primary_method = None
            
            await db.commit()
            
            await self.log_security_event(
                user_id=user.id,
                event_type=AuditEventType.MFA_DISABLED,
                description="MFA disabled",
                success=True,
                db=db
            )
            return True
        
        return False
    
    async def is_mfa_enabled(self, user: User, db: AsyncSession) -> bool:
        """Check if MFA is enabled for a user."""
        
        mfa_query = select(UserMFA).where(UserMFA.user_id == user.id)
        result = await db.execute(mfa_query)
        mfa_settings = result.scalar_one_or_none()
        
        return mfa_settings and mfa_settings.is_enabled
    
    async def log_security_event(
        self,
        event_type: AuditEventType,
        description: str,
        success: bool,
        user_id: Optional[UUID] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        event_data: Optional[Dict[str, Any]] = None,
        severity_level: str = "medium",
        failure_reason: Optional[str] = None,
        db: AsyncSession = None
    ):
        """Log a security event for audit purposes."""
        
        if not db:
            return
        
        audit_log = SecurityAuditLog(
            user_id=user_id,
            event_type=event_type,
            event_description=description,
            event_data=event_data,
            ip_address=ip_address,
            user_agent=user_agent,
            resource_type=resource_type,
            resource_id=resource_id,
            severity_level=severity_level,
            success=success,
            failure_reason=failure_reason
        )
        
        db.add(audit_log)
        await db.commit()
        
        self.logger.info(
            "Security event logged",
            event_type=event_type.value,
            user_id=str(user_id) if user_id else None,
            success=success,
            severity=severity_level
        )
    
    async def _log_mfa_attempt(
        self,
        user_id: UUID,
        method: MFAMethod,
        success: bool,
        failure_reason: Optional[str],
        db: AsyncSession,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log MFA verification attempt."""
        
        attempt = MFAVerificationAttempt(
            user_id=user_id,
            method_used=method,
            success=success,
            failure_reason=failure_reason,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        db.add(attempt)
        await db.commit()
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data using AES-256."""
        encrypted_data = self._cipher_suite.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            self.logger.error("Failed to decrypt data", error=str(e))
            raise ValueError("Failed to decrypt data")
    
    async def create_gdpr_data_request(
        self,
        user: User,
        request_type: str,
        description: Optional[str],
        requested_data_types: Optional[List[str]],
        db: AsyncSession
    ) -> GDPRDataRequest:
        """Create a GDPR/PDPA data request."""
        
        gdpr_request = GDPRDataRequest(
            user_id=user.id,
            request_type=request_type,
            description=description,
            requested_data_types=requested_data_types
        )
        
        db.add(gdpr_request)
        await db.commit()
        await db.refresh(gdpr_request)
        
        await self.log_security_event(
            user_id=user.id,
            event_type=AuditEventType.DATA_ACCESS,
            description=f"GDPR data request created: {request_type}",
            success=True,
            event_data={"request_id": str(gdpr_request.id), "request_type": request_type},
            db=db
        )
        
        return gdpr_request


# Global security service instance
security_service = SecurityService()
