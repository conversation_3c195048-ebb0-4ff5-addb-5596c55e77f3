"""
Enhanced model performance monitoring and validation service.

This service implements comprehensive model performance tracking,
accuracy validation, and prediction feedback loops as required by the PRD.
"""

import json
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
from decimal import Decimal

from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

from app.models.model_performance import (
    ModelPerformanceMetric,
    PredictionFeedback,
    ModelAccuracyValidation,
    SHAPExplanationRecord
)
from app.models.user import User
from app.utils.logging import get_logger

logger = get_logger(__name__)


class ModelPerformanceService:
    """Enhanced model performance monitoring and validation service."""
    
    def __init__(self):
        self.logger = logger
        self.accuracy_target_range = (0.85, 0.95)  # 85-95% accuracy target
        self.latency_target_ms = 500  # <500ms latency target
    
    async def record_model_performance(
        self,
        model_name: str,
        model_version: str,
        model_type: str,
        predictions: List[Dict[str, float]],
        actual_values: List[Dict[str, float]],
        confidence_scores: List[Dict[str, float]],
        latencies_ms: List[float],
        evaluation_period_start: datetime,
        evaluation_period_end: datetime,
        db: AsyncSession
    ) -> ModelPerformanceMetric:
        """Record comprehensive model performance metrics."""
        
        # Calculate accuracy metrics
        accuracy_metrics = self._calculate_accuracy_metrics(predictions, actual_values)
        
        # Calculate confidence metrics
        confidence_metrics = self._calculate_confidence_metrics(confidence_scores)
        
        # Calculate latency metrics
        latency_metrics = self._calculate_latency_metrics(latencies_ms)
        
        # Check target compliance
        meets_accuracy_target = (
            self.accuracy_target_range[0] <= accuracy_metrics["r2"] <= self.accuracy_target_range[1]
        )
        meets_latency_target = latency_metrics["avg_latency"] < self.latency_target_ms
        
        # Create performance metric record
        performance_metric = ModelPerformanceMetric(
            model_name=model_name,
            model_version=model_version,
            model_type=model_type,
            accuracy_r2=Decimal(str(accuracy_metrics["r2"])),
            rmse=Decimal(str(accuracy_metrics["rmse"])),
            mae=Decimal(str(accuracy_metrics["mae"])),
            mape=Decimal(str(accuracy_metrics["mape"])),
            avg_confidence_score=Decimal(str(confidence_metrics["avg_confidence"])),
            confidence_distribution=confidence_metrics["distribution"],
            meets_accuracy_target=meets_accuracy_target,
            accuracy_target_range="85-95%",
            avg_latency_ms=Decimal(str(latency_metrics["avg_latency"])),
            p95_latency_ms=Decimal(str(latency_metrics["p95_latency"])),
            p99_latency_ms=Decimal(str(latency_metrics["p99_latency"])),
            meets_latency_target=meets_latency_target,
            prediction_count=len(predictions),
            error_count=0,  # Would be calculated from actual errors
            error_rate=Decimal("0.0"),
            evaluation_date=date.today(),
            evaluation_period_start=evaluation_period_start,
            evaluation_period_end=evaluation_period_end,
            evaluation_dataset_size=len(predictions)
        )
        
        db.add(performance_metric)
        await db.commit()
        await db.refresh(performance_metric)
        
        self.logger.info(
            "Model performance recorded",
            model_name=model_name,
            model_version=model_version,
            r2_score=accuracy_metrics["r2"],
            meets_accuracy_target=meets_accuracy_target,
            avg_latency_ms=latency_metrics["avg_latency"],
            meets_latency_target=meets_latency_target
        )
        
        return performance_metric
    
    def _calculate_accuracy_metrics(
        self,
        predictions: List[Dict[str, float]],
        actual_values: List[Dict[str, float]]
    ) -> Dict[str, float]:
        """Calculate comprehensive accuracy metrics."""
        
        if not predictions or not actual_values or len(predictions) != len(actual_values):
            return {"r2": 0.0, "rmse": float("inf"), "mae": float("inf"), "mape": float("inf")}
        
        # Get all parameter names
        param_names = set()
        for pred in predictions:
            param_names.update(pred.keys())
        for actual in actual_values:
            param_names.update(actual.keys())
        
        # Calculate metrics for each parameter and overall
        all_predictions = []
        all_actuals = []
        
        for param in param_names:
            param_predictions = [pred.get(param, 0.0) for pred in predictions]
            param_actuals = [actual.get(param, 0.0) for actual in actual_values]
            
            all_predictions.extend(param_predictions)
            all_actuals.extend(param_actuals)
        
        # Calculate overall metrics
        r2 = r2_score(all_actuals, all_predictions)
        rmse = np.sqrt(mean_squared_error(all_actuals, all_predictions))
        mae = mean_absolute_error(all_actuals, all_predictions)
        
        # Calculate MAPE (Mean Absolute Percentage Error)
        mape = np.mean(np.abs((np.array(all_actuals) - np.array(all_predictions)) / 
                             np.maximum(np.abs(all_actuals), 1e-8))) * 100
        
        return {
            "r2": float(r2),
            "rmse": float(rmse),
            "mae": float(mae),
            "mape": float(mape)
        }
    
    def _calculate_confidence_metrics(
        self,
        confidence_scores: List[Dict[str, float]]
    ) -> Dict[str, Any]:
        """Calculate confidence score metrics."""
        
        if not confidence_scores:
            return {"avg_confidence": 0.0, "distribution": {}}
        
        # Flatten all confidence scores
        all_confidences = []
        for conf_dict in confidence_scores:
            all_confidences.extend(conf_dict.values())
        
        if not all_confidences:
            return {"avg_confidence": 0.0, "distribution": {}}
        
        avg_confidence = np.mean(all_confidences)
        
        # Calculate distribution
        high_conf = sum(1 for c in all_confidences if c >= 0.8) / len(all_confidences)
        medium_conf = sum(1 for c in all_confidences if 0.6 <= c < 0.8) / len(all_confidences)
        low_conf = sum(1 for c in all_confidences if c < 0.6) / len(all_confidences)
        
        return {
            "avg_confidence": float(avg_confidence),
            "distribution": {
                "high": float(high_conf),
                "medium": float(medium_conf),
                "low": float(low_conf)
            }
        }
    
    def _calculate_latency_metrics(self, latencies_ms: List[float]) -> Dict[str, float]:
        """Calculate latency metrics."""
        
        if not latencies_ms:
            return {"avg_latency": 0.0, "p95_latency": 0.0, "p99_latency": 0.0}
        
        avg_latency = np.mean(latencies_ms)
        p95_latency = np.percentile(latencies_ms, 95)
        p99_latency = np.percentile(latencies_ms, 99)
        
        return {
            "avg_latency": float(avg_latency),
            "p95_latency": float(p95_latency),
            "p99_latency": float(p99_latency)
        }
    
    async def record_prediction_feedback(
        self,
        user: User,
        prediction_id: str,
        model_name: str,
        model_version: str,
        predicted_values: Dict[str, float],
        feedback_type: str,
        feedback_rating: Optional[int] = None,
        corrected_values: Optional[Dict[str, float]] = None,
        actual_measured_values: Optional[Dict[str, float]] = None,
        feedback_text: Optional[str] = None,
        confidence_scores: Optional[Dict[str, float]] = None,
        estate_id: Optional[UUID] = None,
        db: AsyncSession = None
    ) -> PredictionFeedback:
        """Record user feedback on predictions."""
        
        feedback = PredictionFeedback(
            user_id=user.id,
            estate_id=estate_id,
            prediction_id=prediction_id,
            model_name=model_name,
            model_version=model_version,
            predicted_values=predicted_values,
            confidence_scores=confidence_scores,
            feedback_type=feedback_type,
            feedback_rating=feedback_rating,
            corrected_values=corrected_values,
            actual_measured_values=actual_measured_values,
            feedback_text=feedback_text
        )
        
        db.add(feedback)
        await db.commit()
        await db.refresh(feedback)
        
        self.logger.info(
            "Prediction feedback recorded",
            prediction_id=prediction_id,
            user_id=str(user.id),
            feedback_type=feedback_type,
            feedback_rating=feedback_rating
        )
        
        return feedback
    
    async def validate_model_accuracy(
        self,
        model_name: str,
        model_version: str,
        validation_type: str,
        validation_dataset: List[Tuple[Dict[str, float], Dict[str, float]]],  # (features, targets)
        model_predict_func: callable,
        db: AsyncSession
    ) -> ModelAccuracyValidation:
        """Perform comprehensive model accuracy validation."""
        
        # Extract features and targets
        features_list = [item[0] for item in validation_dataset]
        targets_list = [item[1] for item in validation_dataset]
        
        # Get predictions
        predictions_list = []
        for features in features_list:
            try:
                prediction = model_predict_func(features)
                predictions_list.append(prediction)
            except Exception as e:
                self.logger.error(f"Prediction failed for validation: {e}")
                predictions_list.append({})
        
        # Calculate accuracy metrics
        accuracy_metrics = self._calculate_accuracy_metrics(predictions_list, targets_list)
        
        # Calculate parameter-specific accuracies
        parameter_accuracies = {}
        param_names = set()
        for target in targets_list:
            param_names.update(target.keys())
        
        for param in param_names:
            param_predictions = [pred.get(param, 0.0) for pred in predictions_list]
            param_targets = [target.get(param, 0.0) for target in targets_list]
            
            if param_predictions and param_targets:
                param_r2 = r2_score(param_targets, param_predictions)
                parameter_accuracies[param] = float(param_r2)
        
        # Check if meets 85-95% target
        overall_accuracy = accuracy_metrics["r2"]
        meets_target = self.accuracy_target_range[0] <= overall_accuracy <= self.accuracy_target_range[1]
        
        # Create validation record
        validation = ModelAccuracyValidation(
            model_name=model_name,
            model_version=model_version,
            validation_type=validation_type,
            validation_dataset_size=len(validation_dataset),
            validation_method="r2_score_validation",
            overall_accuracy=Decimal(str(overall_accuracy)),
            parameter_accuracies=parameter_accuracies,
            r2_score=Decimal(str(accuracy_metrics["r2"])),
            rmse=Decimal(str(accuracy_metrics["rmse"])),
            mae=Decimal(str(accuracy_metrics["mae"])),
            meets_85_95_target=meets_target,
            accuracy_percentile=Decimal(str(overall_accuracy * 100)),
            results_by_parameter=parameter_accuracies,
            validation_date=date.today(),
            validation_status="completed"
        )
        
        db.add(validation)
        await db.commit()
        await db.refresh(validation)
        
        self.logger.info(
            "Model accuracy validation completed",
            model_name=model_name,
            model_version=model_version,
            overall_accuracy=overall_accuracy,
            meets_target=meets_target,
            validation_dataset_size=len(validation_dataset)
        )
        
        return validation
    
    async def record_shap_explanation(
        self,
        prediction_id: str,
        model_name: str,
        model_version: str,
        feature_importance: Dict[str, float],
        shap_values: Dict[str, Any],
        base_values: Dict[str, float],
        input_features: Dict[str, float],
        prediction_output: Dict[str, float],
        explanation_confidence: float,
        user_id: Optional[UUID] = None,
        explanation_time_ms: Optional[float] = None,
        db: AsyncSession = None
    ) -> SHAPExplanationRecord:
        """Record SHAP explanation for a prediction."""
        
        # Extract top positive and negative features
        sorted_features = sorted(feature_importance.items(), key=lambda x: abs(x[1]), reverse=True)
        top_positive = [{"feature": k, "importance": v} for k, v in sorted_features if v > 0][:5]
        top_negative = [{"feature": k, "importance": v} for k, v in sorted_features if v < 0][:5]
        
        explanation_record = SHAPExplanationRecord(
            prediction_id=prediction_id,
            model_name=model_name,
            model_version=model_version,
            feature_importance=feature_importance,
            shap_values=shap_values,
            base_values=base_values,
            top_positive_features=top_positive,
            top_negative_features=top_negative,
            explanation_confidence=Decimal(str(explanation_confidence)),
            user_id=user_id,
            input_features=input_features,
            prediction_output=prediction_output,
            explanation_time_ms=Decimal(str(explanation_time_ms)) if explanation_time_ms else None
        )
        
        db.add(explanation_record)
        await db.commit()
        await db.refresh(explanation_record)
        
        self.logger.info(
            "SHAP explanation recorded",
            prediction_id=prediction_id,
            model_name=model_name,
            explanation_confidence=explanation_confidence,
            top_features_count=len(top_positive) + len(top_negative)
        )
        
        return explanation_record
    
    async def get_model_performance_summary(
        self,
        model_name: Optional[str] = None,
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get model performance summary."""
        
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = select(ModelPerformanceMetric).where(
            ModelPerformanceMetric.evaluation_date >= start_date
        )
        
        if model_name:
            query = query.where(ModelPerformanceMetric.model_name == model_name)
        
        query = query.order_by(desc(ModelPerformanceMetric.evaluation_date))
        
        result = await db.execute(query)
        metrics = result.scalars().all()
        
        if not metrics:
            return {"error": "No performance data available"}
        
        # Calculate summary statistics
        latest_metric = metrics[0]
        avg_accuracy = np.mean([float(m.accuracy_r2) for m in metrics if m.accuracy_r2])
        avg_latency = np.mean([float(m.avg_latency_ms) for m in metrics if m.avg_latency_ms])
        
        accuracy_trend = "stable"
        if len(metrics) > 1:
            recent_accuracy = float(metrics[0].accuracy_r2 or 0)
            older_accuracy = float(metrics[-1].accuracy_r2 or 0)
            if recent_accuracy > older_accuracy + 0.02:
                accuracy_trend = "improving"
            elif recent_accuracy < older_accuracy - 0.02:
                accuracy_trend = "declining"
        
        return {
            "model_name": latest_metric.model_name,
            "model_version": latest_metric.model_version,
            "current_accuracy": float(latest_metric.accuracy_r2 or 0),
            "average_accuracy_30d": avg_accuracy,
            "meets_accuracy_target": latest_metric.meets_accuracy_target,
            "current_latency_ms": float(latest_metric.avg_latency_ms or 0),
            "average_latency_30d": avg_latency,
            "meets_latency_target": latest_metric.meets_latency_target,
            "accuracy_trend": accuracy_trend,
            "total_predictions": sum(m.prediction_count for m in metrics),
            "error_rate": float(latest_metric.error_rate or 0),
            "last_evaluation": latest_metric.evaluation_date,
            "evaluation_count": len(metrics)
        }


# Global service instance
model_performance_service = ModelPerformanceService()
