"""
API v1 package containing all version 1 endpoints.

This package organizes API endpoints by functional domain and provides
a centralized router for all v1 endpoints.
"""

from fastapi import APIRouter

from app.api.v1 import (
    auth, estates, sensors, predictions, analytics, admin, chat, demo, roi, dashboard,
    authorization, mpob_compliance, security, model_performance, economic_impact,
    performance, spatial_analytics
)

# Create the main API router for v1
api_router = APIRouter()

# Include all domain routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    estates.router,
    prefix="/estates",
    tags=["Estate Management"]
)

api_router.include_router(
    sensors.router,
    prefix="/sensors",
    tags=["Sensor Management"]
)

api_router.include_router(
    predictions.router,
    prefix="/predictions",
    tags=["AI Predictions"]
)

api_router.include_router(
    analytics.router,
    prefix="/analytics",
    tags=["Analytics"]
)

api_router.include_router(
    admin.router,
    prefix="/admin",
    tags=["Administration"]
)

api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["AI Chat"]
)

api_router.include_router(
    demo.router,
    prefix="/demo",
    tags=["Demo Management"]
)

api_router.include_router(
    roi.router,
    prefix="/roi",
    tags=["ROI Analysis"]
)

api_router.include_router(
    dashboard.router,
    prefix="/dashboard",
    tags=["Dashboard"]
)

api_router.include_router(
    authorization.router,
    prefix="/authorization",
    tags=["Authorization System"]
)

api_router.include_router(
    mpob_compliance.router,
    prefix="/mpob-compliance",
    tags=["MPOB Compliance"]
)

api_router.include_router(
    security.router,
    prefix="/security",
    tags=["Security & MFA"]
)

api_router.include_router(
    model_performance.router,
    prefix="/model-performance",
    tags=["Model Performance"]
)

api_router.include_router(
    economic_impact.router,
    prefix="/economic-impact",
    tags=["Economic Impact Analysis"]
)

api_router.include_router(
    performance.router,
    prefix="/performance",
    tags=["Performance Monitoring"]
)

api_router.include_router(
    spatial_analytics.router,
    prefix="/spatial-analytics",
    tags=["Enhanced Spatial Analytics"]
)
