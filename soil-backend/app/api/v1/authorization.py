"""
Authorization management API endpoints for General Staff dynamic permissions system.

This module implements the authorization request/grant workflows, time-limited permissions,
and audit trails as specified in the PRD.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.authorization import AuthorizationRequest, Authorization, AuthorizationAuditLog
from app.schemas.authorization import (
    AuthorizationRequestCreate,
    AuthorizationRequestResponse,
    AuthorizationRequestInfo,
    AuthorizationCreate,
    AuthorizationInfo,
    AuthorizationRevoke,
    AuthorizationUsage,
    AuthorizationAuditLogInfo,
    AuthorizationSummary,
    AvailableDataEntryForms
)
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/authorization", tags=["Authorization Management"])


@router.post("/requests", response_model=AuthorizationRequestInfo)
async def create_authorization_request(
    request_data: AuthorizationRequestCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new authorization request from General Staff to a primary persona."""
    
    # Verify user is General Staff
    if current_user.role != "general_staff":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only General Staff can create authorization requests"
        )
    
    # Verify target persona exists and has appropriate role
    target_persona_query = select(User).where(User.id == request_data.target_persona_id)
    result = await db.execute(target_persona_query)
    target_persona = result.scalar_one_or_none()
    
    if not target_persona:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Target persona not found"
        )
    
    if target_persona.role not in ["decision_maker", "manager", "agronomist", "researcher"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Target persona must be a primary persona (decision_maker, manager, agronomist, researcher)"
        )
    
    # Create authorization request
    auth_request = AuthorizationRequest(
        requester_id=current_user.id,
        target_persona_id=request_data.target_persona_id,
        estate_id=request_data.estate_id,
        data_entry_type=request_data.data_entry_type,
        permission_level=request_data.permission_level,
        justification=request_data.justification,
        requested_duration_days=request_data.requested_duration_days
    )
    
    db.add(auth_request)
    await db.commit()
    await db.refresh(auth_request)
    
    # Log the request creation
    audit_log = AuthorizationAuditLog(
        user_id=current_user.id,
        action="request_created",
        details={
            "request_id": str(auth_request.id),
            "target_persona_id": str(request_data.target_persona_id),
            "data_entry_type": request_data.data_entry_type,
            "permission_level": request_data.permission_level,
            "requested_duration_days": request_data.requested_duration_days
        }
    )
    db.add(audit_log)
    await db.commit()
    
    logger.info(
        "Authorization request created",
        request_id=str(auth_request.id),
        requester_id=str(current_user.id),
        target_persona_id=str(request_data.target_persona_id)
    )
    
    return AuthorizationRequestInfo.from_orm(auth_request)


@router.get("/requests", response_model=List[AuthorizationRequestInfo])
async def get_authorization_requests(
    status_filter: Optional[str] = Query(None, regex="^(pending|approved|rejected|expired)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get authorization requests for the current user."""
    
    query = select(AuthorizationRequest).options(
        selectinload(AuthorizationRequest.requester),
        selectinload(AuthorizationRequest.target_persona),
        selectinload(AuthorizationRequest.responded_by)
    )
    
    if current_user.role == "general_staff":
        # General Staff sees their own requests
        query = query.where(AuthorizationRequest.requester_id == current_user.id)
    elif current_user.role in ["decision_maker", "manager", "agronomist", "researcher"]:
        # Primary personas see requests directed to them
        query = query.where(AuthorizationRequest.target_persona_id == current_user.id)
    else:
        # Admins see all requests
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
    
    if status_filter:
        query = query.where(AuthorizationRequest.status == status_filter)
    
    query = query.order_by(AuthorizationRequest.created_at.desc())
    
    result = await db.execute(query)
    requests = result.scalars().all()
    
    return [AuthorizationRequestInfo.from_orm(req) for req in requests]


@router.post("/requests/{request_id}/respond", response_model=AuthorizationRequestInfo)
async def respond_to_authorization_request(
    request_id: UUID,
    response_data: AuthorizationRequestResponse,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Respond to an authorization request (approve or reject)."""
    
    # Get the authorization request
    query = select(AuthorizationRequest).where(AuthorizationRequest.id == request_id)
    result = await db.execute(query)
    auth_request = result.scalar_one_or_none()
    
    if not auth_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Authorization request not found"
        )
    
    # Verify user can respond to this request
    if auth_request.target_persona_id != current_user.id and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only respond to requests directed to you"
        )
    
    # Verify request is still pending
    if auth_request.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Request has already been responded to"
        )
    
    # Update the request
    auth_request.status = response_data.status
    auth_request.response_message = response_data.response_message
    auth_request.responded_at = datetime.utcnow()
    auth_request.responded_by_id = current_user.id
    
    # If approved, create the authorization
    if response_data.status == "approved":
        expires_at = datetime.utcnow() + timedelta(days=response_data.approved_duration_days)
        
        authorization = Authorization(
            general_staff_user_id=auth_request.requester_id,
            authorizing_persona_id=current_user.id,
            estate_id=auth_request.estate_id,
            authorizing_persona=current_user.role,
            data_entry_type=auth_request.data_entry_type,
            permission_level=auth_request.permission_level,
            expires_at=expires_at,
            conditions=response_data.conditions
        )
        
        db.add(authorization)
        
        # Log authorization granted
        audit_log = AuthorizationAuditLog(
            authorization_id=authorization.id,
            user_id=current_user.id,
            action="authorization_granted",
            details={
                "request_id": str(request_id),
                "general_staff_user_id": str(auth_request.requester_id),
                "data_entry_type": auth_request.data_entry_type,
                "permission_level": auth_request.permission_level,
                "expires_at": expires_at.isoformat(),
                "approved_duration_days": response_data.approved_duration_days
            }
        )
        db.add(audit_log)
    
    # Log the response
    audit_log = AuthorizationAuditLog(
        user_id=current_user.id,
        action="request_approved" if response_data.status == "approved" else "request_rejected",
        details={
            "request_id": str(request_id),
            "response_status": response_data.status,
            "response_message": response_data.response_message
        }
    )
    db.add(audit_log)
    
    await db.commit()
    await db.refresh(auth_request)
    
    logger.info(
        "Authorization request responded",
        request_id=str(request_id),
        status=response_data.status,
        responder_id=str(current_user.id)
    )
    
    return AuthorizationRequestInfo.from_orm(auth_request)


@router.get("/active", response_model=List[AuthorizationInfo])
async def get_active_authorizations(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get active authorizations for the current user."""
    
    query = select(Authorization).options(
        selectinload(Authorization.general_staff_user),
        selectinload(Authorization.authorizing_persona_user)
    )
    
    if current_user.role == "general_staff":
        # General Staff sees their own authorizations
        query = query.where(Authorization.general_staff_user_id == current_user.id)
    elif current_user.role in ["decision_maker", "manager", "agronomist", "researcher"]:
        # Primary personas see authorizations they granted
        query = query.where(Authorization.authorizing_persona_id == current_user.id)
    else:
        # Admins see all authorizations
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
    
    # Filter for active and non-expired authorizations
    now = datetime.utcnow()
    query = query.where(
        and_(
            Authorization.is_active == True,
            Authorization.expires_at > now,
            Authorization.revoked_at.is_(None)
        )
    )
    
    query = query.order_by(Authorization.expires_at.asc())
    
    result = await db.execute(query)
    authorizations = result.scalars().all()
    
    return [AuthorizationInfo.from_orm(auth) for auth in authorizations]


@router.post("/revoke/{authorization_id}", response_model=AuthorizationInfo)
async def revoke_authorization(
    authorization_id: UUID,
    revoke_data: AuthorizationRevoke,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Revoke an active authorization."""
    
    # Get the authorization
    query = select(Authorization).where(Authorization.id == authorization_id)
    result = await db.execute(query)
    authorization = result.scalar_one_or_none()
    
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Authorization not found"
        )
    
    # Verify user can revoke this authorization
    can_revoke = (
        authorization.authorizing_persona_id == current_user.id or  # Original grantor
        current_user.role == "admin"  # Admin
    )
    
    if not can_revoke:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only revoke authorizations you granted"
        )
    
    # Verify authorization is still active
    if not authorization.is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization is already inactive or expired"
        )
    
    # Revoke the authorization
    authorization.is_active = False
    authorization.revoked_at = datetime.utcnow()
    authorization.revoked_by_id = current_user.id
    authorization.revocation_reason = revoke_data.revocation_reason
    
    # Log the revocation
    audit_log = AuthorizationAuditLog(
        authorization_id=authorization.id,
        user_id=current_user.id,
        action="authorization_revoked",
        details={
            "authorization_id": str(authorization_id),
            "revocation_reason": revoke_data.revocation_reason,
            "general_staff_user_id": str(authorization.general_staff_user_id)
        }
    )
    db.add(audit_log)
    
    await db.commit()
    await db.refresh(authorization)
    
    logger.info(
        "Authorization revoked",
        authorization_id=str(authorization_id),
        revoked_by=str(current_user.id),
        reason=revoke_data.revocation_reason
    )
    
    return AuthorizationInfo.from_orm(authorization)


@router.post("/usage/{authorization_id}")
async def track_authorization_usage(
    authorization_id: UUID,
    usage_data: AuthorizationUsage,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Track usage of an authorization."""

    # Get the authorization
    query = select(Authorization).where(Authorization.id == authorization_id)
    result = await db.execute(query)
    authorization = result.scalar_one_or_none()

    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Authorization not found"
        )

    # Verify user can use this authorization
    if authorization.general_staff_user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only use your own authorizations"
        )

    # Verify authorization is valid
    if not authorization.is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization is not valid (expired, revoked, or inactive)"
        )

    # Update usage tracking
    authorization.last_used_at = datetime.utcnow()
    authorization.usage_count += 1

    # Log the usage
    audit_log = AuthorizationAuditLog(
        authorization_id=authorization.id,
        user_id=current_user.id,
        action="authorization_used",
        details={
            "authorization_id": str(authorization_id),
            "action_performed": usage_data.action_performed,
            "data_modified": usage_data.data_modified,
            "usage_count": authorization.usage_count
        }
    )
    db.add(audit_log)

    await db.commit()

    logger.info(
        "Authorization used",
        authorization_id=str(authorization_id),
        user_id=str(current_user.id),
        action=usage_data.action_performed
    )

    return {"message": "Authorization usage tracked successfully"}


@router.get("/available-forms", response_model=AvailableDataEntryForms)
async def get_available_data_entry_forms(
    estate_id: UUID = Query(..., description="Estate ID to check authorizations for"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get available data entry forms based on current authorizations."""

    # Verify user is General Staff
    if current_user.role != "general_staff":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only General Staff can access data entry forms"
        )

    # Core MPOB forms always available
    core_mpob_forms = [
        "ffb_harvest_data",
        "yield_validation_data",
        "fertilizer_usage_tracking",
        "basic_operational_data"
    ]

    # Get active authorizations for this user and estate
    now = datetime.utcnow()
    query = select(Authorization).where(
        and_(
            Authorization.general_staff_user_id == current_user.id,
            Authorization.estate_id == estate_id,
            Authorization.is_active == True,
            Authorization.expires_at > now,
            Authorization.revoked_at.is_(None)
        )
    ).options(selectinload(Authorization.authorizing_persona_user))

    result = await db.execute(query)
    authorizations = result.scalars().all()

    # Build authorized forms list
    authorized_forms = []
    for auth in authorizations:
        authorized_forms.append({
            "data_entry_type": auth.data_entry_type,
            "permission_level": auth.permission_level,
            "expires_at": auth.expires_at,
            "authorizing_persona": auth.authorizing_persona,
            "conditions": auth.conditions
        })

    return AvailableDataEntryForms(
        core_mpob_forms=core_mpob_forms,
        authorized_forms=authorized_forms
    )


@router.get("/summary", response_model=AuthorizationSummary)
async def get_authorization_summary(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get authorization summary statistics."""

    # Verify user has appropriate permissions
    if current_user.role not in ["admin", "decision_maker", "manager", "agronomist", "researcher"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )

    now = datetime.utcnow()

    # Count active authorizations
    active_auth_query = select(Authorization).where(
        and_(
            Authorization.is_active == True,
            Authorization.expires_at > now,
            Authorization.revoked_at.is_(None)
        )
    )

    if current_user.role != "admin":
        active_auth_query = active_auth_query.where(
            Authorization.authorizing_persona_id == current_user.id
        )

    result = await db.execute(active_auth_query)
    active_authorizations = result.scalars().all()

    # Count pending requests
    pending_req_query = select(AuthorizationRequest).where(
        AuthorizationRequest.status == "pending"
    )

    if current_user.role != "admin":
        pending_req_query = pending_req_query.where(
            AuthorizationRequest.target_persona_id == current_user.id
        )

    result = await db.execute(pending_req_query)
    pending_requests = result.scalars().all()

    # Group authorizations by type
    authorizations_by_type = {}
    for auth in active_authorizations:
        auth_type = auth.data_entry_type
        authorizations_by_type[auth_type] = authorizations_by_type.get(auth_type, 0) + 1

    # Count authorizations expiring soon (within 7 days)
    expiring_soon = sum(
        1 for auth in active_authorizations
        if auth.expires_at <= now + timedelta(days=7)
    )

    # Count recent activity (last 24 hours)
    recent_activity_query = select(AuthorizationAuditLog).where(
        AuthorizationAuditLog.created_at >= now - timedelta(days=1)
    )

    result = await db.execute(recent_activity_query)
    recent_activities = result.scalars().all()

    return AuthorizationSummary(
        total_active_authorizations=len(active_authorizations),
        total_pending_requests=len(pending_requests),
        authorizations_by_type=authorizations_by_type,
        authorizations_expiring_soon=expiring_soon,
        recent_activity_count=len(recent_activities)
    )


@router.get("/audit-log", response_model=List[AuthorizationAuditLogInfo])
async def get_authorization_audit_log(
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    action_filter: Optional[str] = Query(None),
    current_user: User = Depends(require_roles(["admin", "decision_maker", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get authorization audit log entries."""

    query = select(AuthorizationAuditLog).options(
        selectinload(AuthorizationAuditLog.user)
    )

    if action_filter:
        query = query.where(AuthorizationAuditLog.action == action_filter)

    query = query.order_by(AuthorizationAuditLog.created_at.desc())
    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    audit_logs = result.scalars().all()

    return [AuthorizationAuditLogInfo.from_orm(log) for log in audit_logs]
