"""
Economic impact analysis API endpoints for ROI calculations and investment analysis.

This module implements comprehensive economic impact assessment capabilities
including ROI calculations, NPV analysis, competitiveness metrics, and scenario modeling.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.economic_impact import (
    EconomicImpactAnalysis,
    CompetitivenessMetric,
    InvestmentScenario,
    EconomicIndicator
)
from app.services.economic_impact_service import economic_impact_service
from app.schemas.common import BaseSchema
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/economic-impact", tags=["Economic Impact Analysis"])


class EconomicImpactAnalysisCreate(BaseModel):
    """Schema for creating economic impact analysis."""
    
    analysis_name: str = Field(min_length=1, max_length=200, description="Name of the analysis")
    analysis_type: str = Field(
        pattern="^(investment|policy|technology|infrastructure)$",
        description="Type of analysis"
    )
    investment_amount: float = Field(gt=0, description="Investment amount in USD")
    investment_period_years: int = Field(ge=1, le=50, description="Investment period in years")
    baseline_productivity: Optional[float] = Field(None, gt=0, description="Baseline productivity")
    projected_productivity_increase: Optional[float] = Field(None, ge=0, le=100, description="Projected productivity increase percentage")
    baseline_costs: Optional[float] = Field(None, gt=0, description="Baseline costs per hectare")
    projected_cost_reduction: Optional[float] = Field(None, ge=0, le=100, description="Projected cost reduction percentage")
    estate_id: Optional[UUID] = Field(None, description="Estate ID if applicable")


class EconomicImpactAnalysisInfo(BaseSchema):
    """Schema for economic impact analysis information."""
    
    id: UUID
    analysis_name: str
    analysis_type: str
    analysis_scope: str
    investment_amount_usd: float
    investment_period_years: int
    baseline_productivity: Optional[float]
    projected_productivity_increase: Optional[float]
    baseline_costs_per_hectare: Optional[float]
    projected_cost_reduction: Optional[float]
    roi_percentage: Optional[float]
    npv_usd: Optional[float]
    irr_percentage: Optional[float]
    payback_period_years: Optional[float]
    job_creation_estimate: Optional[int]
    gdp_impact_usd: Optional[float]
    risk_factors: Optional[List[str]]
    success_probability: Optional[float]
    analysis_results: Dict[str, Any]
    key_findings: Optional[List[str]]
    recommendations: Optional[List[str]]
    is_validated: bool
    analysis_date: date
    created_at: datetime


class InvestmentScenarioCreate(BaseModel):
    """Schema for creating investment scenario."""
    
    scenario_name: str = Field(min_length=1, max_length=200, description="Name of the scenario")
    scenario_type: str = Field(
        pattern="^(baseline|optimistic|pessimistic|policy_intervention)$",
        description="Type of scenario"
    )
    total_investment: float = Field(gt=0, description="Total investment amount in USD")
    timeline_years: int = Field(ge=1, le=20, description="Investment timeline in years")
    funding_sources: Dict[str, float] = Field(description="Funding sources breakdown")
    implementation_phases: List[Dict[str, Any]] = Field(description="Implementation phases")


class InvestmentScenarioInfo(BaseSchema):
    """Schema for investment scenario information."""
    
    id: UUID
    scenario_name: str
    scenario_type: str
    total_investment_usd: float
    investment_timeline_years: int
    funding_sources: Dict[str, float]
    implementation_phases: List[Dict[str, Any]]
    milestone_targets: Dict[str, Any]
    productivity_impact: Dict[str, Any]
    employment_impact: Dict[str, Any]
    environmental_impact: Dict[str, Any]
    social_impact: Dict[str, Any]
    scenario_results: Dict[str, Any]
    success_metrics: Dict[str, Any]
    created_at: datetime


class ROICalculationRequest(BaseModel):
    """Schema for ROI calculation request."""
    
    initial_investment: float = Field(gt=0, description="Initial investment amount")
    annual_returns: List[float] = Field(min_items=1, description="Annual returns for each year")
    investment_period_years: int = Field(ge=1, le=50, description="Investment period in years")


class ROICalculationResult(BaseModel):
    """Schema for ROI calculation result."""
    
    simple_roi_percentage: float
    annualized_roi_percentage: float
    npv: float
    irr_percentage: Optional[float]
    payback_period_years: Optional[float]
    total_returns: float
    net_profit: float


class CompetitivenessAnalysisResult(BaseModel):
    """Schema for competitiveness analysis result."""
    
    country: str
    analysis_date: str
    metrics_by_category: Dict[str, List[Dict[str, Any]]]
    overall_ranking: Dict[str, Any]
    key_strengths: List[str]
    improvement_areas: List[str]


@router.post("/analysis", response_model=EconomicImpactAnalysisInfo)
async def create_economic_impact_analysis(
    analysis_data: EconomicImpactAnalysisCreate,
    current_user: User = Depends(require_roles(["minister", "admin", "manager", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Create comprehensive economic impact analysis."""
    
    analysis = await economic_impact_service.create_economic_impact_analysis(
        user=current_user,
        analysis_name=analysis_data.analysis_name,
        analysis_type=analysis_data.analysis_type,
        investment_amount=analysis_data.investment_amount,
        investment_period_years=analysis_data.investment_period_years,
        baseline_productivity=analysis_data.baseline_productivity,
        projected_productivity_increase=analysis_data.projected_productivity_increase,
        baseline_costs=analysis_data.baseline_costs,
        projected_cost_reduction=analysis_data.projected_cost_reduction,
        estate_id=analysis_data.estate_id,
        db=db
    )
    
    return EconomicImpactAnalysisInfo.from_orm(analysis)


@router.get("/analysis", response_model=List[EconomicImpactAnalysisInfo])
async def get_economic_impact_analyses(
    analysis_type: Optional[str] = Query(None),
    analysis_scope: Optional[str] = Query(None),
    validated_only: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["minister", "admin", "manager", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get economic impact analyses with optional filtering."""
    
    query = select(EconomicImpactAnalysis)
    
    # Apply filters
    if analysis_type:
        query = query.where(EconomicImpactAnalysis.analysis_type == analysis_type)
    
    if analysis_scope:
        query = query.where(EconomicImpactAnalysis.analysis_scope == analysis_scope)
    
    if validated_only is not None:
        query = query.where(EconomicImpactAnalysis.is_validated == validated_only)
    
    # Apply ordering and pagination
    query = query.order_by(desc(EconomicImpactAnalysis.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    analyses = result.scalars().all()
    
    return [EconomicImpactAnalysisInfo.from_orm(analysis) for analysis in analyses]


@router.post("/roi-calculation", response_model=ROICalculationResult)
async def calculate_roi(
    roi_request: ROICalculationRequest,
    current_user: User = Depends(require_roles(["minister", "admin", "manager", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Calculate ROI metrics for investment analysis."""
    
    roi_metrics = economic_impact_service.calculate_roi(
        initial_investment=roi_request.initial_investment,
        annual_returns=roi_request.annual_returns,
        investment_period_years=roi_request.investment_period_years
    )
    
    if "error" in roi_metrics:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=roi_metrics["error"]
        )
    
    return ROICalculationResult(**roi_metrics)


@router.post("/scenarios", response_model=InvestmentScenarioInfo)
async def create_investment_scenario(
    scenario_data: InvestmentScenarioCreate,
    current_user: User = Depends(require_roles(["minister", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Create investment scenario for policy analysis."""
    
    scenario = await economic_impact_service.create_investment_scenario(
        user=current_user,
        scenario_name=scenario_data.scenario_name,
        scenario_type=scenario_data.scenario_type,
        total_investment=scenario_data.total_investment,
        timeline_years=scenario_data.timeline_years,
        funding_sources=scenario_data.funding_sources,
        implementation_phases=scenario_data.implementation_phases,
        db=db
    )
    
    return InvestmentScenarioInfo.from_orm(scenario)


@router.get("/scenarios", response_model=List[InvestmentScenarioInfo])
async def get_investment_scenarios(
    scenario_type: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["minister", "admin", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get investment scenarios with optional filtering."""
    
    query = select(InvestmentScenario)
    
    # Apply filters
    if scenario_type:
        query = query.where(InvestmentScenario.scenario_type == scenario_type)
    
    # Apply ordering and pagination
    query = query.order_by(desc(InvestmentScenario.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    scenarios = result.scalars().all()
    
    return [InvestmentScenarioInfo.from_orm(scenario) for scenario in scenarios]


@router.get("/competitiveness", response_model=CompetitivenessAnalysisResult)
async def get_competitiveness_analysis(
    country: str = Query("Malaysia", description="Country for competitiveness analysis"),
    current_user: User = Depends(require_roles(["minister", "admin", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get competitiveness analysis for a country."""
    
    analysis = await economic_impact_service.get_competitiveness_analysis(
        country=country,
        db=db
    )
    
    return CompetitivenessAnalysisResult(**analysis)


@router.get("/economic-indicators")
async def get_economic_indicators(
    country: str = Query("Malaysia"),
    indicator_category: Optional[str] = Query(None),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(require_roles(["minister", "admin", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get economic indicators for agricultural sector."""
    
    query = select(EconomicIndicator).where(EconomicIndicator.country == country)
    
    if indicator_category:
        query = query.where(EconomicIndicator.indicator_category == indicator_category)
    
    query = query.order_by(desc(EconomicIndicator.measurement_date))
    query = query.limit(limit)
    
    result = await db.execute(query)
    indicators = result.scalars().all()
    
    if not indicators:
        # Return mock data for demonstration
        mock_indicators = [
            {
                "indicator_name": "Agricultural GDP Contribution",
                "indicator_code": "AGR_GDP",
                "indicator_category": "production",
                "current_value": 8.2,
                "previous_period_value": 8.0,
                "year_over_year_change": 2.5,
                "unit_of_measurement": "percentage",
                "measurement_date": date.today().isoformat()
            },
            {
                "indicator_name": "Palm Oil Export Revenue",
                "indicator_code": "PO_EXP_REV",
                "indicator_category": "trade",
                "current_value": 12500.0,
                "previous_period_value": 12000.0,
                "year_over_year_change": 4.2,
                "unit_of_measurement": "million_usd",
                "measurement_date": date.today().isoformat()
            },
            {
                "indicator_name": "Agricultural Employment",
                "indicator_code": "AGR_EMP",
                "indicator_category": "employment",
                "current_value": 1250000,
                "previous_period_value": 1220000,
                "year_over_year_change": 2.5,
                "unit_of_measurement": "persons",
                "measurement_date": date.today().isoformat()
            }
        ]
        return mock_indicators
    
    return [
        {
            "indicator_name": indicator.indicator_name,
            "indicator_code": indicator.indicator_code,
            "indicator_category": indicator.indicator_category,
            "current_value": float(indicator.current_value),
            "previous_period_value": float(indicator.previous_period_value) if indicator.previous_period_value else None,
            "year_over_year_change": float(indicator.year_over_year_change) if indicator.year_over_year_change else None,
            "unit_of_measurement": indicator.unit_of_measurement,
            "measurement_date": indicator.measurement_date.isoformat()
        }
        for indicator in indicators
    ]


@router.get("/investment-impact-summary")
async def get_investment_impact_summary(
    investment_type: Optional[str] = Query(None),
    current_user: User = Depends(require_roles(["minister", "admin", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get investment impact summary for policy decisions."""
    
    # Get recent analyses
    query = select(EconomicImpactAnalysis).order_by(desc(EconomicImpactAnalysis.created_at)).limit(10)
    
    if investment_type:
        query = query.where(EconomicImpactAnalysis.analysis_type == investment_type)
    
    result = await db.execute(query)
    analyses = result.scalars().all()
    
    if not analyses:
        return {"message": "No investment analyses available"}
    
    # Calculate summary statistics
    total_investment = sum(float(a.investment_amount_usd) for a in analyses)
    avg_roi = sum(float(a.roi_percentage or 0) for a in analyses) / len(analyses)
    total_job_creation = sum(a.job_creation_estimate or 0 for a in analyses)
    total_gdp_impact = sum(float(a.gdp_impact_usd or 0) for a in analyses)
    
    # Count by analysis type
    analyses_by_type = {}
    for analysis in analyses:
        analysis_type = analysis.analysis_type
        analyses_by_type[analysis_type] = analyses_by_type.get(analysis_type, 0) + 1
    
    return {
        "summary_statistics": {
            "total_analyses": len(analyses),
            "total_investment_usd": total_investment,
            "average_roi_percentage": avg_roi,
            "total_projected_job_creation": total_job_creation,
            "total_projected_gdp_impact_usd": total_gdp_impact
        },
        "analyses_by_type": analyses_by_type,
        "recent_analyses": [
            {
                "id": str(analysis.id),
                "name": analysis.analysis_name,
                "type": analysis.analysis_type,
                "investment_amount": float(analysis.investment_amount_usd),
                "roi_percentage": float(analysis.roi_percentage or 0),
                "created_at": analysis.created_at.isoformat()
            }
            for analysis in analyses[:5]  # Show top 5 recent analyses
        ]
    }
