"""
Government research API endpoints for <PERSON><PERSON> persona.

This module implements research collaboration and policy development
support for MPOB, MARDI, and Jabatan Pertanian coordination.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.schemas.common import BaseSchema
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/research", tags=["Government Research"])


class MPOBIntegrationData(BaseSchema):
    """Schema for MPOB integration data."""
    
    active_research_projects: int
    data_sharing_agreements: int
    collaborative_studies: int
    policy_recommendations_submitted: int
    standards_development_contributions: int
    last_sync_timestamp: datetime


class MARDICollaborationRequest(BaseSchema):
    """Schema for MARDI collaboration request."""
    
    project_title: str
    research_objectives: List[str]
    data_requirements: List[str]
    collaboration_type: str  # "data_sharing", "joint_research", "consultation"
    timeline_months: int
    budget_requirements: Optional[float]
    expected_outcomes: List[str]


class MARDICollaborationResponse(BaseSchema):
    """Schema for MARDI collaboration response."""
    
    collaboration_id: str
    status: str
    approved_data_access: List[str]
    assigned_researchers: List[str]
    project_timeline: Dict[str, Any]
    budget_allocation: Optional[float]
    next_steps: List[str]


class JabatanPertanianData(BaseSchema):
    """Schema for Jabatan Pertanian integration data."""
    
    registered_estates: int
    compliance_reports_submitted: int
    policy_consultations: int
    training_programs_conducted: int
    certification_processes: int
    regulatory_updates: int


class PolicyRecommendationRequest(BaseSchema):
    """Schema for policy recommendation request."""
    
    recommendation_title: str
    policy_area: str
    evidence_summary: str
    data_sources: List[str]
    impact_assessment: Dict[str, Any]
    implementation_requirements: List[str]
    stakeholder_consultation_results: Dict[str, Any]
    priority_level: str  # "low", "medium", "high", "critical"


class PolicyRecommendationResponse(BaseSchema):
    """Schema for policy recommendation response."""
    
    recommendation_id: str
    submission_status: str
    review_timeline: Dict[str, Any]
    assigned_reviewers: List[str]
    feedback_requirements: List[str]
    next_review_date: date


class ResearchDatasetInfo(BaseSchema):
    """Schema for research dataset information."""
    
    dataset_id: str
    dataset_name: str
    description: str
    data_type: str
    size_mb: float
    last_updated: datetime
    access_level: str
    contributing_agencies: List[str]
    download_count: int


class InternationalResearchDatabase(BaseSchema):
    """Schema for international research database integration."""
    
    database_name: str
    connection_status: str
    available_datasets: int
    last_sync: datetime
    access_credentials_valid: bool
    data_exchange_agreements: List[str]


@router.get("/mpob-integration", response_model=MPOBIntegrationData)
async def get_mpob_integration_status(
    current_user: User = Depends(require_roles(["researcher", "admin", "minister"])),
    db: AsyncSession = Depends(get_db)
):
    """Get MPOB integration status and collaboration metrics."""
    
    # Mock data - would come from MPOB API integration
    integration_data = MPOBIntegrationData(
        active_research_projects=23,
        data_sharing_agreements=8,
        collaborative_studies=12,
        policy_recommendations_submitted=45,
        standards_development_contributions=18,
        last_sync_timestamp=datetime.utcnow() - timedelta(hours=2)
    )
    
    logger.info(
        "MPOB integration status retrieved",
        user_id=str(current_user.id),
        active_projects=integration_data.active_research_projects
    )
    
    return integration_data


@router.post("/mardi-collaboration", response_model=MARDICollaborationResponse)
async def request_mardi_collaboration(
    collaboration_request: MARDICollaborationRequest,
    current_user: User = Depends(require_roles(["researcher", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Request collaboration with MARDI for research projects."""
    
    collaboration_id = f"mardi_collab_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
    
    # Mock MARDI collaboration response
    response = MARDICollaborationResponse(
        collaboration_id=collaboration_id,
        status="under_review",
        approved_data_access=[
            "soil_composition_database",
            "crop_yield_historical_data",
            "weather_pattern_analysis"
        ],
        assigned_researchers=[
            "Dr. Ahmad Rahman (MARDI)",
            "Dr. Siti Nurhaliza (MARDI)",
            "Prof. Lim Wei Ming (MARDI)"
        ],
        project_timeline={
            "review_period_weeks": 2,
            "approval_timeline_weeks": 4,
            "project_duration_months": collaboration_request.timeline_months,
            "reporting_frequency": "monthly"
        },
        budget_allocation=collaboration_request.budget_requirements * 0.8 if collaboration_request.budget_requirements else None,
        next_steps=[
            "Submit detailed research proposal",
            "Attend stakeholder meeting",
            "Complete data access agreements",
            "Establish project governance structure"
        ]
    )
    
    logger.info(
        "MARDI collaboration requested",
        collaboration_id=collaboration_id,
        user_id=str(current_user.id),
        project_title=collaboration_request.project_title
    )
    
    return response


@router.get("/jabatan-pertanian", response_model=JabatanPertanianData)
async def get_jabatan_pertanian_data(
    current_user: User = Depends(require_roles(["researcher", "admin", "minister"])),
    db: AsyncSession = Depends(get_db)
):
    """Get Jabatan Pertanian integration data and metrics."""
    
    # Mock data - would come from Jabatan Pertanian systems
    jp_data = JabatanPertanianData(
        registered_estates=5420,
        compliance_reports_submitted=1245,
        policy_consultations=67,
        training_programs_conducted=156,
        certification_processes=89,
        regulatory_updates=23
    )
    
    logger.info(
        "Jabatan Pertanian data retrieved",
        user_id=str(current_user.id),
        registered_estates=jp_data.registered_estates
    )
    
    return jp_data


@router.post("/policy-recommendations", response_model=PolicyRecommendationResponse)
async def submit_policy_recommendation(
    recommendation_request: PolicyRecommendationRequest,
    current_user: User = Depends(require_roles(["researcher", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Submit policy recommendation based on research findings."""
    
    recommendation_id = f"policy_rec_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
    
    # Determine review timeline based on priority
    review_weeks = {
        "critical": 1,
        "high": 2,
        "medium": 4,
        "low": 8
    }.get(recommendation_request.priority_level, 4)
    
    response = PolicyRecommendationResponse(
        recommendation_id=recommendation_id,
        submission_status="submitted",
        review_timeline={
            "initial_review_weeks": review_weeks,
            "stakeholder_consultation_weeks": review_weeks + 2,
            "final_review_weeks": review_weeks + 4,
            "implementation_planning_weeks": review_weeks + 8
        },
        assigned_reviewers=[
            "Policy Review Committee",
            "Technical Advisory Panel",
            "Stakeholder Representatives"
        ],
        feedback_requirements=[
            "Detailed cost-benefit analysis",
            "Implementation feasibility study",
            "Stakeholder impact assessment",
            "Risk mitigation strategies"
        ],
        next_review_date=date.today() + timedelta(weeks=review_weeks)
    )
    
    logger.info(
        "Policy recommendation submitted",
        recommendation_id=recommendation_id,
        user_id=str(current_user.id),
        title=recommendation_request.recommendation_title,
        priority=recommendation_request.priority_level
    )
    
    return response


@router.get("/datasets", response_model=List[ResearchDatasetInfo])
async def get_available_research_datasets(
    data_type: Optional[str] = Query(None),
    access_level: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=200),
    current_user: User = Depends(require_roles(["researcher", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Get available research datasets for analysis."""
    
    # Mock research datasets - would come from data catalog
    datasets = [
        ResearchDatasetInfo(
            dataset_id="soil_comp_2024",
            dataset_name="National Soil Composition Database 2024",
            description="Comprehensive soil composition data from 5,000+ estates",
            data_type="soil_analysis",
            size_mb=2450.5,
            last_updated=datetime.utcnow() - timedelta(days=7),
            access_level="public",
            contributing_agencies=["MPOB", "MARDI", "Universities"],
            download_count=156
        ),
        ResearchDatasetInfo(
            dataset_id="yield_trends_2020_2024",
            dataset_name="Palm Oil Yield Trends 2020-2024",
            description="Historical yield data with climate correlation analysis",
            data_type="yield_analysis",
            size_mb=1890.2,
            last_updated=datetime.utcnow() - timedelta(days=3),
            access_level="restricted",
            contributing_agencies=["MPOB", "Jabatan Pertanian"],
            download_count=89
        ),
        ResearchDatasetInfo(
            dataset_id="climate_impact_2024",
            dataset_name="Climate Impact on Agricultural Productivity",
            description="Climate data correlation with agricultural outcomes",
            data_type="climate_analysis",
            size_mb=3200.8,
            last_updated=datetime.utcnow() - timedelta(days=1),
            access_level="public",
            contributing_agencies=["MetMalaysia", "MARDI", "Research Universities"],
            download_count=234
        ),
        ResearchDatasetInfo(
            dataset_id="fertilizer_efficiency_2024",
            dataset_name="Fertilizer Efficiency Study Results",
            description="Comprehensive analysis of fertilizer types and application methods",
            data_type="fertilizer_analysis",
            size_mb=1567.3,
            last_updated=datetime.utcnow() - timedelta(days=5),
            access_level="restricted",
            contributing_agencies=["MPOB", "Private Research Institutes"],
            download_count=67
        ),
        ResearchDatasetInfo(
            dataset_id="sustainability_metrics_2024",
            dataset_name="Sustainability Metrics and Certification Data",
            description="Sustainability certification and environmental impact data",
            data_type="sustainability_analysis",
            size_mb=987.6,
            last_updated=datetime.utcnow() - timedelta(days=10),
            access_level="public",
            contributing_agencies=["RSPO", "MSPO", "Environmental NGOs"],
            download_count=145
        )
    ]
    
    # Apply filters
    filtered_datasets = datasets
    if data_type:
        filtered_datasets = [d for d in filtered_datasets if d.data_type == data_type]
    if access_level:
        filtered_datasets = [d for d in filtered_datasets if d.access_level == access_level]
    
    # Apply limit
    filtered_datasets = filtered_datasets[:limit]
    
    logger.info(
        "Research datasets retrieved",
        user_id=str(current_user.id),
        dataset_count=len(filtered_datasets),
        data_type_filter=data_type,
        access_level_filter=access_level
    )
    
    return filtered_datasets


@router.get("/international-databases", response_model=List[InternationalResearchDatabase])
async def get_international_research_databases(
    current_user: User = Depends(require_roles(["researcher", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Get status of international research database integrations."""
    
    databases = [
        InternationalResearchDatabase(
            database_name="FAO Agricultural Statistics",
            connection_status="connected",
            available_datasets=156,
            last_sync=datetime.utcnow() - timedelta(hours=6),
            access_credentials_valid=True,
            data_exchange_agreements=["Data Sharing MOU 2024", "Research Collaboration Agreement"]
        ),
        InternationalResearchDatabase(
            database_name="CGIAR Research Data",
            connection_status="connected",
            available_datasets=89,
            last_sync=datetime.utcnow() - timedelta(hours=12),
            access_credentials_valid=True,
            data_exchange_agreements=["CGIAR Partnership Agreement"]
        ),
        InternationalResearchDatabase(
            database_name="World Bank Agricultural Data",
            connection_status="connected",
            available_datasets=234,
            last_sync=datetime.utcnow() - timedelta(hours=24),
            access_credentials_valid=True,
            data_exchange_agreements=["World Bank Data Access Agreement"]
        ),
        InternationalResearchDatabase(
            database_name="ASEAN Agricultural Research Network",
            connection_status="maintenance",
            available_datasets=67,
            last_sync=datetime.utcnow() - timedelta(days=2),
            access_credentials_valid=True,
            data_exchange_agreements=["ASEAN Research Collaboration MOU"]
        )
    ]
    
    logger.info(
        "International research databases status retrieved",
        user_id=str(current_user.id),
        database_count=len(databases)
    )
    
    return databases


@router.get("/collaboration-opportunities")
async def get_collaboration_opportunities(
    research_area: Optional[str] = Query(None),
    current_user: User = Depends(require_roles(["researcher", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Get available collaboration opportunities with research institutions."""
    
    opportunities = [
        {
            "opportunity_id": "collab_001",
            "title": "Sustainable Palm Oil Production Research Initiative",
            "lead_institution": "University of Malaya",
            "partner_institutions": ["MPOB", "MARDI", "International Universities"],
            "research_areas": ["sustainability", "yield_optimization", "environmental_impact"],
            "funding_available": 2500000.0,
            "application_deadline": "2024-06-30",
            "project_duration_months": 36,
            "expected_outcomes": [
                "New sustainable production methods",
                "Environmental impact reduction strategies",
                "Policy recommendations for industry"
            ]
        },
        {
            "opportunity_id": "collab_002",
            "title": "Climate-Resilient Agriculture Technology Development",
            "lead_institution": "MARDI",
            "partner_institutions": ["CGIAR", "Regional Universities"],
            "research_areas": ["climate_adaptation", "technology_development", "precision_agriculture"],
            "funding_available": 1800000.0,
            "application_deadline": "2024-08-15",
            "project_duration_months": 24,
            "expected_outcomes": [
                "Climate-resilient crop varieties",
                "Precision agriculture tools",
                "Farmer training programs"
            ]
        },
        {
            "opportunity_id": "collab_003",
            "title": "Digital Agriculture and AI Research Program",
            "lead_institution": "Universiti Putra Malaysia",
            "partner_institutions": ["Tech Companies", "International Research Centers"],
            "research_areas": ["artificial_intelligence", "digital_agriculture", "data_analytics"],
            "funding_available": 3200000.0,
            "application_deadline": "2024-09-30",
            "project_duration_months": 48,
            "expected_outcomes": [
                "AI-powered agricultural systems",
                "Digital transformation frameworks",
                "Technology adoption strategies"
            ]
        }
    ]
    
    # Filter by research area if specified
    if research_area:
        opportunities = [
            opp for opp in opportunities
            if research_area.lower() in [area.lower() for area in opp["research_areas"]]
        ]
    
    logger.info(
        "Collaboration opportunities retrieved",
        user_id=str(current_user.id),
        opportunity_count=len(opportunities),
        research_area_filter=research_area
    )
    
    return opportunities
