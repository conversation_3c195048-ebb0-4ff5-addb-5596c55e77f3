"""
Performance monitoring and optimization API endpoints.

This module provides endpoints for monitoring system performance,
health checks, and optimization recommendations.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.utils.performance import performance_optimizer, performance_monitor
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/performance", tags=["Performance Monitoring"])


class PerformanceMetrics(BaseModel):
    """Schema for performance metrics."""
    
    endpoint: str
    request_count: int
    average_ms: float
    median_ms: float
    p95_ms: float
    p99_ms: float
    min_ms: float
    max_ms: float
    last_hour_count: int


class SystemHealthResponse(BaseModel):
    """Schema for system health response."""
    
    overall_health_score: int
    status: str
    api_performance: Dict[str, Any]
    database_performance: Dict[str, Any]
    cache_performance: Dict[str, Any]
    timestamp: str
    recommendations: List[str]


class PerformanceAlert(BaseModel):
    """Schema for performance alerts."""
    
    alert_type: str
    severity: str
    message: str
    endpoint: Optional[str]
    metric_value: float
    threshold: float
    timestamp: str


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive system health metrics."""
    
    health_data = await performance_optimizer.get_system_health(db)
    
    logger.info(
        "System health check performed",
        user_id=str(current_user.id),
        health_score=health_data["overall_health_score"],
        status=health_data["status"]
    )
    
    return SystemHealthResponse(**health_data)


@router.get("/metrics", response_model=List[PerformanceMetrics])
async def get_performance_metrics(
    endpoint: Optional[str] = Query(None, description="Filter by specific endpoint"),
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get performance metrics for API endpoints."""
    
    if endpoint:
        stats = performance_monitor.get_endpoint_stats(endpoint)
        if "error" in stats:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No performance data available for endpoint: {endpoint}"
            )
        return [PerformanceMetrics(**stats)]
    else:
        all_stats = performance_monitor.get_all_stats()
        metrics = []
        for endpoint_name, stats in all_stats.items():
            if isinstance(stats, dict) and "error" not in stats:
                metrics.append(PerformanceMetrics(**stats))
        return metrics


@router.get("/alerts", response_model=List[PerformanceAlert])
async def get_performance_alerts(
    severity: Optional[str] = Query(None, pattern="^(low|medium|high|critical)$"),
    hours: int = Query(24, ge=1, le=168, description="Hours to look back for alerts"),
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get performance alerts based on thresholds."""
    
    alerts = []
    all_stats = performance_monitor.get_all_stats()
    
    # Define thresholds
    thresholds = {
        "response_time_warning": 200,  # ms
        "response_time_critical": 500,  # ms
        "error_rate_warning": 1,  # %
        "error_rate_critical": 5   # %
    }
    
    for endpoint, stats in all_stats.items():
        if isinstance(stats, dict) and "error" not in stats:
            # Check response time alerts
            if stats["p95_ms"] > thresholds["response_time_critical"]:
                alerts.append(PerformanceAlert(
                    alert_type="response_time",
                    severity="critical",
                    message=f"Critical response time detected on {endpoint}",
                    endpoint=endpoint,
                    metric_value=stats["p95_ms"],
                    threshold=thresholds["response_time_critical"],
                    timestamp=datetime.utcnow().isoformat()
                ))
            elif stats["p95_ms"] > thresholds["response_time_warning"]:
                alerts.append(PerformanceAlert(
                    alert_type="response_time",
                    severity="high",
                    message=f"High response time detected on {endpoint}",
                    endpoint=endpoint,
                    metric_value=stats["p95_ms"],
                    threshold=thresholds["response_time_warning"],
                    timestamp=datetime.utcnow().isoformat()
                ))
            
            # Check request volume alerts
            if stats["last_hour_count"] > 10000:  # High volume
                alerts.append(PerformanceAlert(
                    alert_type="high_volume",
                    severity="medium",
                    message=f"High request volume on {endpoint}",
                    endpoint=endpoint,
                    metric_value=stats["last_hour_count"],
                    threshold=10000,
                    timestamp=datetime.utcnow().isoformat()
                ))
    
    # Filter by severity if specified
    if severity:
        alerts = [alert for alert in alerts if alert.severity == severity]
    
    # Sort by severity and timestamp
    severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
    alerts.sort(key=lambda x: (severity_order.get(x.severity, 4), x.timestamp), reverse=True)
    
    return alerts


@router.get("/database-stats")
async def get_database_performance_stats(
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed database performance statistics."""
    
    db_stats = await performance_optimizer.db_optimizer.get_database_stats(db)
    
    logger.info(
        "Database performance stats retrieved",
        user_id=str(current_user.id),
        connection_count=db_stats.get("connection_count", 0)
    )
    
    return db_stats


@router.get("/cache-stats")
async def get_cache_performance_stats(
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get cache performance statistics."""
    
    cache_stats = {"enabled": performance_optimizer.cache.enabled}
    
    if performance_optimizer.cache.enabled:
        try:
            info = performance_optimizer.cache.redis_client.info()
            cache_stats.update({
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_mb": info.get("used_memory", 0) / (1024 * 1024),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate_percentage": info.get("keyspace_hits", 0) / max(
                    info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1
                ) * 100,
                "uptime_seconds": info.get("uptime_in_seconds", 0),
                "redis_version": info.get("redis_version", "unknown")
            })
        except Exception as e:
            cache_stats["error"] = str(e)
    
    logger.info(
        "Cache performance stats retrieved",
        user_id=str(current_user.id),
        cache_enabled=cache_stats["enabled"]
    )
    
    return cache_stats


@router.post("/cache/clear")
async def clear_cache(
    pattern: str = Query("*", description="Pattern to match for cache clearing"),
    current_user: User = Depends(require_roles(["admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Clear cache entries matching pattern."""
    
    if not performance_optimizer.cache.enabled:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache is not enabled"
        )
    
    cleared_count = await performance_optimizer.cache.clear_pattern(pattern)
    
    logger.info(
        "Cache cleared",
        user_id=str(current_user.id),
        pattern=pattern,
        cleared_count=cleared_count
    )
    
    return {
        "message": f"Cleared {cleared_count} cache entries",
        "pattern": pattern,
        "cleared_count": cleared_count
    }


@router.get("/recommendations")
async def get_performance_recommendations(
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get performance optimization recommendations."""
    
    health_data = await performance_optimizer.get_system_health(db)
    recommendations = health_data["recommendations"]
    
    # Add detailed recommendations based on current metrics
    detailed_recommendations = []
    
    for rec in recommendations:
        detailed_rec = {
            "recommendation": rec,
            "priority": "high" if "critical" in rec.lower() else "medium" if "optimize" in rec.lower() else "low",
            "category": "api" if "endpoint" in rec.lower() else "database" if "database" in rec.lower() else "cache" if "cache" in rec.lower() else "general",
            "estimated_impact": "high" if "critical" in rec.lower() else "medium",
            "implementation_effort": "low" if "cache" in rec.lower() else "medium"
        }
        detailed_recommendations.append(detailed_rec)
    
    # Add general recommendations
    if not detailed_recommendations:
        detailed_recommendations.append({
            "recommendation": "System is performing well. Consider implementing proactive monitoring.",
            "priority": "low",
            "category": "monitoring",
            "estimated_impact": "medium",
            "implementation_effort": "low"
        })
    
    return {
        "overall_health_score": health_data["overall_health_score"],
        "status": health_data["status"],
        "recommendations": detailed_recommendations,
        "generated_at": datetime.utcnow().isoformat()
    }


@router.get("/load-test-results")
async def get_load_test_results(
    test_type: Optional[str] = Query(None, pattern="^(concurrent|throughput|stress)$"),
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get recent load test results and performance benchmarks."""
    
    # Mock load test results for demonstration
    # In production, this would come from a test results database
    load_test_results = [
        {
            "test_id": "load_test_001",
            "test_type": "concurrent",
            "test_date": (datetime.utcnow() - timedelta(days=1)).isoformat(),
            "configuration": {
                "concurrent_users": 100,
                "requests_per_user": 20,
                "duration_seconds": 300
            },
            "results": {
                "total_requests": 2000,
                "successful_requests": 1995,
                "failed_requests": 5,
                "success_rate_percentage": 99.75,
                "average_response_time_ms": 145.2,
                "p95_response_time_ms": 189.5,
                "p99_response_time_ms": 245.8,
                "requests_per_second": 6.67
            },
            "requirements_compliance": {
                "meets_200ms_requirement": True,
                "meets_concurrent_users_requirement": True,
                "meets_success_rate_requirement": True
            }
        },
        {
            "test_id": "throughput_test_001",
            "test_type": "throughput",
            "test_date": (datetime.utcnow() - timedelta(days=2)).isoformat(),
            "configuration": {
                "target_rps": 167,
                "duration_seconds": 300,
                "endpoint": "/api/v1/sensor-data"
            },
            "results": {
                "actual_rps": 164.3,
                "total_requests": 49290,
                "successful_requests": 49145,
                "failed_requests": 145,
                "success_rate_percentage": 99.71,
                "average_response_time_ms": 98.7,
                "p95_response_time_ms": 156.2,
                "p99_response_time_ms": 198.4
            },
            "requirements_compliance": {
                "meets_throughput_requirement": True,
                "meets_response_time_requirement": True,
                "meets_reliability_requirement": True
            }
        }
    ]
    
    # Filter by test type if specified
    if test_type:
        load_test_results = [
            result for result in load_test_results
            if result["test_type"] == test_type
        ]
    
    return {
        "test_results": load_test_results,
        "summary": {
            "total_tests": len(load_test_results),
            "latest_test_date": max([result["test_date"] for result in load_test_results]) if load_test_results else None,
            "overall_compliance": all([
                all(result["requirements_compliance"].values())
                for result in load_test_results
            ])
        }
    }


@router.get("/uptime")
async def get_uptime_metrics(
    days: int = Query(7, ge=1, le=30, description="Number of days to analyze"),
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get uptime and availability metrics."""
    
    # Mock uptime data for demonstration
    # In production, this would come from monitoring systems
    uptime_data = {
        "period_days": days,
        "overall_uptime_percentage": 99.7,
        "target_uptime_percentage": 99.5,
        "meets_sla": True,
        "total_downtime_minutes": 43.2,
        "incidents": [
            {
                "incident_id": "INC-001",
                "start_time": (datetime.utcnow() - timedelta(days=3, hours=2)).isoformat(),
                "end_time": (datetime.utcnow() - timedelta(days=3, hours=1, minutes=45)).isoformat(),
                "duration_minutes": 15,
                "severity": "medium",
                "cause": "Database connection timeout",
                "resolution": "Connection pool optimization"
            },
            {
                "incident_id": "INC-002",
                "start_time": (datetime.utcnow() - timedelta(days=5, hours=14)).isoformat(),
                "end_time": (datetime.utcnow() - timedelta(days=5, hours=13, minutes=32)).isoformat(),
                "duration_minutes": 28,
                "severity": "high",
                "cause": "Memory leak in prediction service",
                "resolution": "Service restart and memory optimization"
            }
        ],
        "daily_uptime": [
            {
                "date": (datetime.utcnow() - timedelta(days=i)).date().isoformat(),
                "uptime_percentage": 99.8 if i not in [3, 5] else 99.2 if i == 3 else 98.9
            }
            for i in range(days)
        ]
    }
    
    return uptime_data
