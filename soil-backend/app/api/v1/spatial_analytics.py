"""
Enhanced spatial analytics API endpoints for advanced interpolation, hotspot analysis, and trend analysis.

This module provides comprehensive spatial analysis capabilities including
advanced interpolation algorithms, spatial clustering, hotspot detection,
and temporal trend analysis for agricultural data.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.spatial_analytics import (
    SpatialInterpolationModel,
    HotspotAnalysis,
    SpatialTrendAnalysis,
    SpatialCluster,
    SpatialOptimization
)
from app.services.spatial_analytics_service import spatial_analytics_service
from app.schemas.common import BaseSchema
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/spatial-analytics", tags=["Enhanced Spatial Analytics"])


class InterpolationModelCreate(BaseModel):
    """Schema for creating interpolation model."""
    
    estate_id: UUID = Field(description="Estate ID for analysis")
    model_name: str = Field(min_length=1, max_length=100, description="Name of the interpolation model")
    interpolation_method: str = Field(
        pattern="^(kriging|idw|rbf|spline|neural_spatial|ensemble)$",
        description="Interpolation method to use"
    )
    parameter: str = Field(description="Soil parameter to interpolate")
    model_parameters: Dict[str, Any] = Field(description="Method-specific parameters")
    grid_resolution_meters: int = Field(default=100, ge=10, le=1000, description="Grid resolution in meters")


class InterpolationModelInfo(BaseSchema):
    """Schema for interpolation model information."""
    
    id: UUID
    model_name: str
    interpolation_method: str
    estate_id: Optional[UUID]
    model_parameters: Dict[str, Any]
    variogram_model: Optional[str]
    cross_validation_score: Optional[float]
    rmse: Optional[float]
    mae: Optional[float]
    r2_score: Optional[float]
    grid_resolution_meters: int
    grid_points_count: Optional[int]
    is_trained: bool
    is_active: bool
    training_data_count: Optional[int]
    created_at: datetime
    trained_at: Optional[datetime]


class HotspotAnalysisCreate(BaseModel):
    """Schema for creating hotspot analysis."""
    
    estate_id: UUID = Field(description="Estate ID for analysis")
    parameter: str = Field(description="Parameter to analyze for hotspots")
    analysis_type: str = Field(
        default="getis_ord",
        pattern="^(getis_ord|local_moran|kernel_density|dbscan|custom)$",
        description="Type of hotspot analysis"
    )
    distance_threshold: float = Field(default=1000.0, gt=0, description="Distance threshold in meters")
    significance_level: float = Field(default=0.05, gt=0, lt=1, description="Statistical significance level")


class HotspotAnalysisInfo(BaseSchema):
    """Schema for hotspot analysis information."""
    
    id: UUID
    analysis_name: str
    analysis_type: str
    estate_id: Optional[UUID]
    parameter_analyzed: str
    analysis_parameters: Dict[str, Any]
    distance_threshold_meters: Optional[float]
    significance_level: float
    hotspots_detected: int
    coldspots_detected: int
    hotspot_locations: List[Dict[str, Any]]
    coldspot_locations: List[Dict[str, Any]]
    global_moran_i: Optional[float]
    p_value: Optional[float]
    z_score: Optional[float]
    analysis_results: Dict[str, Any]
    confidence_score: Optional[float]
    analysis_date: date
    created_at: datetime


class SpatialClusteringCreate(BaseModel):
    """Schema for creating spatial clustering analysis."""
    
    estate_id: UUID = Field(description="Estate ID for analysis")
    parameters: List[str] = Field(min_items=1, description="Parameters to use for clustering")
    algorithm: str = Field(
        default="kmeans",
        pattern="^(kmeans|dbscan|hierarchical|gaussian_mixture|spectral)$",
        description="Clustering algorithm"
    )
    n_clusters: int = Field(default=5, ge=2, le=20, description="Number of clusters (for applicable algorithms)")
    algorithm_parameters: Dict[str, Any] = Field(default_factory=dict, description="Algorithm-specific parameters")


class SpatialClusterInfo(BaseSchema):
    """Schema for spatial cluster information."""
    
    id: UUID
    cluster_name: str
    clustering_algorithm: str
    estate_id: Optional[UUID]
    parameters_used: List[str]
    algorithm_parameters: Dict[str, Any]
    number_of_clusters: int
    cluster_assignments: Dict[str, int]
    cluster_centers: Dict[str, Any]
    cluster_characteristics: Dict[str, Any]
    silhouette_score: Optional[float]
    calinski_harabasz_score: Optional[float]
    davies_bouldin_score: Optional[float]
    management_recommendations: Optional[Dict[str, Any]]
    data_points_count: int
    analysis_date: date
    created_at: datetime


class SpatialOptimizationCreate(BaseModel):
    """Schema for creating spatial optimization."""
    
    estate_id: UUID = Field(description="Estate ID for optimization")
    optimization_type: str = Field(
        pattern="^(fertilizer_allocation|sampling_design|irrigation_planning|harvest_routing)$",
        description="Type of spatial optimization"
    )
    objective_function: str = Field(description="Objective function to optimize")
    constraints: Dict[str, Any] = Field(description="Optimization constraints")
    optimization_parameters: Dict[str, Any] = Field(description="Optimization-specific parameters")


class SpatialOptimizationInfo(BaseSchema):
    """Schema for spatial optimization information."""
    
    id: UUID
    optimization_name: str
    optimization_type: str
    estate_id: Optional[UUID]
    objective_function: str
    constraints: Dict[str, Any]
    optimization_parameters: Dict[str, Any]
    optimal_solution: Dict[str, Any]
    objective_value: float
    convergence_status: str
    optimality_gap: Optional[float]
    solution_confidence: Optional[float]
    sensitivity_analysis: Optional[Dict[str, Any]]
    implementation_plan: Optional[Dict[str, Any]]
    expected_benefits: Optional[Dict[str, Any]]
    implementation_cost: Optional[float]
    solver_used: str
    computation_time_seconds: Optional[float]
    iterations: Optional[int]
    created_at: datetime


@router.post("/interpolation-models", response_model=InterpolationModelInfo)
async def create_interpolation_model(
    model_data: InterpolationModelCreate,
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Create advanced spatial interpolation model."""
    
    try:
        interpolation_model = await spatial_analytics_service.create_advanced_interpolation_model(
            estate_id=model_data.estate_id,
            model_name=model_data.model_name,
            interpolation_method=model_data.interpolation_method,
            parameter=model_data.parameter,
            model_parameters=model_data.model_parameters,
            grid_resolution_meters=model_data.grid_resolution_meters,
            db=db
        )
        
        return InterpolationModelInfo.from_orm(interpolation_model)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/interpolation-models", response_model=List[InterpolationModelInfo])
async def get_interpolation_models(
    estate_id: Optional[UUID] = Query(None),
    interpolation_method: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get spatial interpolation models with optional filtering."""
    
    query = select(SpatialInterpolationModel)
    
    # Apply filters
    if estate_id:
        query = query.where(SpatialInterpolationModel.estate_id == estate_id)
    
    if interpolation_method:
        query = query.where(SpatialInterpolationModel.interpolation_method == interpolation_method)
    
    if is_active is not None:
        query = query.where(SpatialInterpolationModel.is_active == is_active)
    
    # Apply ordering and pagination
    query = query.order_by(desc(SpatialInterpolationModel.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    models = result.scalars().all()
    
    return [InterpolationModelInfo.from_orm(model) for model in models]


@router.post("/hotspot-analysis", response_model=HotspotAnalysisInfo)
async def create_hotspot_analysis(
    analysis_data: HotspotAnalysisCreate,
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Perform hotspot analysis to identify spatial patterns."""
    
    try:
        hotspot_analysis = await spatial_analytics_service.perform_hotspot_analysis(
            estate_id=analysis_data.estate_id,
            parameter=analysis_data.parameter,
            analysis_type=analysis_data.analysis_type,
            distance_threshold=analysis_data.distance_threshold,
            significance_level=analysis_data.significance_level,
            db=db
        )
        
        return HotspotAnalysisInfo.from_orm(hotspot_analysis)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/hotspot-analysis", response_model=List[HotspotAnalysisInfo])
async def get_hotspot_analyses(
    estate_id: Optional[UUID] = Query(None),
    parameter: Optional[str] = Query(None),
    analysis_type: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get hotspot analyses with optional filtering."""
    
    query = select(HotspotAnalysis)
    
    # Apply filters
    if estate_id:
        query = query.where(HotspotAnalysis.estate_id == estate_id)
    
    if parameter:
        query = query.where(HotspotAnalysis.parameter_analyzed == parameter)
    
    if analysis_type:
        query = query.where(HotspotAnalysis.analysis_type == analysis_type)
    
    # Apply ordering and pagination
    query = query.order_by(desc(HotspotAnalysis.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    analyses = result.scalars().all()
    
    return [HotspotAnalysisInfo.from_orm(analysis) for analysis in analyses]


@router.post("/spatial-clustering", response_model=SpatialClusterInfo)
async def create_spatial_clustering(
    clustering_data: SpatialClusteringCreate,
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Perform spatial clustering analysis."""
    
    try:
        spatial_cluster = await spatial_analytics_service.perform_spatial_clustering(
            estate_id=clustering_data.estate_id,
            parameters=clustering_data.parameters,
            algorithm=clustering_data.algorithm,
            n_clusters=clustering_data.n_clusters,
            algorithm_params=clustering_data.algorithm_parameters,
            db=db
        )
        
        return SpatialClusterInfo.from_orm(spatial_cluster)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/spatial-clustering", response_model=List[SpatialClusterInfo])
async def get_spatial_clusters(
    estate_id: Optional[UUID] = Query(None),
    algorithm: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get spatial clustering results with optional filtering."""
    
    query = select(SpatialCluster)
    
    # Apply filters
    if estate_id:
        query = query.where(SpatialCluster.estate_id == estate_id)
    
    if algorithm:
        query = query.where(SpatialCluster.clustering_algorithm == algorithm)
    
    # Apply ordering and pagination
    query = query.order_by(desc(SpatialCluster.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    clusters = result.scalars().all()
    
    return [SpatialClusterInfo.from_orm(cluster) for cluster in clusters]


@router.post("/spatial-optimization", response_model=SpatialOptimizationInfo)
async def create_spatial_optimization(
    optimization_data: SpatialOptimizationCreate,
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist"])),
    db: AsyncSession = Depends(get_db)
):
    """Perform spatial optimization for resource allocation."""
    
    try:
        spatial_optimization = await spatial_analytics_service.perform_spatial_optimization(
            estate_id=optimization_data.estate_id,
            optimization_type=optimization_data.optimization_type,
            objective_function=optimization_data.objective_function,
            constraints=optimization_data.constraints,
            optimization_params=optimization_data.optimization_parameters,
            db=db
        )
        
        return SpatialOptimizationInfo.from_orm(spatial_optimization)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/spatial-optimization", response_model=List[SpatialOptimizationInfo])
async def get_spatial_optimizations(
    estate_id: Optional[UUID] = Query(None),
    optimization_type: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist"])),
    db: AsyncSession = Depends(get_db)
):
    """Get spatial optimization results with optional filtering."""
    
    query = select(SpatialOptimization)
    
    # Apply filters
    if estate_id:
        query = query.where(SpatialOptimization.estate_id == estate_id)
    
    if optimization_type:
        query = query.where(SpatialOptimization.optimization_type == optimization_type)
    
    # Apply ordering and pagination
    query = query.order_by(desc(SpatialOptimization.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    optimizations = result.scalars().all()
    
    return [SpatialOptimizationInfo.from_orm(optimization) for optimization in optimizations]
