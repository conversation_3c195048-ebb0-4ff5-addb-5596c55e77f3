"""
Enhanced security API endpoints for enterprise-grade authentication and audit logging.

This module implements multi-factor authentication, security audit logging,
and GDPR compliance endpoints.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.security import (
    UserMFA, SecurityAuditLog, LoginAttempt, GDPRDataRequest,
    AuditEventType, MFAMethod
)
from app.services.security_service import security_service
from app.schemas.common import BaseSchema
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/security", tags=["Security & Compliance"])


class MFASetupRequest(BaseModel):
    """Schema for MFA setup request."""
    method: str = Field(pattern="^(totp|sms|email)$", description="MFA method to set up")


class MFASetupResponse(BaseSchema):
    """Schema for MFA setup response."""
    secret: Optional[str] = None
    qr_code: Optional[str] = None
    backup_codes: Optional[List[str]] = None
    setup_complete: bool


class MFAVerificationRequest(BaseModel):
    """Schema for MFA verification request."""
    token: str = Field(min_length=6, max_length=8, description="MFA token or backup code")
    method: str = Field(pattern="^(totp|backup_codes)$", description="Verification method")


class SecurityAuditLogInfo(BaseSchema):
    """Schema for security audit log information."""
    id: UUID
    user_id: Optional[UUID]
    event_type: str
    event_description: str
    event_data: Optional[Dict[str, Any]]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    severity_level: str
    success: bool
    failure_reason: Optional[str]
    created_at: datetime


class GDPRDataRequestCreate(BaseModel):
    """Schema for creating GDPR data request."""
    request_type: str = Field(
        pattern="^(data_export|data_deletion|data_correction|consent_withdrawal)$",
        description="Type of GDPR request"
    )
    description: Optional[str] = Field(None, max_length=1000, description="Request description")
    requested_data_types: Optional[List[str]] = Field(None, description="Specific data types requested")


class GDPRDataRequestInfo(BaseSchema):
    """Schema for GDPR data request information."""
    id: UUID
    user_id: UUID
    request_type: str
    status: str
    description: Optional[str]
    requested_data_types: Optional[List[str]]
    processed_by_id: Optional[UUID]
    processing_notes: Optional[str]
    completion_date: Optional[datetime]
    export_file_path: Optional[str]
    export_expires_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class SecuritySummary(BaseModel):
    """Schema for security summary statistics."""
    total_users_with_mfa: int
    failed_login_attempts_24h: int
    security_events_24h: int
    high_severity_events_7d: int
    pending_gdpr_requests: int
    active_sessions: int


@router.post("/mfa/setup", response_model=MFASetupResponse)
async def setup_mfa(
    setup_request: MFASetupRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Set up multi-factor authentication for the current user."""
    
    if setup_request.method == "totp":
        setup_data = await security_service.setup_mfa_totp(current_user, db)
        return MFASetupResponse(
            secret=setup_data["secret"],
            qr_code=setup_data["qr_code"],
            setup_complete=False
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"MFA method '{setup_request.method}' not yet implemented"
        )


@router.post("/mfa/verify", response_model=Dict[str, Any])
async def verify_mfa(
    verification_request: MFAVerificationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Verify MFA token to complete setup or authenticate."""
    
    if verification_request.method == "totp":
        is_valid = await security_service.verify_totp(current_user, verification_request.token, db)
    elif verification_request.method == "backup_codes":
        is_valid = await security_service.verify_backup_code(current_user, verification_request.token, db)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Verification method '{verification_request.method}' not supported"
        )
    
    if is_valid:
        return {"verified": True, "message": "MFA verification successful"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid MFA token"
        )


@router.post("/mfa/backup-codes", response_model=Dict[str, List[str]])
async def generate_backup_codes(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate backup codes for MFA recovery."""
    
    backup_codes = await security_service.generate_backup_codes(current_user, db)
    return {"backup_codes": backup_codes}


@router.delete("/mfa")
async def disable_mfa(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Disable MFA for the current user."""
    
    success = await security_service.disable_mfa(current_user, db)
    if success:
        return {"message": "MFA disabled successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="MFA was not enabled"
        )


@router.get("/mfa/status")
async def get_mfa_status(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get MFA status for the current user."""
    
    is_enabled = await security_service.is_mfa_enabled(current_user, db)
    
    # Get MFA settings details
    mfa_query = select(UserMFA).where(UserMFA.user_id == current_user.id)
    result = await db.execute(mfa_query)
    mfa_settings = result.scalar_one_or_none()
    
    return {
        "mfa_enabled": is_enabled,
        "primary_method": mfa_settings.primary_method if mfa_settings else None,
        "totp_verified": mfa_settings.totp_verified if mfa_settings else False,
        "backup_codes_available": bool(mfa_settings and mfa_settings.backup_codes),
        "last_verification": mfa_settings.last_verification if mfa_settings else None
    }


@router.get("/audit-log", response_model=List[SecurityAuditLogInfo])
async def get_security_audit_log(
    event_type: Optional[str] = Query(None),
    user_id: Optional[UUID] = Query(None),
    severity_level: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get security audit log entries."""
    
    query = select(SecurityAuditLog)
    
    # Apply filters
    if event_type:
        query = query.where(SecurityAuditLog.event_type == event_type)
    
    if user_id:
        query = query.where(SecurityAuditLog.user_id == user_id)
    
    if severity_level:
        query = query.where(SecurityAuditLog.severity_level == severity_level)
    
    if start_date:
        query = query.where(SecurityAuditLog.created_at >= start_date)
    
    if end_date:
        query = query.where(SecurityAuditLog.created_at <= end_date)
    
    # Apply ordering and pagination
    query = query.order_by(desc(SecurityAuditLog.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    audit_logs = result.scalars().all()
    
    return [SecurityAuditLogInfo.from_orm(log) for log in audit_logs]


@router.post("/gdpr/data-request", response_model=GDPRDataRequestInfo)
async def create_gdpr_data_request(
    request_data: GDPRDataRequestCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a GDPR/PDPA data request."""
    
    gdpr_request = await security_service.create_gdpr_data_request(
        user=current_user,
        request_type=request_data.request_type,
        description=request_data.description,
        requested_data_types=request_data.requested_data_types,
        db=db
    )
    
    return GDPRDataRequestInfo.from_orm(gdpr_request)


@router.get("/gdpr/data-requests", response_model=List[GDPRDataRequestInfo])
async def get_gdpr_data_requests(
    status_filter: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get GDPR data requests for the current user."""
    
    query = select(GDPRDataRequest).where(GDPRDataRequest.user_id == current_user.id)
    
    if status_filter:
        query = query.where(GDPRDataRequest.status == status_filter)
    
    query = query.order_by(desc(GDPRDataRequest.created_at))
    
    result = await db.execute(query)
    requests = result.scalars().all()
    
    return [GDPRDataRequestInfo.from_orm(req) for req in requests]


@router.get("/summary", response_model=SecuritySummary)
async def get_security_summary(
    current_user: User = Depends(require_roles(["admin", "manager"])),
    db: AsyncSession = Depends(get_db)
):
    """Get security summary statistics."""
    
    now = datetime.utcnow()
    
    # Count users with MFA enabled
    mfa_users_query = select(func.count(UserMFA.id)).where(UserMFA.is_enabled == True)
    mfa_result = await db.execute(mfa_users_query)
    total_users_with_mfa = mfa_result.scalar() or 0
    
    # Count failed login attempts in last 24 hours
    failed_logins_query = select(func.count(LoginAttempt.id)).where(
        and_(
            LoginAttempt.success == False,
            LoginAttempt.attempted_at >= now - timedelta(hours=24)
        )
    )
    failed_logins_result = await db.execute(failed_logins_query)
    failed_login_attempts_24h = failed_logins_result.scalar() or 0
    
    # Count security events in last 24 hours
    security_events_query = select(func.count(SecurityAuditLog.id)).where(
        SecurityAuditLog.created_at >= now - timedelta(hours=24)
    )
    security_events_result = await db.execute(security_events_query)
    security_events_24h = security_events_result.scalar() or 0
    
    # Count high severity events in last 7 days
    high_severity_query = select(func.count(SecurityAuditLog.id)).where(
        and_(
            SecurityAuditLog.severity_level == "high",
            SecurityAuditLog.created_at >= now - timedelta(days=7)
        )
    )
    high_severity_result = await db.execute(high_severity_query)
    high_severity_events_7d = high_severity_result.scalar() or 0
    
    # Count pending GDPR requests
    pending_gdpr_query = select(func.count(GDPRDataRequest.id)).where(
        GDPRDataRequest.status == "pending"
    )
    pending_gdpr_result = await db.execute(pending_gdpr_query)
    pending_gdpr_requests = pending_gdpr_result.scalar() or 0
    
    # Mock active sessions count (would come from session management)
    active_sessions = 156  # This would be calculated from actual session data
    
    return SecuritySummary(
        total_users_with_mfa=total_users_with_mfa,
        failed_login_attempts_24h=failed_login_attempts_24h,
        security_events_24h=security_events_24h,
        high_severity_events_7d=high_severity_events_7d,
        pending_gdpr_requests=pending_gdpr_requests,
        active_sessions=active_sessions
    )


@router.get("/login-attempts", response_model=List[Dict[str, Any]])
async def get_login_attempts(
    user_email: Optional[str] = Query(None),
    success_only: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Get login attempts for security monitoring."""
    
    query = select(LoginAttempt)
    
    # Apply filters
    if user_email:
        query = query.where(LoginAttempt.email.ilike(f"%{user_email}%"))
    
    if success_only is not None:
        query = query.where(LoginAttempt.success == success_only)
    
    # Apply ordering and pagination
    query = query.order_by(desc(LoginAttempt.attempted_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    login_attempts = result.scalars().all()
    
    return [
        {
            "id": str(attempt.id),
            "email": attempt.email,
            "success": attempt.success,
            "failure_reason": attempt.failure_reason,
            "ip_address": attempt.ip_address,
            "user_agent": attempt.user_agent,
            "is_suspicious": attempt.is_suspicious,
            "attempted_at": attempt.attempted_at
        }
        for attempt in login_attempts
    ]


@router.post("/encrypt-data")
async def encrypt_sensitive_data(
    data: Dict[str, str],
    current_user: User = Depends(require_roles(["admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Encrypt sensitive data using AES-256."""
    
    encrypted_data = {}
    for key, value in data.items():
        encrypted_data[key] = security_service.encrypt_sensitive_data(value)
    
    await security_service.log_security_event(
        user_id=current_user.id,
        event_type=AuditEventType.DATA_MODIFICATION,
        description="Data encryption performed",
        success=True,
        event_data={"encrypted_fields": list(data.keys())},
        db=db
    )
    
    return {"encrypted_data": encrypted_data}


@router.post("/decrypt-data")
async def decrypt_sensitive_data(
    encrypted_data: Dict[str, str],
    current_user: User = Depends(require_roles(["admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Decrypt sensitive data."""
    
    try:
        decrypted_data = {}
        for key, value in encrypted_data.items():
            decrypted_data[key] = security_service.decrypt_sensitive_data(value)
        
        await security_service.log_security_event(
            user_id=current_user.id,
            event_type=AuditEventType.DATA_ACCESS,
            description="Data decryption performed",
            success=True,
            event_data={"decrypted_fields": list(encrypted_data.keys())},
            db=db
        )
        
        return {"decrypted_data": decrypted_data}
    
    except ValueError as e:
        await security_service.log_security_event(
            user_id=current_user.id,
            event_type=AuditEventType.SECURITY_VIOLATION,
            description="Failed data decryption attempt",
            success=False,
            failure_reason=str(e),
            severity_level="high",
            db=db
        )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to decrypt data"
        )
