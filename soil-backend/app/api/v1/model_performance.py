"""
Enhanced model performance monitoring and validation API endpoints.

This module implements comprehensive model performance tracking,
accuracy validation, and prediction feedback loops as required by the PRD.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.model_performance import (
    ModelPerformanceMetric,
    PredictionFeedback,
    ModelAccuracyValidation,
    SHAPExplanationRecord
)
from app.services.model_performance_service import model_performance_service
from app.schemas.common import BaseSchema
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/model-performance", tags=["Model Performance & Validation"])


class PredictionFeedbackCreate(BaseModel):
    """Schema for creating prediction feedback."""
    
    prediction_id: str = Field(description="ID of the prediction")
    model_name: str = Field(description="Name of the model")
    model_version: str = Field(description="Version of the model")
    predicted_values: Dict[str, float] = Field(description="Original predicted values")
    feedback_type: str = Field(
        pattern="^(accuracy_rating|correction|validation|complaint)$",
        description="Type of feedback"
    )
    feedback_rating: Optional[int] = Field(None, ge=1, le=5, description="Rating from 1-5")
    corrected_values: Optional[Dict[str, float]] = Field(None, description="Corrected values")
    actual_measured_values: Optional[Dict[str, float]] = Field(None, description="Actual measured values")
    feedback_text: Optional[str] = Field(None, max_length=1000, description="Feedback text")
    confidence_scores: Optional[Dict[str, float]] = Field(None, description="Original confidence scores")
    estate_id: Optional[UUID] = Field(None, description="Estate ID if applicable")


class PredictionFeedbackInfo(BaseSchema):
    """Schema for prediction feedback information."""
    
    id: UUID
    user_id: UUID
    estate_id: Optional[UUID]
    prediction_id: str
    model_name: str
    model_version: str
    predicted_values: Dict[str, float]
    confidence_scores: Optional[Dict[str, float]]
    feedback_type: str
    feedback_rating: Optional[int]
    corrected_values: Optional[Dict[str, float]]
    actual_measured_values: Optional[Dict[str, float]]
    feedback_text: Optional[str]
    is_processed: bool
    processed_at: Optional[datetime]
    used_for_retraining: bool
    created_at: datetime
    updated_at: datetime


class ModelAccuracyValidationInfo(BaseSchema):
    """Schema for model accuracy validation information."""
    
    id: UUID
    model_name: str
    model_version: str
    validation_type: str
    validation_dataset_size: int
    overall_accuracy: float
    parameter_accuracies: Dict[str, float]
    r2_score: float
    rmse: float
    mae: float
    meets_85_95_target: bool
    accuracy_percentile: Optional[float]
    results_by_parameter: Dict[str, float]
    validation_date: date
    validation_status: str
    created_at: datetime


class ModelPerformanceMetricInfo(BaseSchema):
    """Schema for model performance metric information."""
    
    id: UUID
    model_name: str
    model_version: str
    model_type: str
    accuracy_r2: Optional[float]
    rmse: Optional[float]
    mae: Optional[float]
    mape: Optional[float]
    avg_confidence_score: Optional[float]
    confidence_distribution: Optional[Dict[str, float]]
    meets_accuracy_target: bool
    accuracy_target_range: Optional[str]
    avg_latency_ms: Optional[float]
    p95_latency_ms: Optional[float]
    p99_latency_ms: Optional[float]
    meets_latency_target: bool
    prediction_count: int
    error_count: int
    error_rate: Optional[float]
    evaluation_date: date
    created_at: datetime


class SHAPExplanationInfo(BaseSchema):
    """Schema for SHAP explanation information."""
    
    id: UUID
    prediction_id: str
    model_name: str
    model_version: str
    feature_importance: Dict[str, float]
    top_positive_features: List[Dict[str, Any]]
    top_negative_features: List[Dict[str, Any]]
    explanation_confidence: float
    input_features: Dict[str, float]
    prediction_output: Dict[str, float]
    explanation_time_ms: Optional[float]
    created_at: datetime


class ModelPerformanceSummary(BaseModel):
    """Schema for model performance summary."""
    
    model_name: str
    model_version: str
    current_accuracy: float
    average_accuracy_30d: float
    meets_accuracy_target: bool
    current_latency_ms: float
    average_latency_30d: float
    meets_latency_target: bool
    accuracy_trend: str
    total_predictions: int
    error_rate: float
    last_evaluation: date
    evaluation_count: int


@router.post("/feedback", response_model=PredictionFeedbackInfo)
async def create_prediction_feedback(
    feedback_data: PredictionFeedbackCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create prediction feedback from users."""
    
    feedback = await model_performance_service.record_prediction_feedback(
        user=current_user,
        prediction_id=feedback_data.prediction_id,
        model_name=feedback_data.model_name,
        model_version=feedback_data.model_version,
        predicted_values=feedback_data.predicted_values,
        feedback_type=feedback_data.feedback_type,
        feedback_rating=feedback_data.feedback_rating,
        corrected_values=feedback_data.corrected_values,
        actual_measured_values=feedback_data.actual_measured_values,
        feedback_text=feedback_data.feedback_text,
        confidence_scores=feedback_data.confidence_scores,
        estate_id=feedback_data.estate_id,
        db=db
    )
    
    return PredictionFeedbackInfo.from_orm(feedback)


@router.get("/feedback", response_model=List[PredictionFeedbackInfo])
async def get_prediction_feedback(
    model_name: Optional[str] = Query(None),
    feedback_type: Optional[str] = Query(None),
    processed_only: Optional[bool] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get prediction feedback entries."""
    
    query = select(PredictionFeedback)
    
    # Non-admin users can only see their own feedback
    if current_user.role not in ["admin", "manager"]:
        query = query.where(PredictionFeedback.user_id == current_user.id)
    
    # Apply filters
    if model_name:
        query = query.where(PredictionFeedback.model_name == model_name)
    
    if feedback_type:
        query = query.where(PredictionFeedback.feedback_type == feedback_type)
    
    if processed_only is not None:
        query = query.where(PredictionFeedback.is_processed == processed_only)
    
    # Apply ordering and pagination
    query = query.order_by(desc(PredictionFeedback.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    feedback_entries = result.scalars().all()
    
    return [PredictionFeedbackInfo.from_orm(feedback) for feedback in feedback_entries]


@router.get("/accuracy-validations", response_model=List[ModelAccuracyValidationInfo])
async def get_accuracy_validations(
    model_name: Optional[str] = Query(None),
    validation_type: Optional[str] = Query(None),
    meets_target_only: Optional[bool] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get model accuracy validation results."""
    
    query = select(ModelAccuracyValidation)
    
    # Apply filters
    if model_name:
        query = query.where(ModelAccuracyValidation.model_name == model_name)
    
    if validation_type:
        query = query.where(ModelAccuracyValidation.validation_type == validation_type)
    
    if meets_target_only is not None:
        query = query.where(ModelAccuracyValidation.meets_85_95_target == meets_target_only)
    
    # Apply ordering and pagination
    query = query.order_by(desc(ModelAccuracyValidation.validation_date))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    validations = result.scalars().all()
    
    return [ModelAccuracyValidationInfo.from_orm(validation) for validation in validations]


@router.get("/metrics", response_model=List[ModelPerformanceMetricInfo])
async def get_performance_metrics(
    model_name: Optional[str] = Query(None),
    model_type: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    meets_targets_only: Optional[bool] = Query(None),
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get model performance metrics."""
    
    query = select(ModelPerformanceMetric)
    
    # Apply filters
    if model_name:
        query = query.where(ModelPerformanceMetric.model_name == model_name)
    
    if model_type:
        query = query.where(ModelPerformanceMetric.model_type == model_type)
    
    if start_date:
        query = query.where(ModelPerformanceMetric.evaluation_date >= start_date)
    
    if end_date:
        query = query.where(ModelPerformanceMetric.evaluation_date <= end_date)
    
    if meets_targets_only is not None:
        query = query.where(
            and_(
                ModelPerformanceMetric.meets_accuracy_target == meets_targets_only,
                ModelPerformanceMetric.meets_latency_target == meets_targets_only
            )
        )
    
    # Apply ordering and pagination
    query = query.order_by(desc(ModelPerformanceMetric.evaluation_date))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    metrics = result.scalars().all()
    
    return [ModelPerformanceMetricInfo.from_orm(metric) for metric in metrics]


@router.get("/summary", response_model=ModelPerformanceSummary)
async def get_performance_summary(
    model_name: Optional[str] = Query(None),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get model performance summary."""
    
    summary = await model_performance_service.get_model_performance_summary(
        model_name=model_name,
        days=days,
        db=db
    )
    
    if "error" in summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=summary["error"]
        )
    
    return ModelPerformanceSummary(**summary)


@router.get("/shap-explanations", response_model=List[SHAPExplanationInfo])
async def get_shap_explanations(
    prediction_id: Optional[str] = Query(None),
    model_name: Optional[str] = Query(None),
    user_id: Optional[UUID] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get SHAP explanation records."""
    
    query = select(SHAPExplanationRecord)
    
    # Non-admin users can only see their own explanations
    if current_user.role not in ["admin", "manager"] and not user_id:
        query = query.where(SHAPExplanationRecord.user_id == current_user.id)
    elif user_id and current_user.role in ["admin", "manager"]:
        query = query.where(SHAPExplanationRecord.user_id == user_id)
    
    # Apply filters
    if prediction_id:
        query = query.where(SHAPExplanationRecord.prediction_id == prediction_id)
    
    if model_name:
        query = query.where(SHAPExplanationRecord.model_name == model_name)
    
    # Apply ordering and pagination
    query = query.order_by(desc(SHAPExplanationRecord.created_at))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    explanations = result.scalars().all()
    
    return [SHAPExplanationInfo.from_orm(explanation) for explanation in explanations]


@router.get("/accuracy-compliance")
async def get_accuracy_compliance_status(
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist", "researcher"])),
    db: AsyncSession = Depends(get_db)
):
    """Get accuracy compliance status against 85-95% target."""
    
    # Get latest validation results
    latest_validations_query = select(ModelAccuracyValidation).order_by(
        desc(ModelAccuracyValidation.validation_date)
    ).limit(10)
    
    result = await db.execute(latest_validations_query)
    validations = result.scalars().all()
    
    if not validations:
        return {"error": "No validation data available"}
    
    # Calculate compliance statistics
    total_validations = len(validations)
    compliant_validations = sum(1 for v in validations if v.meets_85_95_target)
    compliance_rate = compliant_validations / total_validations if total_validations > 0 else 0
    
    # Get accuracy distribution
    accuracies = [float(v.overall_accuracy) for v in validations]
    avg_accuracy = sum(accuracies) / len(accuracies) if accuracies else 0
    
    # Categorize models by compliance
    compliant_models = []
    non_compliant_models = []
    
    for validation in validations:
        model_info = {
            "model_name": validation.model_name,
            "model_version": validation.model_version,
            "accuracy": float(validation.overall_accuracy),
            "validation_date": validation.validation_date
        }
        
        if validation.meets_85_95_target:
            compliant_models.append(model_info)
        else:
            non_compliant_models.append(model_info)
    
    return {
        "compliance_summary": {
            "total_models_validated": total_validations,
            "compliant_models": compliant_validations,
            "compliance_rate_percent": compliance_rate * 100,
            "average_accuracy_percent": avg_accuracy * 100,
            "target_range": "85-95%"
        },
        "compliant_models": compliant_models,
        "non_compliant_models": non_compliant_models,
        "accuracy_distribution": {
            "below_85": sum(1 for a in accuracies if a < 0.85),
            "85_to_95": sum(1 for a in accuracies if 0.85 <= a <= 0.95),
            "above_95": sum(1 for a in accuracies if a > 0.95)
        }
    }


@router.get("/feedback-analytics")
async def get_feedback_analytics(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(require_roles(["admin", "manager", "agronomist"])),
    db: AsyncSession = Depends(get_db)
):
    """Get prediction feedback analytics."""
    
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get feedback in the specified period
    feedback_query = select(PredictionFeedback).where(
        PredictionFeedback.created_at >= start_date
    )
    
    result = await db.execute(feedback_query)
    feedback_entries = result.scalars().all()
    
    if not feedback_entries:
        return {"error": "No feedback data available for the specified period"}
    
    # Analyze feedback
    total_feedback = len(feedback_entries)
    feedback_by_type = {}
    feedback_by_rating = {}
    feedback_by_model = {}
    
    for feedback in feedback_entries:
        # By type
        feedback_type = feedback.feedback_type
        feedback_by_type[feedback_type] = feedback_by_type.get(feedback_type, 0) + 1
        
        # By rating
        if feedback.feedback_rating:
            rating = feedback.feedback_rating
            feedback_by_rating[rating] = feedback_by_rating.get(rating, 0) + 1
        
        # By model
        model = feedback.model_name
        feedback_by_model[model] = feedback_by_model.get(model, 0) + 1
    
    # Calculate average rating
    ratings = [f.feedback_rating for f in feedback_entries if f.feedback_rating]
    avg_rating = sum(ratings) / len(ratings) if ratings else 0
    
    # Count processed feedback
    processed_feedback = sum(1 for f in feedback_entries if f.is_processed)
    processing_rate = processed_feedback / total_feedback if total_feedback > 0 else 0
    
    return {
        "period_summary": {
            "total_feedback_entries": total_feedback,
            "processed_entries": processed_feedback,
            "processing_rate_percent": processing_rate * 100,
            "average_rating": avg_rating,
            "period_days": days
        },
        "feedback_by_type": feedback_by_type,
        "feedback_by_rating": feedback_by_rating,
        "feedback_by_model": feedback_by_model,
        "rating_distribution": {
            "excellent_5": feedback_by_rating.get(5, 0),
            "good_4": feedback_by_rating.get(4, 0),
            "average_3": feedback_by_rating.get(3, 0),
            "poor_2": feedback_by_rating.get(2, 0),
            "very_poor_1": feedback_by_rating.get(1, 0)
        }
    }
