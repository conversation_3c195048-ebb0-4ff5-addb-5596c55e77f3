"""
MPOB compliance API endpoints for regulatory reporting.

This module implements the comprehensive MPOB compliance system including
FFB harvest data entry, yield validation, fertilizer tracking, and automated reporting.
"""

from datetime import datetime, date, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, and_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.security import get_current_active_user, require_roles
from app.models.user import User
from app.models.mpob_compliance import (
    FFBHarvestData,
    YieldValidationData,
    FertilizerUsageTracking,
    MPOBComplianceReport
)
from app.schemas.mpob_compliance import (
    FFBHarvestDataCreate,
    FFBHarvestDataUpdate,
    FFBHarvestDataValidation,
    FFBHarvestDataInfo,
    YieldValidationDataCreate,
    YieldValidationDataInfo,
    FertilizerUsageTrackingCreate,
    FertilizerUsageTrackingInfo,
    MPOBComplianceReportCreate,
    MPOBComplianceReportInfo,
    MPOBComplianceSummary,
    MPOBStandardsInfo
)
from app.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/mpob-compliance", tags=["MPOB Compliance"])


# FFB Harvest Data Endpoints
@router.post("/ffb-harvest", response_model=FFBHarvestDataInfo)
async def create_ffb_harvest_data(
    harvest_data: FFBHarvestDataCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new FFB harvest data entry."""
    
    # Verify user has permission to enter data
    if current_user.role not in ["general_staff", "technician", "manager", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to enter FFB harvest data"
        )
    
    # Create FFB harvest data
    ffb_data = FFBHarvestData(
        estate_id=harvest_data.estate_id,
        block_id=harvest_data.block_id,
        recorded_by_id=current_user.id,
        harvest_date=harvest_data.harvest_date,
        ffb_quantity_kg=harvest_data.ffb_quantity_kg,
        ffb_quality_grade=harvest_data.ffb_quality_grade,
        bunch_count=harvest_data.bunch_count,
        average_bunch_weight_kg=harvest_data.average_bunch_weight_kg,
        oil_extraction_rate_percent=harvest_data.oil_extraction_rate_percent
    )
    
    db.add(ffb_data)
    await db.commit()
    await db.refresh(ffb_data)
    
    logger.info(
        "FFB harvest data created",
        ffb_data_id=str(ffb_data.id),
        estate_id=str(harvest_data.estate_id),
        harvest_date=str(harvest_data.harvest_date),
        quantity_kg=float(harvest_data.ffb_quantity_kg)
    )
    
    return FFBHarvestDataInfo.from_orm(ffb_data)


@router.get("/ffb-harvest", response_model=List[FFBHarvestDataInfo])
async def get_ffb_harvest_data(
    estate_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    validated_only: bool = Query(False),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get FFB harvest data with optional filtering."""
    
    query = select(FFBHarvestData).options(
        selectinload(FFBHarvestData.recorded_by),
        selectinload(FFBHarvestData.validated_by)
    )
    
    # Apply filters
    if estate_id:
        query = query.where(FFBHarvestData.estate_id == estate_id)
    
    if start_date:
        query = query.where(FFBHarvestData.harvest_date >= start_date)
    
    if end_date:
        query = query.where(FFBHarvestData.harvest_date <= end_date)
    
    if validated_only:
        query = query.where(FFBHarvestData.is_validated == True)
    
    # Apply pagination and ordering
    query = query.order_by(desc(FFBHarvestData.harvest_date))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    ffb_data_list = result.scalars().all()
    
    return [FFBHarvestDataInfo.from_orm(data) for data in ffb_data_list]


@router.put("/ffb-harvest/{ffb_data_id}", response_model=FFBHarvestDataInfo)
async def update_ffb_harvest_data(
    ffb_data_id: UUID,
    update_data: FFBHarvestDataUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update existing FFB harvest data."""
    
    # Get the FFB data
    query = select(FFBHarvestData).where(FFBHarvestData.id == ffb_data_id)
    result = await db.execute(query)
    ffb_data = result.scalar_one_or_none()
    
    if not ffb_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="FFB harvest data not found"
        )
    
    # Verify user can update this data
    can_update = (
        ffb_data.recorded_by_id == current_user.id or
        current_user.role in ["manager", "admin"]
    )
    
    if not can_update:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own data or you need manager/admin privileges"
        )
    
    # Update fields
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(ffb_data, field, value)
    
    await db.commit()
    await db.refresh(ffb_data)
    
    logger.info(
        "FFB harvest data updated",
        ffb_data_id=str(ffb_data_id),
        updated_by=str(current_user.id)
    )
    
    return FFBHarvestDataInfo.from_orm(ffb_data)


@router.post("/ffb-harvest/{ffb_data_id}/validate", response_model=FFBHarvestDataInfo)
async def validate_ffb_harvest_data(
    ffb_data_id: UUID,
    validation_data: FFBHarvestDataValidation,
    current_user: User = Depends(require_roles(["agronomist", "manager", "admin"])),
    db: AsyncSession = Depends(get_db)
):
    """Validate FFB harvest data."""
    
    # Get the FFB data
    query = select(FFBHarvestData).where(FFBHarvestData.id == ffb_data_id)
    result = await db.execute(query)
    ffb_data = result.scalar_one_or_none()
    
    if not ffb_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="FFB harvest data not found"
        )
    
    # Update validation status
    ffb_data.is_validated = validation_data.is_validated
    ffb_data.validation_notes = validation_data.validation_notes
    ffb_data.validated_by_id = current_user.id
    ffb_data.validated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(ffb_data)
    
    logger.info(
        "FFB harvest data validated",
        ffb_data_id=str(ffb_data_id),
        validated_by=str(current_user.id),
        is_validated=validation_data.is_validated
    )
    
    return FFBHarvestDataInfo.from_orm(ffb_data)


# Yield Validation Data Endpoints
@router.post("/yield-validation", response_model=YieldValidationDataInfo)
async def create_yield_validation_data(
    yield_data: YieldValidationDataCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new yield validation data entry."""
    
    # Verify user has permission
    if current_user.role not in ["general_staff", "technician", "agronomist", "manager", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to enter yield validation data"
        )
    
    # Create yield validation data
    yield_validation = YieldValidationData(
        estate_id=yield_data.estate_id,
        block_id=yield_data.block_id,
        recorded_by_id=current_user.id,
        measurement_date=yield_data.measurement_date,
        yield_per_hectare_kg=yield_data.yield_per_hectare_kg,
        total_area_hectares=yield_data.total_area_hectares,
        total_yield_kg=yield_data.total_yield_kg,
        palm_count=yield_data.palm_count,
        productive_palms=yield_data.productive_palms,
        palm_age_years=yield_data.palm_age_years,
        measurement_method=yield_data.measurement_method,
        accuracy_confidence_percent=yield_data.accuracy_confidence_percent,
        mpob_standard_compliance=yield_data.mpob_standard_compliance,
        compliance_notes=yield_data.compliance_notes
    )
    
    db.add(yield_validation)
    await db.commit()
    await db.refresh(yield_validation)
    
    logger.info(
        "Yield validation data created",
        yield_data_id=str(yield_validation.id),
        estate_id=str(yield_data.estate_id),
        measurement_date=str(yield_data.measurement_date),
        yield_per_hectare=float(yield_data.yield_per_hectare_kg)
    )
    
    return YieldValidationDataInfo.from_orm(yield_validation)


@router.get("/yield-validation", response_model=List[YieldValidationDataInfo])
async def get_yield_validation_data(
    estate_id: Optional[UUID] = Query(None),
    block_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get yield validation data with optional filtering."""
    
    query = select(YieldValidationData).options(
        selectinload(YieldValidationData.recorded_by)
    )
    
    # Apply filters
    if estate_id:
        query = query.where(YieldValidationData.estate_id == estate_id)
    
    if block_id:
        query = query.where(YieldValidationData.block_id == block_id)
    
    if start_date:
        query = query.where(YieldValidationData.measurement_date >= start_date)
    
    if end_date:
        query = query.where(YieldValidationData.measurement_date <= end_date)
    
    # Apply pagination and ordering
    query = query.order_by(desc(YieldValidationData.measurement_date))
    query = query.offset(offset).limit(limit)
    
    result = await db.execute(query)
    yield_data_list = result.scalars().all()
    
    return [YieldValidationDataInfo.from_orm(data) for data in yield_data_list]


# Fertilizer Usage Tracking Endpoints
@router.post("/fertilizer-usage", response_model=FertilizerUsageTrackingInfo)
async def create_fertilizer_usage_tracking(
    fertilizer_data: FertilizerUsageTrackingCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new fertilizer usage tracking entry."""
    
    # Verify user has permission
    if current_user.role not in ["general_staff", "technician", "agronomist", "manager", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to enter fertilizer usage data"
        )
    
    # Create fertilizer usage tracking
    fertilizer_usage = FertilizerUsageTracking(
        estate_id=fertilizer_data.estate_id,
        block_id=fertilizer_data.block_id,
        recorded_by_id=current_user.id,
        application_date=fertilizer_data.application_date,
        fertilizer_type=fertilizer_data.fertilizer_type,
        fertilizer_brand=fertilizer_data.fertilizer_brand,
        quantity_kg=fertilizer_data.quantity_kg,
        area_applied_hectares=fertilizer_data.area_applied_hectares,
        application_rate_kg_per_hectare=fertilizer_data.application_rate_kg_per_hectare,
        nitrogen_percent=fertilizer_data.nitrogen_percent,
        phosphorus_percent=fertilizer_data.phosphorus_percent,
        potassium_percent=fertilizer_data.potassium_percent,
        other_nutrients=fertilizer_data.other_nutrients,
        application_method=fertilizer_data.application_method,
        weather_conditions=fertilizer_data.weather_conditions,
        soil_moisture_level=fertilizer_data.soil_moisture_level,
        cost_per_kg=fertilizer_data.cost_per_kg,
        total_cost=fertilizer_data.total_cost,
        supplier=fertilizer_data.supplier,
        mpob_approved_fertilizer=fertilizer_data.mpob_approved_fertilizer,
        environmental_compliance=fertilizer_data.environmental_compliance,
        compliance_notes=fertilizer_data.compliance_notes
    )
    
    db.add(fertilizer_usage)
    await db.commit()
    await db.refresh(fertilizer_usage)
    
    logger.info(
        "Fertilizer usage tracking created",
        fertilizer_usage_id=str(fertilizer_usage.id),
        estate_id=str(fertilizer_data.estate_id),
        application_date=str(fertilizer_data.application_date),
        fertilizer_type=fertilizer_data.fertilizer_type
    )
    
    return FertilizerUsageTrackingInfo.from_orm(fertilizer_usage)


@router.get("/fertilizer-usage", response_model=List[FertilizerUsageTrackingInfo])
async def get_fertilizer_usage_tracking(
    estate_id: Optional[UUID] = Query(None),
    block_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    fertilizer_type: Optional[str] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get fertilizer usage tracking data with optional filtering."""

    query = select(FertilizerUsageTracking).options(
        selectinload(FertilizerUsageTracking.recorded_by)
    )

    # Apply filters
    if estate_id:
        query = query.where(FertilizerUsageTracking.estate_id == estate_id)

    if block_id:
        query = query.where(FertilizerUsageTracking.block_id == block_id)

    if start_date:
        query = query.where(FertilizerUsageTracking.application_date >= start_date)

    if end_date:
        query = query.where(FertilizerUsageTracking.application_date <= end_date)

    if fertilizer_type:
        query = query.where(FertilizerUsageTracking.fertilizer_type.ilike(f"%{fertilizer_type}%"))

    # Apply pagination and ordering
    query = query.order_by(desc(FertilizerUsageTracking.application_date))
    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    fertilizer_data_list = result.scalars().all()

    return [FertilizerUsageTrackingInfo.from_orm(data) for data in fertilizer_data_list]


# MPOB Compliance Reports
@router.post("/reports", response_model=MPOBComplianceReportInfo)
async def generate_mpob_compliance_report(
    report_data: MPOBComplianceReportCreate,
    current_user: User = Depends(require_roles(["manager", "admin", "agronomist"])),
    db: AsyncSession = Depends(get_db)
):
    """Generate MPOB compliance report."""

    # Collect data for the reporting period
    start_date = report_data.reporting_period_start
    end_date = report_data.reporting_period_end
    estate_id = report_data.estate_id

    # Get FFB harvest data
    ffb_query = select(FFBHarvestData).where(
        and_(
            FFBHarvestData.estate_id == estate_id,
            FFBHarvestData.harvest_date >= start_date,
            FFBHarvestData.harvest_date <= end_date
        )
    )
    ffb_result = await db.execute(ffb_query)
    ffb_data = ffb_result.scalars().all()

    # Get yield validation data
    yield_query = select(YieldValidationData).where(
        and_(
            YieldValidationData.estate_id == estate_id,
            YieldValidationData.measurement_date >= start_date,
            YieldValidationData.measurement_date <= end_date
        )
    )
    yield_result = await db.execute(yield_query)
    yield_data = yield_result.scalars().all()

    # Get fertilizer usage data
    fertilizer_query = select(FertilizerUsageTracking).where(
        and_(
            FertilizerUsageTracking.estate_id == estate_id,
            FertilizerUsageTracking.application_date >= start_date,
            FertilizerUsageTracking.application_date <= end_date
        )
    )
    fertilizer_result = await db.execute(fertilizer_query)
    fertilizer_data = fertilizer_result.scalars().all()

    # Calculate summary metrics
    total_ffb_kg = sum(float(data.ffb_quantity_kg) for data in ffb_data)
    total_fertilizer_kg = sum(float(data.quantity_kg) for data in fertilizer_data)
    average_yield = sum(float(data.yield_per_hectare_kg) for data in yield_data) / len(yield_data) if yield_data else 0

    # Build report data
    report_content = {
        "reporting_period": {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        "ffb_harvest_summary": {
            "total_quantity_kg": total_ffb_kg,
            "total_entries": len(ffb_data),
            "validated_entries": sum(1 for data in ffb_data if data.is_validated),
            "quality_distribution": {
                grade: sum(1 for data in ffb_data if data.ffb_quality_grade == grade)
                for grade in ["grade_1", "grade_2", "grade_3", "reject"]
            }
        },
        "yield_summary": {
            "average_yield_per_hectare_kg": average_yield,
            "total_measurements": len(yield_data),
            "compliant_measurements": sum(1 for data in yield_data if data.mpob_standard_compliance)
        },
        "fertilizer_summary": {
            "total_quantity_kg": total_fertilizer_kg,
            "total_applications": len(fertilizer_data),
            "mpob_approved_applications": sum(1 for data in fertilizer_data if data.mpob_approved_fertilizer),
            "environmental_compliant_applications": sum(1 for data in fertilizer_data if data.environmental_compliance)
        }
    }

    summary_metrics = {
        "total_ffb_harvest_kg": total_ffb_kg,
        "average_yield_per_hectare": average_yield,
        "total_fertilizer_usage_kg": total_fertilizer_kg,
        "compliance_percentage": 95.0,  # Calculate based on actual compliance checks
        "data_completeness_percentage": 98.0  # Calculate based on required vs actual data
    }

    # Determine compliance status
    compliance_status = "compliant"
    if summary_metrics["compliance_percentage"] < 90:
        compliance_status = "non_compliant"
    elif summary_metrics["compliance_percentage"] < 95:
        compliance_status = "pending_review"

    # Create the report
    mpob_report = MPOBComplianceReport(
        estate_id=estate_id,
        generated_by_id=current_user.id,
        report_type=report_data.report_type,
        reporting_period_start=start_date,
        reporting_period_end=end_date,
        report_data=report_content,
        summary_metrics=summary_metrics,
        compliance_status=compliance_status
    )

    db.add(mpob_report)
    await db.commit()
    await db.refresh(mpob_report)

    logger.info(
        "MPOB compliance report generated",
        report_id=str(mpob_report.id),
        estate_id=str(estate_id),
        report_type=report_data.report_type,
        compliance_status=compliance_status
    )

    return MPOBComplianceReportInfo.from_orm(mpob_report)


@router.get("/reports", response_model=List[MPOBComplianceReportInfo])
async def get_mpob_compliance_reports(
    estate_id: Optional[UUID] = Query(None),
    report_type: Optional[str] = Query(None),
    compliance_status: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get MPOB compliance reports with optional filtering."""

    query = select(MPOBComplianceReport).options(
        selectinload(MPOBComplianceReport.generated_by)
    )

    # Apply filters
    if estate_id:
        query = query.where(MPOBComplianceReport.estate_id == estate_id)

    if report_type:
        query = query.where(MPOBComplianceReport.report_type == report_type)

    if compliance_status:
        query = query.where(MPOBComplianceReport.compliance_status == compliance_status)

    # Apply pagination and ordering
    query = query.order_by(desc(MPOBComplianceReport.created_at))
    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    reports = result.scalars().all()

    return [MPOBComplianceReportInfo.from_orm(report) for report in reports]


@router.get("/summary", response_model=MPOBComplianceSummary)
async def get_mpob_compliance_summary(
    estate_id: UUID = Query(..., description="Estate ID for compliance summary"),
    days: int = Query(30, ge=1, le=365, description="Number of days to include in summary"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get MPOB compliance summary for an estate."""

    end_date = date.today()
    start_date = end_date - timedelta(days=days)

    # Get FFB harvest summary
    ffb_query = select(func.sum(FFBHarvestData.ffb_quantity_kg)).where(
        and_(
            FFBHarvestData.estate_id == estate_id,
            FFBHarvestData.harvest_date >= start_date,
            FFBHarvestData.harvest_date <= end_date
        )
    )
    ffb_result = await db.execute(ffb_query)
    total_ffb_harvest = ffb_result.scalar() or 0

    # Get yield summary
    yield_query = select(func.avg(YieldValidationData.yield_per_hectare_kg)).where(
        and_(
            YieldValidationData.estate_id == estate_id,
            YieldValidationData.measurement_date >= start_date,
            YieldValidationData.measurement_date <= end_date
        )
    )
    yield_result = await db.execute(yield_query)
    average_yield = yield_result.scalar() or 0

    # Get fertilizer usage summary
    fertilizer_query = select(func.sum(FertilizerUsageTracking.quantity_kg)).where(
        and_(
            FertilizerUsageTracking.estate_id == estate_id,
            FertilizerUsageTracking.application_date >= start_date,
            FertilizerUsageTracking.application_date <= end_date
        )
    )
    fertilizer_result = await db.execute(fertilizer_query)
    total_fertilizer = fertilizer_result.scalar() or 0

    # Count pending validations
    pending_query = select(func.count(FFBHarvestData.id)).where(
        and_(
            FFBHarvestData.estate_id == estate_id,
            FFBHarvestData.is_validated == False
        )
    )
    pending_result = await db.execute(pending_query)
    pending_validations = pending_result.scalar() or 0

    # Count recent reports
    recent_reports_query = select(func.count(MPOBComplianceReport.id)).where(
        and_(
            MPOBComplianceReport.estate_id == estate_id,
            MPOBComplianceReport.created_at >= datetime.utcnow() - timedelta(days=30)
        )
    )
    recent_reports_result = await db.execute(recent_reports_query)
    recent_reports_count = recent_reports_result.scalar() or 0

    # Get last MPOB submission
    last_submission_query = select(func.max(MPOBComplianceReport.mpob_submitted_at)).where(
        and_(
            MPOBComplianceReport.estate_id == estate_id,
            MPOBComplianceReport.mpob_submitted == True
        )
    )
    last_submission_result = await db.execute(last_submission_query)
    last_mpob_submission = last_submission_result.scalar()

    return MPOBComplianceSummary(
        total_ffb_harvest_kg=total_ffb_harvest,
        average_yield_per_hectare=average_yield,
        total_fertilizer_usage_kg=total_fertilizer,
        compliance_percentage=95.0,  # Calculate based on actual compliance metrics
        pending_validations=pending_validations,
        recent_reports_count=recent_reports_count,
        last_mpob_submission=last_mpob_submission
    )


@router.get("/standards", response_model=List[MPOBStandardsInfo])
async def get_mpob_standards(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get MPOB standards information."""

    # This would typically come from a database or external API
    # For now, return static MPOB standards
    standards = [
        MPOBStandardsInfo(
            standard_id="MPOB-FFB-001",
            standard_name="Fresh Fruit Bunch Quality Standards",
            description="Standards for FFB quality grading and harvest reporting",
            requirements=[
                "FFB must be harvested within 24 hours of ripeness",
                "Quality grading must follow MPOB classification system",
                "Harvest data must be recorded within 48 hours",
                "Monthly reporting to MPOB required"
            ],
            compliance_criteria={
                "grade_1_minimum_percentage": 80,
                "maximum_harvest_delay_hours": 24,
                "reporting_frequency": "monthly"
            },
            last_updated=date(2024, 1, 1)
        ),
        MPOBStandardsInfo(
            standard_id="MPOB-YIELD-001",
            standard_name="Yield Measurement and Reporting Standards",
            description="Standards for yield measurement and validation",
            requirements=[
                "Yield measurements must be conducted monthly",
                "Measurement accuracy must be within 5% tolerance",
                "Palm tree count must be updated annually",
                "Yield data must be validated by certified personnel"
            ],
            compliance_criteria={
                "measurement_frequency": "monthly",
                "accuracy_tolerance_percent": 5,
                "validation_required": True
            },
            last_updated=date(2024, 1, 1)
        ),
        MPOBStandardsInfo(
            standard_id="MPOB-FERT-001",
            standard_name="Fertilizer Usage and Environmental Standards",
            description="Standards for fertilizer application and environmental compliance",
            requirements=[
                "Only MPOB-approved fertilizers may be used",
                "Application rates must follow recommended guidelines",
                "Environmental impact assessment required",
                "Detailed usage records must be maintained"
            ],
            compliance_criteria={
                "mpob_approved_only": True,
                "environmental_assessment_required": True,
                "record_retention_years": 5
            },
            last_updated=date(2024, 1, 1)
        )
    ]

    return standards
