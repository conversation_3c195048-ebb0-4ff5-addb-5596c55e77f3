"""
Authorization-related Pydantic schemas for General Staff dynamic permissions system.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator

from app.schemas.common import BaseSchema


class AuthorizationRequestCreate(BaseModel):
    """Schema for creating an authorization request."""
    
    target_persona_id: UUID = Field(description="ID of the persona to request authorization from")
    estate_id: UUID = Field(description="ID of the estate for which authorization is requested")
    data_entry_type: str = Field(
        description="Type of data entry permission requested",
        pattern="^(financial_data|operational_incidents|field_observations|research_data|equipment_maintenance|safety_compliance|pest_disease_reports|soil_assessments|treatment_effectiveness)$"
    )
    permission_level: str = Field(
        default="create",
        description="Level of permission requested",
        pattern="^(read_only|create|edit|delete)$"
    )
    justification: str = Field(
        min_length=10,
        max_length=1000,
        description="Justification for the authorization request"
    )
    requested_duration_days: int = Field(
        default=30,
        ge=1,
        le=365,
        description="Requested duration in days (1-365)"
    )


class AuthorizationRequestResponse(BaseModel):
    """Schema for responding to an authorization request."""
    
    status: str = Field(
        description="Response status",
        pattern="^(approved|rejected)$"
    )
    response_message: Optional[str] = Field(
        None,
        max_length=1000,
        description="Optional response message"
    )
    approved_duration_days: Optional[int] = Field(
        None,
        ge=1,
        le=365,
        description="Approved duration in days (required if approved)"
    )
    conditions: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional conditions or restrictions"
    )
    
    @validator('approved_duration_days')
    def validate_approved_duration(cls, v, values):
        """Validate that approved_duration_days is provided when status is approved."""
        if values.get('status') == 'approved' and v is None:
            raise ValueError('approved_duration_days is required when status is approved')
        return v


class AuthorizationRequestInfo(BaseSchema):
    """Schema for authorization request information."""
    
    id: UUID
    requester_id: UUID
    target_persona_id: UUID
    estate_id: UUID
    data_entry_type: str
    permission_level: str
    justification: str
    requested_duration_days: int
    status: str
    response_message: Optional[str]
    responded_at: Optional[datetime]
    responded_by_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    
    # Nested objects
    requester: Optional["UserResponse"] = None
    target_persona: Optional["UserResponse"] = None
    responded_by: Optional["UserResponse"] = None


class AuthorizationCreate(BaseModel):
    """Schema for creating an authorization (internal use)."""
    
    general_staff_user_id: UUID
    authorizing_persona_id: UUID
    estate_id: UUID
    authorizing_persona: str
    data_entry_type: str
    permission_level: str
    expires_at: datetime
    conditions: Optional[Dict[str, Any]] = None


class AuthorizationInfo(BaseSchema):
    """Schema for authorization information."""
    
    id: UUID
    general_staff_user_id: UUID
    authorizing_persona_id: UUID
    estate_id: UUID
    authorizing_persona: str
    data_entry_type: str
    permission_level: str
    granted_at: datetime
    expires_at: datetime
    is_active: bool
    revoked_at: Optional[datetime]
    revoked_by_id: Optional[UUID]
    revocation_reason: Optional[str]
    last_used_at: Optional[datetime]
    usage_count: int
    conditions: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_expired: bool
    is_valid: bool
    
    # Nested objects
    general_staff_user: Optional["UserResponse"] = None
    authorizing_persona_user: Optional["UserResponse"] = None
    revoked_by: Optional["UserResponse"] = None


class AuthorizationRevoke(BaseModel):
    """Schema for revoking an authorization."""
    
    revocation_reason: str = Field(
        min_length=5,
        max_length=500,
        description="Reason for revoking the authorization"
    )


class AuthorizationUsage(BaseModel):
    """Schema for tracking authorization usage."""
    
    authorization_id: UUID
    action_performed: str = Field(description="Description of the action performed")
    data_modified: Optional[Dict[str, Any]] = Field(
        None,
        description="Summary of data that was modified"
    )


class AuthorizationAuditLogInfo(BaseSchema):
    """Schema for authorization audit log information."""
    
    id: UUID
    authorization_id: Optional[UUID]
    user_id: UUID
    action: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    
    # Nested objects
    user: Optional["UserResponse"] = None


class AuthorizationSummary(BaseModel):
    """Schema for authorization summary statistics."""
    
    total_active_authorizations: int = Field(description="Total number of active authorizations")
    total_pending_requests: int = Field(description="Total number of pending requests")
    authorizations_by_type: Dict[str, int] = Field(description="Count of authorizations by data entry type")
    authorizations_expiring_soon: int = Field(description="Number of authorizations expiring within 7 days")
    recent_activity_count: int = Field(description="Number of authorization activities in last 24 hours")


class AvailableDataEntryForms(BaseModel):
    """Schema for available data entry forms based on current authorizations."""
    
    core_mpob_forms: list[str] = Field(description="Core MPOB compliance forms always available")
    authorized_forms: list[Dict[str, Any]] = Field(description="Forms available through current authorizations")
    
    class AuthorizedForm(BaseModel):
        data_entry_type: str
        permission_level: str
        expires_at: datetime
        authorizing_persona: str
        conditions: Optional[Dict[str, Any]]


# Forward references for nested objects
from app.schemas.user import UserResponse
AuthorizationRequestInfo.model_rebuild()
AuthorizationInfo.model_rebuild()
AuthorizationAuditLogInfo.model_rebuild()
