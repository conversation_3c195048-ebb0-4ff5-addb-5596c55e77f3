"""
MPOB compliance-related Pydantic schemas for regulatory reporting.
"""

from datetime import datetime, date
from typing import Optional, Dict, Any, List
from uuid import UUID
from decimal import Decimal

from pydantic import BaseModel, Field, validator

from app.schemas.common import BaseSchema


class FFBHarvestDataCreate(BaseModel):
    """Schema for creating FFB harvest data."""
    
    estate_id: UUID = Field(description="ID of the estate")
    block_id: Optional[UUID] = Field(None, description="ID of the specific block")
    harvest_date: date = Field(description="Date of harvest")
    ffb_quantity_kg: Decimal = Field(
        gt=0,
        max_digits=10,
        decimal_places=2,
        description="FFB quantity in kilograms"
    )
    ffb_quality_grade: str = Field(
        default="grade_1",
        pattern="^(grade_1|grade_2|grade_3|reject)$",
        description="FFB quality grade"
    )
    bunch_count: Optional[int] = Field(None, ge=0, description="Number of bunches")
    average_bunch_weight_kg: Optional[Decimal] = Field(
        None,
        gt=0,
        max_digits=8,
        decimal_places=2,
        description="Average bunch weight in kg"
    )
    oil_extraction_rate_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Oil extraction rate percentage"
    )


class FFBHarvestDataUpdate(BaseModel):
    """Schema for updating FFB harvest data."""
    
    ffb_quantity_kg: Optional[Decimal] = Field(
        None,
        gt=0,
        max_digits=10,
        decimal_places=2
    )
    ffb_quality_grade: Optional[str] = Field(
        None,
        pattern="^(grade_1|grade_2|grade_3|reject)$"
    )
    bunch_count: Optional[int] = Field(None, ge=0)
    average_bunch_weight_kg: Optional[Decimal] = Field(
        None,
        gt=0,
        max_digits=8,
        decimal_places=2
    )
    oil_extraction_rate_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2
    )


class FFBHarvestDataValidation(BaseModel):
    """Schema for validating FFB harvest data."""
    
    is_validated: bool = Field(description="Validation status")
    validation_notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Validation notes"
    )


class FFBHarvestDataInfo(BaseSchema):
    """Schema for FFB harvest data information."""
    
    id: UUID
    estate_id: UUID
    block_id: Optional[UUID]
    recorded_by_id: UUID
    harvest_date: date
    ffb_quantity_kg: Decimal
    ffb_quality_grade: str
    bunch_count: Optional[int]
    average_bunch_weight_kg: Optional[Decimal]
    oil_extraction_rate_percent: Optional[Decimal]
    is_validated: bool
    validated_by_id: Optional[UUID]
    validated_at: Optional[datetime]
    validation_notes: Optional[str]
    mpob_reported: bool
    mpob_report_id: Optional[str]
    mpob_reported_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class YieldValidationDataCreate(BaseModel):
    """Schema for creating yield validation data."""
    
    estate_id: UUID = Field(description="ID of the estate")
    block_id: UUID = Field(description="ID of the block")
    measurement_date: date = Field(description="Date of measurement")
    yield_per_hectare_kg: Decimal = Field(
        gt=0,
        max_digits=10,
        decimal_places=2,
        description="Yield per hectare in kg"
    )
    total_area_hectares: Decimal = Field(
        gt=0,
        max_digits=10,
        decimal_places=4,
        description="Total area in hectares"
    )
    total_yield_kg: Decimal = Field(
        gt=0,
        max_digits=12,
        decimal_places=2,
        description="Total yield in kg"
    )
    palm_count: Optional[int] = Field(None, ge=0, description="Number of palm trees")
    productive_palms: Optional[int] = Field(None, ge=0, description="Number of productive palms")
    palm_age_years: Optional[int] = Field(None, ge=0, le=100, description="Average palm age in years")
    measurement_method: str = Field(
        default="manual_count",
        pattern="^(manual_count|sensor_data|drone_survey|combined)$",
        description="Measurement method used"
    )
    accuracy_confidence_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Accuracy confidence percentage"
    )
    mpob_standard_compliance: bool = Field(
        default=True,
        description="MPOB standard compliance"
    )
    compliance_notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Compliance notes"
    )


class YieldValidationDataInfo(BaseSchema):
    """Schema for yield validation data information."""
    
    id: UUID
    estate_id: UUID
    block_id: UUID
    recorded_by_id: UUID
    measurement_date: date
    yield_per_hectare_kg: Decimal
    total_area_hectares: Decimal
    total_yield_kg: Decimal
    palm_count: Optional[int]
    productive_palms: Optional[int]
    palm_age_years: Optional[int]
    measurement_method: str
    accuracy_confidence_percent: Optional[Decimal]
    mpob_standard_compliance: bool
    compliance_notes: Optional[str]
    created_at: datetime
    updated_at: datetime


class FertilizerUsageTrackingCreate(BaseModel):
    """Schema for creating fertilizer usage tracking data."""
    
    estate_id: UUID = Field(description="ID of the estate")
    block_id: Optional[UUID] = Field(None, description="ID of the block")
    application_date: date = Field(description="Date of fertilizer application")
    fertilizer_type: str = Field(
        min_length=1,
        max_length=100,
        description="Type of fertilizer"
    )
    fertilizer_brand: Optional[str] = Field(
        None,
        max_length=100,
        description="Brand of fertilizer"
    )
    quantity_kg: Decimal = Field(
        gt=0,
        max_digits=10,
        decimal_places=2,
        description="Quantity in kg"
    )
    area_applied_hectares: Decimal = Field(
        gt=0,
        max_digits=10,
        decimal_places=4,
        description="Area applied in hectares"
    )
    application_rate_kg_per_hectare: Decimal = Field(
        gt=0,
        max_digits=8,
        decimal_places=2,
        description="Application rate in kg per hectare"
    )
    nitrogen_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Nitrogen percentage"
    )
    phosphorus_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Phosphorus percentage"
    )
    potassium_percent: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Potassium percentage"
    )
    other_nutrients: Optional[Dict[str, Any]] = Field(
        None,
        description="Other nutrient composition"
    )
    application_method: str = Field(
        default="broadcast",
        pattern="^(broadcast|spot_application|fertigation|foliar)$",
        description="Application method"
    )
    weather_conditions: Optional[str] = Field(
        None,
        max_length=200,
        description="Weather conditions during application"
    )
    soil_moisture_level: Optional[str] = Field(
        None,
        pattern="^(dry|moist|wet)$",
        description="Soil moisture level"
    )
    cost_per_kg: Optional[Decimal] = Field(
        None,
        ge=0,
        max_digits=8,
        decimal_places=2,
        description="Cost per kg"
    )
    total_cost: Optional[Decimal] = Field(
        None,
        ge=0,
        max_digits=10,
        decimal_places=2,
        description="Total cost"
    )
    supplier: Optional[str] = Field(
        None,
        max_length=100,
        description="Fertilizer supplier"
    )
    mpob_approved_fertilizer: bool = Field(
        default=True,
        description="MPOB approved fertilizer"
    )
    environmental_compliance: bool = Field(
        default=True,
        description="Environmental compliance"
    )
    compliance_notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Compliance notes"
    )


class FertilizerUsageTrackingInfo(BaseSchema):
    """Schema for fertilizer usage tracking information."""
    
    id: UUID
    estate_id: UUID
    block_id: Optional[UUID]
    recorded_by_id: UUID
    application_date: date
    fertilizer_type: str
    fertilizer_brand: Optional[str]
    quantity_kg: Decimal
    area_applied_hectares: Decimal
    application_rate_kg_per_hectare: Decimal
    nitrogen_percent: Optional[Decimal]
    phosphorus_percent: Optional[Decimal]
    potassium_percent: Optional[Decimal]
    other_nutrients: Optional[Dict[str, Any]]
    application_method: str
    weather_conditions: Optional[str]
    soil_moisture_level: Optional[str]
    cost_per_kg: Optional[Decimal]
    total_cost: Optional[Decimal]
    supplier: Optional[str]
    mpob_approved_fertilizer: bool
    environmental_compliance: bool
    compliance_notes: Optional[str]
    created_at: datetime
    updated_at: datetime


class MPOBComplianceReportCreate(BaseModel):
    """Schema for creating MPOB compliance reports."""
    
    estate_id: UUID = Field(description="ID of the estate")
    report_type: str = Field(
        pattern="^(monthly|quarterly|annual|custom)$",
        description="Type of report"
    )
    reporting_period_start: date = Field(description="Start date of reporting period")
    reporting_period_end: date = Field(description="End date of reporting period")
    
    @validator('reporting_period_end')
    def validate_period_end(cls, v, values):
        """Validate that end date is after start date."""
        start_date = values.get('reporting_period_start')
        if start_date and v <= start_date:
            raise ValueError('reporting_period_end must be after reporting_period_start')
        return v


class MPOBComplianceReportInfo(BaseSchema):
    """Schema for MPOB compliance report information."""
    
    id: UUID
    estate_id: UUID
    generated_by_id: UUID
    report_type: str
    reporting_period_start: date
    reporting_period_end: date
    report_data: Dict[str, Any]
    summary_metrics: Dict[str, Any]
    compliance_status: str
    mpob_submitted: bool
    mpob_submission_id: Optional[str]
    mpob_submitted_at: Optional[datetime]
    mpob_response: Optional[Dict[str, Any]]
    report_file_path: Optional[str]
    supporting_documents: Optional[List[str]]
    created_at: datetime
    updated_at: datetime


class MPOBComplianceSummary(BaseModel):
    """Schema for MPOB compliance summary."""
    
    total_ffb_harvest_kg: Decimal = Field(description="Total FFB harvest in kg")
    average_yield_per_hectare: Decimal = Field(description="Average yield per hectare")
    total_fertilizer_usage_kg: Decimal = Field(description="Total fertilizer usage in kg")
    compliance_percentage: Decimal = Field(description="Overall compliance percentage")
    pending_validations: int = Field(description="Number of pending validations")
    recent_reports_count: int = Field(description="Number of recent reports")
    last_mpob_submission: Optional[datetime] = Field(description="Last MPOB submission date")


class MPOBStandardsInfo(BaseModel):
    """Schema for MPOB standards information."""
    
    standard_id: str = Field(description="MPOB standard identifier")
    standard_name: str = Field(description="Name of the standard")
    description: str = Field(description="Description of the standard")
    requirements: List[str] = Field(description="List of requirements")
    compliance_criteria: Dict[str, Any] = Field(description="Compliance criteria")
    last_updated: date = Field(description="Last update date of the standard")
