"""
User-related Pydantic schemas for authentication and user management.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator

from app.schemas.common import BaseSchema


class UserBase(BaseModel):
    """Base user schema with common fields."""
    
    email: EmailStr = Field(description="User email address")
    first_name: str = Field(min_length=1, max_length=100, description="User first name")
    last_name: str = Field(min_length=1, max_length=100, description="User last name")
    phone: Optional[str] = Field(None, max_length=20, description="User phone number")


class UserCreate(UserBase):
    """Schema for creating a new user."""
    
    password: str = Field(
        min_length=8,
        max_length=128,
        description="User password (min 8 characters)"
    )
    role: str = Field(
        default="technician",
        pattern="^(admin|manager|agronomist|technician|minister|researcher|general_staff|decision_maker)$",
        description="User role"
    )
    
    @validator("password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v):
            raise ValueError("Password must contain at least one special character")
        
        return v


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    is_active: Optional[bool] = None
    role: Optional[str] = Field(
        None,
        pattern="^(admin|manager|agronomist|technician|minister|researcher|general_staff|decision_maker)$"
    )


class UserResponse(BaseSchema):
    """Schema for user response data."""
    
    id: UUID
    email: EmailStr
    first_name: str
    last_name: str
    phone: Optional[str]
    role: str
    is_active: bool
    email_verified: bool
    last_login: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"


class UserLogin(BaseModel):
    """Schema for user login."""
    
    email: EmailStr = Field(description="User email address")
    password: str = Field(description="User password")
    remember_me: bool = Field(default=False, description="Remember login session")


class TokenResponse(BaseModel):
    """Schema for authentication token response."""
    
    access_token: str = Field(description="JWT access token")
    refresh_token: str = Field(description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(description="Token expiration time in seconds")
    user: UserResponse = Field(description="User information")


class TokenRefresh(BaseModel):
    """Schema for token refresh request."""
    
    refresh_token: str = Field(description="Refresh token")


class PasswordReset(BaseModel):
    """Schema for password reset request."""
    
    email: EmailStr = Field(description="User email address")


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    
    token: str = Field(description="Password reset token")
    new_password: str = Field(
        min_length=8,
        max_length=128,
        description="New password"
    )
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v):
            raise ValueError("Password must contain at least one special character")
        
        return v


class PasswordChange(BaseModel):
    """Schema for password change."""
    
    current_password: str = Field(description="Current password")
    new_password: str = Field(
        min_length=8,
        max_length=128,
        description="New password"
    )
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v):
            raise ValueError("Password must contain at least one special character")
        
        return v


class UserSessionResponse(BaseSchema):
    """Schema for user session information."""
    
    id: UUID
    expires_at: datetime
    last_used: datetime
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at


class UserProfileUpdate(BaseModel):
    """Schema for user profile updates."""
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    
    @validator("phone")
    def validate_phone(cls, v):
        """Validate phone number format."""
        if v is None:
            return v
        
        # Remove all non-digit characters
        digits_only = "".join(filter(str.isdigit, v))
        
        # Check if it's a valid length (between 10-15 digits)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValueError("Phone number must be between 10-15 digits")
        
        return v


class UserStatsResponse(BaseModel):
    """Schema for user statistics."""
    
    total_estates: int = Field(description="Total number of estates owned")
    total_sensors: int = Field(description="Total number of sensors managed")
    total_alerts: int = Field(description="Total number of active alerts")
    last_activity: Optional[datetime] = Field(description="Last activity timestamp")
    login_count: int = Field(description="Total number of logins")
    account_age_days: int = Field(description="Account age in days")
