"""
User-related database models for authentication and user management.
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>ole<PERSON>, DateTime, Enum, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import INET, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """User model for authentication and authorization."""
    
    __tablename__ = "users"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Authentication fields
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Role-based access control
    role: Mapped[str] = mapped_column(
        Enum("admin", "manager", "agronomist", "technician", "minister", "researcher", "general_staff", "decision_maker", name="user_role"),
        nullable=False,
        default="technician"
    )
    
    # Personal information
    first_name: Mapped[str] = mapped_column(String(100), nullable=False)
    last_name: Mapped[str] = mapped_column(String(100), nullable=False)
    phone: Mapped[Optional[str]] = mapped_column(String(20))
    
    # Account status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    email_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Timestamps
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    sessions: Mapped[list["UserSession"]] = relationship(
        "UserSession",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    password_resets: Mapped[list["PasswordReset"]] = relationship(
        "PasswordReset",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    owned_estates: Mapped[list["Estate"]] = relationship(
        "Estate",
        back_populates="owner",
        foreign_keys="Estate.owner_id"
    )

    # Authorization relationships
    authorization_requests_made: Mapped[list["AuthorizationRequest"]] = relationship(
        "AuthorizationRequest",
        foreign_keys="AuthorizationRequest.requester_id",
        back_populates="requester",
        cascade="all, delete-orphan"
    )
    authorization_requests_received: Mapped[list["AuthorizationRequest"]] = relationship(
        "AuthorizationRequest",
        foreign_keys="AuthorizationRequest.target_persona_id",
        back_populates="target_persona",
        cascade="all, delete-orphan"
    )
    authorizations_received: Mapped[list["Authorization"]] = relationship(
        "Authorization",
        foreign_keys="Authorization.general_staff_user_id",
        back_populates="general_staff_user",
        cascade="all, delete-orphan"
    )
    authorizations_granted: Mapped[list["Authorization"]] = relationship(
        "Authorization",
        foreign_keys="Authorization.authorizing_persona_id",
        back_populates="authorizing_persona_user",
        cascade="all, delete-orphan"
    )

    # Security relationships
    mfa_settings: Mapped[Optional["UserMFA"]] = relationship(
        "UserMFA",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False
    )
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_admin(self) -> bool:
        """Check if user is admin."""
        return self.role == "admin"
    
    @property
    def is_manager(self) -> bool:
        """Check if user is manager or admin."""
        return self.role in ["admin", "manager"]
    
    @property
    def is_agronomist(self) -> bool:
        """Check if user is agronomist, manager, or admin."""
        return self.role in ["admin", "manager", "agronomist"]

    @property
    def is_minister(self) -> bool:
        """Check if user is minister."""
        return self.role == "minister"

    @property
    def is_researcher(self) -> bool:
        """Check if user is researcher."""
        return self.role == "researcher"

    @property
    def is_general_staff(self) -> bool:
        """Check if user is general staff."""
        return self.role == "general_staff"

    @property
    def is_decision_maker(self) -> bool:
        """Check if user is decision maker."""
        return self.role == "decision_maker"
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"


class UserSession(Base):
    """User session model for JWT token management."""
    
    __tablename__ = "user_sessions"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign key to user
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Token information
    token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    refresh_token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Session metadata
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_used: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    ip_address: Mapped[Optional[str]] = mapped_column(INET)
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="sessions")
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def __repr__(self) -> str:
        return f"<UserSession(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"


class PasswordReset(Base):
    """Password reset token model."""
    
    __tablename__ = "password_resets"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign key to user
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Token information
    token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    used_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="password_resets")
    
    @property
    def is_expired(self) -> bool:
        """Check if reset token is expired."""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_used(self) -> bool:
        """Check if reset token has been used."""
        return self.used_at is not None
    
    @property
    def is_valid(self) -> bool:
        """Check if reset token is valid (not expired and not used)."""
        return not self.is_expired and not self.is_used
    
    def __repr__(self) -> str:
        return f"<PasswordReset(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"
