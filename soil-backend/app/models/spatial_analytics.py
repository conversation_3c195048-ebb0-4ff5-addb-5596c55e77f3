"""
Enhanced spatial analytics models for advanced interpolation, hotspot analysis, and trend analysis.

This module implements sophisticated spatial analysis capabilities including
advanced interpolation algorithms, spatial clustering, hotspot detection,
and temporal trend analysis for agricultural data.
"""

import uuid
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from decimal import Decimal

from sqlalchemy import <PERSON>olean, Date, DateTime, ForeignKey, String, Text, Numeric, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from geoalchemy2 import Geometry

from app.core.database import Base


class SpatialInterpolationModel(Base):
    """Advanced spatial interpolation model configurations and results."""
    
    __tablename__ = "spatial_interpolation_models"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Model metadata
    model_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    interpolation_method: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "kriging", "idw", "rbf", "spline", "neural_spatial", "ensemble"
    
    # Geographic scope
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        index=True
    )
    region_boundary: Mapped[Optional[Geometry]] = mapped_column(Geometry("POLYGON", srid=4326))
    
    # Model parameters
    model_parameters: Mapped[dict] = mapped_column(JSON, nullable=False)
    variogram_model: Mapped[Optional[str]] = mapped_column(String(50))  # For Kriging
    nugget: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    sill: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    range_parameter: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    
    # Performance metrics
    cross_validation_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    rmse: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    mae: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    r2_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Grid configuration
    grid_resolution_meters: Mapped[int] = mapped_column(Integer, nullable=False, default=100)
    grid_points_count: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Model status
    is_trained: Mapped[bool] = mapped_column(Boolean, default=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    training_data_count: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    trained_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    last_used_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<SpatialInterpolationModel(name={self.model_name}, method={self.interpolation_method})>"


class HotspotAnalysis(Base):
    """Hotspot analysis results for identifying spatial patterns and anomalies."""
    
    __tablename__ = "hotspot_analyses"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Analysis metadata
    analysis_name: Mapped[str] = mapped_column(String(200), nullable=False)
    analysis_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "getis_ord", "local_moran", "kernel_density", "dbscan", "custom"
    
    # Geographic scope
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        index=True
    )
    analysis_boundary: Mapped[Optional[Geometry]] = mapped_column(Geometry("POLYGON", srid=4326))
    
    # Analysis parameters
    parameter_analyzed: Mapped[str] = mapped_column(String(50), nullable=False)  # "ph", "nitrogen", etc.
    analysis_parameters: Mapped[dict] = mapped_column(JSON, nullable=False)
    distance_threshold_meters: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    significance_level: Mapped[Decimal] = mapped_column(Numeric(3, 2), default=Decimal("0.05"))
    
    # Results
    hotspots_detected: Mapped[int] = mapped_column(Integer, default=0)
    coldspots_detected: Mapped[int] = mapped_column(Integer, default=0)
    hotspot_locations: Mapped[list] = mapped_column(JSON, nullable=False)
    coldspot_locations: Mapped[list] = mapped_column(JSON, nullable=False)
    
    # Statistical measures
    global_moran_i: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6))
    p_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 8))
    z_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    
    # Analysis results
    analysis_results: Mapped[dict] = mapped_column(JSON, nullable=False)
    recommendations: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Quality metrics
    confidence_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    data_quality_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Temporal information
    analysis_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    data_period_start: Mapped[Optional[date]] = mapped_column(Date)
    data_period_end: Mapped[Optional[date]] = mapped_column(Date)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<HotspotAnalysis(name={self.analysis_name}, hotspots={self.hotspots_detected})>"


class SpatialTrendAnalysis(Base):
    """Temporal trend analysis for spatial data patterns."""
    
    __tablename__ = "spatial_trend_analyses"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Analysis metadata
    analysis_name: Mapped[str] = mapped_column(String(200), nullable=False)
    trend_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "temporal", "seasonal", "directional", "cyclic", "anomaly_detection"
    
    # Geographic scope
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        index=True
    )
    analysis_boundary: Mapped[Optional[Geometry]] = mapped_column(Geometry("POLYGON", srid=4326))
    
    # Analysis parameters
    parameter_analyzed: Mapped[str] = mapped_column(String(50), nullable=False)
    time_period_start: Mapped[date] = mapped_column(Date, nullable=False)
    time_period_end: Mapped[date] = mapped_column(Date, nullable=False)
    temporal_resolution: Mapped[str] = mapped_column(
        String(20),
        nullable=False
    )  # "daily", "weekly", "monthly", "seasonal", "annual"
    
    # Trend analysis results
    trend_direction: Mapped[Optional[str]] = mapped_column(
        String(20)
    )  # "increasing", "decreasing", "stable", "cyclic", "irregular"
    trend_magnitude: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    trend_significance: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Statistical measures
    mann_kendall_tau: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6))
    sen_slope: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    p_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 8))
    
    # Seasonal analysis
    seasonal_components: Mapped[Optional[dict]] = mapped_column(JSON)
    seasonal_strength: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Spatial patterns
    spatial_autocorrelation: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6))
    spatial_trend_direction: Mapped[Optional[str]] = mapped_column(String(50))
    
    # Analysis results
    analysis_results: Mapped[dict] = mapped_column(JSON, nullable=False)
    trend_forecast: Mapped[Optional[dict]] = mapped_column(JSON)
    anomalies_detected: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Quality metrics
    model_accuracy: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    confidence_intervals: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<SpatialTrendAnalysis(name={self.analysis_name}, trend={self.trend_direction})>"


class SpatialCluster(Base):
    """Spatial clustering results for identifying similar regions."""
    
    __tablename__ = "spatial_clusters"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Cluster metadata
    cluster_name: Mapped[str] = mapped_column(String(200), nullable=False)
    clustering_algorithm: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "kmeans", "dbscan", "hierarchical", "gaussian_mixture", "spectral"
    
    # Geographic scope
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        index=True
    )
    cluster_boundary: Mapped[Optional[Geometry]] = mapped_column(Geometry("POLYGON", srid=4326))
    
    # Clustering parameters
    parameters_used: Mapped[list] = mapped_column(JSON, nullable=False)  # List of soil parameters
    algorithm_parameters: Mapped[dict] = mapped_column(JSON, nullable=False)
    number_of_clusters: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Cluster results
    cluster_assignments: Mapped[dict] = mapped_column(JSON, nullable=False)  # Point ID -> Cluster ID
    cluster_centers: Mapped[dict] = mapped_column(JSON, nullable=False)  # Cluster centroids
    cluster_characteristics: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Quality metrics
    silhouette_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    calinski_harabasz_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4))
    davies_bouldin_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    inertia: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 6))
    
    # Cluster interpretation
    cluster_labels: Mapped[Optional[dict]] = mapped_column(JSON)  # Human-readable cluster names
    management_recommendations: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Analysis metadata
    data_points_count: Mapped[int] = mapped_column(Integer, nullable=False)
    analysis_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<SpatialCluster(name={self.cluster_name}, clusters={self.number_of_clusters})>"


class SpatialOptimization(Base):
    """Spatial optimization results for resource allocation and management."""
    
    __tablename__ = "spatial_optimizations"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Optimization metadata
    optimization_name: Mapped[str] = mapped_column(String(200), nullable=False)
    optimization_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "fertilizer_allocation", "sampling_design", "irrigation_planning", "harvest_routing"
    
    # Geographic scope
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        index=True
    )
    optimization_boundary: Mapped[Optional[Geometry]] = mapped_column(Geometry("POLYGON", srid=4326))
    
    # Optimization parameters
    objective_function: Mapped[str] = mapped_column(String(100), nullable=False)
    constraints: Mapped[dict] = mapped_column(JSON, nullable=False)
    optimization_parameters: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Results
    optimal_solution: Mapped[dict] = mapped_column(JSON, nullable=False)
    objective_value: Mapped[Decimal] = mapped_column(Numeric(15, 6), nullable=False)
    convergence_status: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Solution quality
    optimality_gap: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 6))
    solution_confidence: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    sensitivity_analysis: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Implementation details
    implementation_plan: Mapped[Optional[dict]] = mapped_column(JSON)
    expected_benefits: Mapped[Optional[dict]] = mapped_column(JSON)
    implementation_cost: Mapped[Optional[Decimal]] = mapped_column(Numeric(12, 2))
    
    # Analysis metadata
    solver_used: Mapped[str] = mapped_column(String(50), nullable=False)
    computation_time_seconds: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    iterations: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<SpatialOptimization(name={self.optimization_name}, objective={self.objective_value})>"
