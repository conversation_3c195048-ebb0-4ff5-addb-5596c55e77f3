"""
Enhanced model performance tracking and validation models.

This module implements comprehensive model performance monitoring,
accuracy validation, and prediction feedback loops as required by the PRD.
"""

import uuid
from datetime import datetime, date
from typing import Optional, Dict, Any
from decimal import Decimal

from sqlalchemy import <PERSON><PERSON><PERSON>, Date, DateTime, ForeignKey, String, Text, Numeric, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class ModelPerformanceMetric(Base):
    """Model performance metrics tracking."""
    
    __tablename__ = "model_performance_metrics"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Model information
    model_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    model_version: Mapped[str] = mapped_column(String(50), nullable=False)
    model_type: Mapped[str] = mapped_column(String(50), nullable=False)  # "ensemble", "xgboost", "neural_network", "kriging"
    
    # Performance metrics
    accuracy_r2: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    rmse: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    mae: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 6))
    mape: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))  # Mean Absolute Percentage Error
    
    # Confidence metrics
    avg_confidence_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    confidence_distribution: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Performance targets compliance
    meets_accuracy_target: Mapped[bool] = mapped_column(Boolean, default=False)
    accuracy_target_range: Mapped[Optional[str]] = mapped_column(String(20))  # "85-95%"
    
    # Latency metrics
    avg_latency_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    p95_latency_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    p99_latency_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    meets_latency_target: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Usage statistics
    prediction_count: Mapped[int] = mapped_column(Integer, default=0)
    error_count: Mapped[int] = mapped_column(Integer, default=0)
    error_rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Data quality metrics
    data_drift_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    concept_drift_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    drift_detected: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Evaluation period
    evaluation_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    evaluation_period_start: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    evaluation_period_end: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    
    # Additional metadata
    evaluation_dataset_size: Mapped[Optional[int]] = mapped_column(Integer)
    feature_importance: Mapped[Optional[dict]] = mapped_column(JSON)
    model_metadata: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    def __repr__(self) -> str:
        return f"<ModelPerformanceMetric(model={self.model_name}, version={self.model_version}, r2={self.accuracy_r2})>"


class PredictionFeedback(Base):
    """Prediction feedback from users for model improvement."""
    
    __tablename__ = "prediction_feedback"
    __table_args__ = {'extend_existing': True}
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="SET NULL"),
        index=True
    )
    
    # Prediction information
    prediction_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    model_name: Mapped[str] = mapped_column(String(100), nullable=False)
    model_version: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Original prediction
    predicted_values: Mapped[dict] = mapped_column(JSON, nullable=False)
    confidence_scores: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Feedback details
    feedback_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "accuracy_rating", "correction", "validation", "complaint"
    
    feedback_rating: Mapped[Optional[int]] = mapped_column(Integer)  # 1-5 scale
    corrected_values: Mapped[Optional[dict]] = mapped_column(JSON)
    actual_measured_values: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Feedback content
    feedback_text: Mapped[Optional[str]] = mapped_column(Text)
    feedback_category: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Context information
    measurement_method: Mapped[Optional[str]] = mapped_column(String(100))
    measurement_date: Mapped[Optional[date]] = mapped_column(Date)
    environmental_conditions: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Processing status
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False)
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    processed_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    
    # Impact on model
    used_for_retraining: Mapped[bool] = mapped_column(Boolean, default=False)
    feedback_weight: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))  # Weight in retraining
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id])
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    processed_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[processed_by_id])
    
    def __repr__(self) -> str:
        return f"<PredictionFeedback(id={self.id}, type={self.feedback_type}, rating={self.feedback_rating})>"


class ModelAccuracyValidation(Base):
    """Model accuracy validation results."""
    
    __tablename__ = "model_accuracy_validations"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Model information
    model_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    model_version: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Validation details
    validation_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False
    )  # "cross_validation", "holdout", "temporal", "spatial"
    
    validation_dataset_size: Mapped[int] = mapped_column(Integer, nullable=False)
    validation_method: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Accuracy results
    overall_accuracy: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    parameter_accuracies: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Statistical metrics
    r2_score: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    rmse: Mapped[Decimal] = mapped_column(Numeric(10, 6), nullable=False)
    mae: Mapped[Decimal] = mapped_column(Numeric(10, 6), nullable=False)
    
    # Confidence analysis
    confidence_calibration: Mapped[dict] = mapped_column(JSON)
    confidence_reliability: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))
    
    # Target compliance
    meets_85_95_target: Mapped[bool] = mapped_column(Boolean, nullable=False)
    accuracy_percentile: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Validation conditions
    validation_conditions: Mapped[Optional[dict]] = mapped_column(JSON)
    environmental_factors: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Results breakdown
    results_by_parameter: Mapped[dict] = mapped_column(JSON, nullable=False)
    results_by_region: Mapped[Optional[dict]] = mapped_column(JSON)
    results_by_season: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Validation metadata
    validation_notes: Mapped[Optional[str]] = mapped_column(Text)
    validation_status: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="completed"
    )  # "completed", "failed", "in_progress"
    
    # Timestamps
    validation_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    
    def __repr__(self) -> str:
        return f"<ModelAccuracyValidation(model={self.model_name}, accuracy={self.overall_accuracy}, meets_target={self.meets_85_95_target})>"


class SHAPExplanationRecord(Base):
    """SHAP explanation records for prediction interpretability."""
    
    __tablename__ = "shap_explanation_records"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Prediction reference
    prediction_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    model_name: Mapped[str] = mapped_column(String(100), nullable=False)
    model_version: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # SHAP explanation data
    feature_importance: Mapped[dict] = mapped_column(JSON, nullable=False)
    shap_values: Mapped[dict] = mapped_column(JSON, nullable=False)
    base_values: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Explanation summary
    top_positive_features: Mapped[list] = mapped_column(JSON, nullable=False)
    top_negative_features: Mapped[list] = mapped_column(JSON, nullable=False)
    explanation_confidence: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    
    # User context
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True
    )
    
    # Input data context
    input_features: Mapped[dict] = mapped_column(JSON, nullable=False)
    prediction_output: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Explanation metadata
    explanation_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="local"
    )  # "local", "global", "partial_dependence"
    
    explanation_method: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="tree_explainer"
    )  # "tree_explainer", "kernel_explainer", "deep_explainer"
    
    # Processing metrics
    explanation_time_ms: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        index=True
    )
    
    # Relationships
    user: Mapped[Optional["User"]] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<SHAPExplanationRecord(prediction_id={self.prediction_id}, model={self.model_name})>"
