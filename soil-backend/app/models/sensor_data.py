"""
Sensor data models for the soil-backend application.

This module defines the database models for sensor data collection,
including soil sensors, weather stations, and IoT device data.
"""

from sqlalchemy import Column, String, Float, DateTime, Boolean, Text, ForeignKey, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.core.database import Base


class SensorData(Base):
    """Model for sensor data readings."""
    
    __tablename__ = "sensor_data"
    __table_args__ = {'extend_existing': True}
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Sensor identification
    sensor_id = Column(String(100), nullable=False, index=True)
    sensor_type = Column(String(50), nullable=False)  # soil, weather, moisture, etc.
    device_id = Column(String(100), nullable=True)
    
    # Location data
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    elevation = Column(Float, nullable=True)
    location_name = Column(String(200), nullable=True)
    
    # Measurement data
    measurement_type = Column(String(50), nullable=False)  # pH, moisture, temperature, etc.
    value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=False)
    
    # Quality and metadata
    quality_score = Column(Float, default=1.0)
    calibration_status = Column(String(20), default='calibrated')
    sensor_metadata = Column(JSONB, default={})
    
    # Timestamps
    measured_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Status
    is_active = Column(Boolean, default=True)
    is_validated = Column(Boolean, default=False)
    
    def __repr__(self) -> str:
        return f"<SensorData(sensor={self.sensor_id}, type={self.measurement_type}, value={self.value})>"


class SensorDevice(Base):
    """Model for sensor device information."""
    
    __tablename__ = "sensor_devices"
    __table_args__ = {'extend_existing': True}
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Device identification
    device_id = Column(String(100), unique=True, nullable=False, index=True)
    device_name = Column(String(200), nullable=False)
    device_type = Column(String(50), nullable=False)
    manufacturer = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    firmware_version = Column(String(50), nullable=True)
    
    # Location and installation
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    elevation = Column(Float, nullable=True)
    installation_depth = Column(Float, nullable=True)  # For soil sensors
    location_description = Column(Text, nullable=True)
    
    # Status and configuration
    is_active = Column(Boolean, default=True)
    is_online = Column(Boolean, default=False)
    last_communication = Column(DateTime(timezone=True), nullable=True)
    battery_level = Column(Float, nullable=True)
    signal_strength = Column(Float, nullable=True)
    
    # Configuration
    sampling_interval = Column(Integer, default=3600)  # seconds
    configuration = Column(JSONB, default={})
    
    # Timestamps
    installed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self) -> str:
        return f"<SensorDevice(id={self.device_id}, name={self.device_name}, type={self.device_type})>"


class SensorCalibration(Base):
    """Model for sensor calibration data."""
    
    __tablename__ = "sensor_calibrations"
    __table_args__ = {'extend_existing': True}
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Sensor reference
    sensor_id = Column(String(100), nullable=False, index=True)
    device_id = Column(String(100), nullable=True)
    
    # Calibration data
    calibration_type = Column(String(50), nullable=False)  # linear, polynomial, lookup
    calibration_parameters = Column(JSONB, nullable=False)
    reference_values = Column(JSONB, nullable=True)
    measured_values = Column(JSONB, nullable=True)
    
    # Quality metrics
    accuracy = Column(Float, nullable=True)
    precision = Column(Float, nullable=True)
    r_squared = Column(Float, nullable=True)
    
    # Validity
    valid_from = Column(DateTime(timezone=True), nullable=False)
    valid_until = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Metadata
    calibrated_by = Column(String(100), nullable=True)
    calibration_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self) -> str:
        return f"<SensorCalibration(sensor={self.sensor_id}, type={self.calibration_type}, active={self.is_active})>"
