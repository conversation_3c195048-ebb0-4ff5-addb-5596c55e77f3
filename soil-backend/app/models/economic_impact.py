"""
Economic impact analysis models for ROI calculations and investment analysis.

This module implements comprehensive economic impact assessment capabilities
for agricultural investments, policy analysis, and competitiveness metrics.
"""

import uuid
from datetime import datetime, date
from typing import Optional, Dict, Any
from decimal import Decimal

from sqlalchemy import <PERSON>olean, Date, DateTime, ForeignKey, String, Text, Numeric, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class EconomicImpactAnalysis(Base):
    """Economic impact analysis for agricultural investments."""
    
    __tablename__ = "economic_impact_analyses"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Analysis metadata
    analysis_name: Mapped[str] = mapped_column(String(200), nullable=False)
    analysis_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "investment", "policy", "technology", "infrastructure"
    
    analysis_scope: Mapped[str] = mapped_column(
        String(50),
        nullable=False
    )  # "estate", "regional", "national", "international"
    
    # Investment details
    investment_amount_usd: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    investment_currency: Mapped[str] = mapped_column(String(3), default="USD")
    investment_period_years: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Geographic scope
    country: Mapped[Optional[str]] = mapped_column(String(100))
    region: Mapped[Optional[str]] = mapped_column(String(100))
    estate_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="SET NULL"),
        index=True
    )
    
    # Economic metrics
    baseline_productivity: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    projected_productivity_increase: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    baseline_costs_per_hectare: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    projected_cost_reduction: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # ROI calculations
    roi_percentage: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    npv_usd: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2))
    irr_percentage: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    payback_period_years: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Impact metrics
    job_creation_estimate: Mapped[Optional[int]] = mapped_column(Integer)
    gdp_impact_usd: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2))
    export_revenue_impact_usd: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 2))
    sustainability_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Risk assessment
    risk_factors: Mapped[Optional[list]] = mapped_column(JSON)
    risk_mitigation_strategies: Mapped[Optional[list]] = mapped_column(JSON)
    success_probability: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Analysis results
    analysis_results: Mapped[dict] = mapped_column(JSON, nullable=False)
    key_findings: Mapped[Optional[list]] = mapped_column(JSON)
    recommendations: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Validation and approval
    is_validated: Mapped[bool] = mapped_column(Boolean, default=False)
    validated_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    validated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Analysis metadata
    created_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    analysis_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    estate: Mapped[Optional["Estate"]] = relationship("Estate")
    created_by: Mapped["User"] = relationship("User", foreign_keys=[created_by_id])
    validated_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[validated_by_id])
    
    def __repr__(self) -> str:
        return f"<EconomicImpactAnalysis(id={self.id}, name={self.analysis_name}, roi={self.roi_percentage}%)>"


class CompetitivenessMetric(Base):
    """National and regional competitiveness metrics."""
    
    __tablename__ = "competitiveness_metrics"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Metric details
    metric_name: Mapped[str] = mapped_column(String(100), nullable=False)
    metric_category: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "productivity", "sustainability", "technology", "cost_efficiency", "quality"
    
    # Geographic scope
    country: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    region: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Metric values
    current_value: Mapped[Decimal] = mapped_column(Numeric(15, 4), nullable=False)
    previous_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    target_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    benchmark_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    
    # Rankings
    global_rank: Mapped[Optional[int]] = mapped_column(Integer)
    regional_rank: Mapped[Optional[int]] = mapped_column(Integer)
    total_countries: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Trend analysis
    year_over_year_change: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    trend_direction: Mapped[Optional[str]] = mapped_column(
        String(20)
    )  # "improving", "stable", "declining"
    
    # Comparison data
    peer_countries: Mapped[Optional[list]] = mapped_column(JSON)
    benchmark_countries: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Metric metadata
    unit_of_measurement: Mapped[str] = mapped_column(String(50), nullable=False)
    data_source: Mapped[str] = mapped_column(String(200), nullable=False)
    measurement_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Quality indicators
    data_quality_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2))
    confidence_level: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    def __repr__(self) -> str:
        return f"<CompetitivenessMetric(metric={self.metric_name}, country={self.country}, value={self.current_value})>"


class InvestmentScenario(Base):
    """Investment scenario modeling for policy analysis."""
    
    __tablename__ = "investment_scenarios"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Scenario details
    scenario_name: Mapped[str] = mapped_column(String(200), nullable=False)
    scenario_description: Mapped[Optional[str]] = mapped_column(Text)
    scenario_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False
    )  # "baseline", "optimistic", "pessimistic", "policy_intervention"
    
    # Investment parameters
    total_investment_usd: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    investment_timeline_years: Mapped[int] = mapped_column(Integer, nullable=False)
    funding_sources: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Implementation phases
    implementation_phases: Mapped[list] = mapped_column(JSON, nullable=False)
    milestone_targets: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Economic assumptions
    discount_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    inflation_rate: Mapped[Decimal] = mapped_column(Numeric(5, 4), nullable=False)
    currency_exchange_assumptions: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Impact projections
    productivity_impact: Mapped[dict] = mapped_column(JSON, nullable=False)
    employment_impact: Mapped[dict] = mapped_column(JSON, nullable=False)
    environmental_impact: Mapped[dict] = mapped_column(JSON, nullable=False)
    social_impact: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Financial projections
    revenue_projections: Mapped[dict] = mapped_column(JSON, nullable=False)
    cost_projections: Mapped[dict] = mapped_column(JSON, nullable=False)
    cash_flow_projections: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Risk analysis
    risk_assessment: Mapped[dict] = mapped_column(JSON, nullable=False)
    sensitivity_analysis: Mapped[dict] = mapped_column(JSON, nullable=False)
    monte_carlo_results: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Scenario results
    scenario_results: Mapped[dict] = mapped_column(JSON, nullable=False)
    success_metrics: Mapped[dict] = mapped_column(JSON, nullable=False)
    
    # Analysis metadata
    created_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    created_by: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<InvestmentScenario(id={self.id}, name={self.scenario_name}, investment=${self.total_investment_usd})>"


class EconomicIndicator(Base):
    """Economic indicators for agricultural sector monitoring."""
    
    __tablename__ = "economic_indicators"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Indicator details
    indicator_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    indicator_code: Mapped[str] = mapped_column(String(20), nullable=False, unique=True)
    indicator_category: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )  # "production", "trade", "prices", "employment", "investment"
    
    # Geographic and temporal scope
    country: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    region: Mapped[Optional[str]] = mapped_column(String(100))
    reporting_period: Mapped[str] = mapped_column(
        String(20),
        nullable=False
    )  # "monthly", "quarterly", "annual"
    
    # Indicator values
    current_value: Mapped[Decimal] = mapped_column(Numeric(15, 4), nullable=False)
    previous_period_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    year_ago_value: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    
    # Change calculations
    period_change_absolute: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    period_change_percentage: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    year_over_year_change: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    
    # Forecasting
    forecast_next_period: Mapped[Optional[Decimal]] = mapped_column(Numeric(15, 4))
    forecast_confidence: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2))
    
    # Data metadata
    unit_of_measurement: Mapped[str] = mapped_column(String(50), nullable=False)
    data_source: Mapped[str] = mapped_column(String(200), nullable=False)
    collection_method: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Quality and reliability
    data_quality_rating: Mapped[Optional[str]] = mapped_column(
        String(20)
    )  # "high", "medium", "low"
    reliability_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2))
    
    # Temporal information
    measurement_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    publication_date: Mapped[Optional[date]] = mapped_column(Date)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    def __repr__(self) -> str:
        return f"<EconomicIndicator(code={self.indicator_code}, country={self.country}, value={self.current_value})>"
