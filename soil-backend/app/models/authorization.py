"""
Authorization models for General Staff dynamic permissions system.

This module implements the complex authorization system required by the PRD
for General Staff to receive time-limited permissions from primary personas.
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy import Boolean, DateTime, Enum, ForeignKey, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class AuthorizationRequest(Base):
    """Model for authorization requests from General Staff to primary personas."""
    
    __tablename__ = "authorization_requests"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    requester_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    target_persona_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Authorization details
    data_entry_type: Mapped[str] = mapped_column(
        Enum(
            "financial_data", "operational_incidents", "field_observations", 
            "research_data", "equipment_maintenance", "safety_compliance",
            "pest_disease_reports", "soil_assessments", "treatment_effectiveness",
            name="data_entry_type"
        ),
        nullable=False
    )
    
    permission_level: Mapped[str] = mapped_column(
        Enum("read_only", "create", "edit", "delete", name="permission_level"),
        nullable=False,
        default="create"
    )
    
    # Request details
    justification: Mapped[str] = mapped_column(Text, nullable=False)
    requested_duration_days: Mapped[int] = mapped_column(nullable=False, default=30)
    
    # Status tracking
    status: Mapped[str] = mapped_column(
        Enum("pending", "approved", "rejected", "expired", name="authorization_status"),
        nullable=False,
        default="pending"
    )
    
    # Response details
    response_message: Mapped[Optional[str]] = mapped_column(Text)
    responded_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    responded_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    requester: Mapped["User"] = relationship(
        "User",
        foreign_keys=[requester_id],
        back_populates="authorization_requests_made"
    )
    target_persona: Mapped["User"] = relationship(
        "User",
        foreign_keys=[target_persona_id],
        back_populates="authorization_requests_received"
    )
    responded_by: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[responded_by_id]
    )
    estate: Mapped["Estate"] = relationship("Estate")
    
    def __repr__(self) -> str:
        return f"<AuthorizationRequest(id={self.id}, type={self.data_entry_type}, status={self.status})>"


class Authorization(Base):
    """Model for active authorizations granted to General Staff."""
    
    __tablename__ = "authorizations"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    general_staff_user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    authorizing_persona_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Authorization details
    authorizing_persona: Mapped[str] = mapped_column(
        Enum("decision_maker", "manager", "agronomist", "researcher", name="authorizing_persona_type"),
        nullable=False
    )
    
    data_entry_type: Mapped[str] = mapped_column(
        Enum(
            "financial_data", "operational_incidents", "field_observations", 
            "research_data", "equipment_maintenance", "safety_compliance",
            "pest_disease_reports", "soil_assessments", "treatment_effectiveness",
            name="data_entry_type"
        ),
        nullable=False
    )
    
    permission_level: Mapped[str] = mapped_column(
        Enum("read_only", "create", "edit", "delete", name="permission_level"),
        nullable=False
    )
    
    # Time limits
    granted_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False
    )
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    revoked_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    revoked_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    revocation_reason: Mapped[Optional[str]] = mapped_column(Text)
    
    # Usage tracking
    last_used_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    usage_count: Mapped[int] = mapped_column(default=0)
    
    # Additional metadata
    conditions: Mapped[Optional[dict]] = mapped_column(JSON)  # Additional conditions or restrictions
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    general_staff_user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[general_staff_user_id],
        back_populates="authorizations_received"
    )
    authorizing_persona_user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[authorizing_persona_id],
        back_populates="authorizations_granted"
    )
    revoked_by: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[revoked_by_id]
    )
    estate: Mapped["Estate"] = relationship("Estate")
    
    @property
    def is_expired(self) -> bool:
        """Check if authorization is expired."""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if authorization is valid (active and not expired)."""
        return self.is_active and not self.is_expired and self.revoked_at is None
    
    def __repr__(self) -> str:
        return f"<Authorization(id={self.id}, type={self.data_entry_type}, expires={self.expires_at})>"


class AuthorizationAuditLog(Base):
    """Audit log for all authorization-related activities."""
    
    __tablename__ = "authorization_audit_logs"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    authorization_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("authorizations.id", ondelete="SET NULL"),
        index=True
    )
    
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Audit details
    action: Mapped[str] = mapped_column(
        Enum(
            "request_created", "request_approved", "request_rejected",
            "authorization_granted", "authorization_used", "authorization_revoked",
            "authorization_expired", "permission_modified",
            name="authorization_action"
        ),
        nullable=False
    )
    
    details: Mapped[dict] = mapped_column(JSON, nullable=False)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))  # IPv6 support
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamp
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        index=True
    )
    
    # Relationships
    authorization: Mapped[Optional["Authorization"]] = relationship("Authorization")
    user: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<AuthorizationAuditLog(id={self.id}, action={self.action}, created_at={self.created_at})>"
