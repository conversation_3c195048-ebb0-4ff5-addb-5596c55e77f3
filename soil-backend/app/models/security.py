"""
Enhanced security models for enterprise-grade authentication and audit logging.

This module implements multi-factor authentication, comprehensive audit logging,
and advanced security features required for enterprise deployment.
"""

import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from enum import Enum

from sqlalchemy import <PERSON>olean, DateTime, Enum as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Text, Integer, J<PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID, INET
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class MFAMethod(str, Enum):
    """Multi-factor authentication methods."""
    TOTP = "totp"  # Time-based One-Time Password (Google Authenticator, etc.)
    SMS = "sms"    # SMS-based verification
    EMAIL = "email"  # Email-based verification
    BACKUP_CODES = "backup_codes"  # Backup recovery codes


class AuditEventType(str, Enum):
    """Types of audit events."""
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    MFA_ENABLED = "mfa_enabled"
    MFA_DISABLED = "mfa_disabled"
    MFA_VERIFICATION_SUCCESS = "mfa_verification_success"
    MFA_VERIFICATION_FAILURE = "mfa_verification_failure"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_REVOKED = "permission_revoked"
    SECURITY_VIOLATION = "security_violation"
    SYSTEM_ACCESS = "system_access"
    API_ACCESS = "api_access"
    EXPORT_DATA = "export_data"
    IMPORT_DATA = "import_data"


class UserMFA(Base):
    """Multi-factor authentication settings for users."""
    
    __tablename__ = "user_mfa"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign key to user
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True
    )
    
    # MFA configuration
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=False)
    primary_method: Mapped[Optional[str]] = mapped_column(
        SQLEnum(MFAMethod, name="mfa_method"),
        nullable=True
    )
    
    # TOTP settings
    totp_secret: Mapped[Optional[str]] = mapped_column(String(32))  # Base32 encoded secret
    totp_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # SMS settings
    sms_phone_number: Mapped[Optional[str]] = mapped_column(String(20))
    sms_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Email settings (uses user's primary email)
    email_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Backup codes
    backup_codes: Mapped[Optional[list]] = mapped_column(JSON)  # List of hashed backup codes
    backup_codes_used: Mapped[Optional[list]] = mapped_column(JSON)  # List of used backup codes
    
    # Recovery settings
    recovery_email: Mapped[Optional[str]] = mapped_column(String(255))
    recovery_phone: Mapped[Optional[str]] = mapped_column(String(20))
    
    # Timestamps
    enabled_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    last_verification: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="mfa_settings")
    
    def __repr__(self) -> str:
        return f"<UserMFA(user_id={self.user_id}, enabled={self.is_enabled}, method={self.primary_method})>"


class MFAVerificationAttempt(Base):
    """MFA verification attempts for security monitoring."""
    
    __tablename__ = "mfa_verification_attempts"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign key to user
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Verification details
    method_used: Mapped[str] = mapped_column(
        SQLEnum(MFAMethod, name="mfa_method"),
        nullable=False
    )
    success: Mapped[bool] = mapped_column(Boolean, nullable=False)
    failure_reason: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Request metadata
    ip_address: Mapped[Optional[str]] = mapped_column(INET)
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    session_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Timestamp
    attempted_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        index=True
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<MFAVerificationAttempt(user_id={self.user_id}, method={self.method_used}, success={self.success})>"


class SecurityAuditLog(Base):
    """Comprehensive security audit logging."""
    
    __tablename__ = "security_audit_logs"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # User information (optional for system events)
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True
    )
    
    # Event details
    event_type: Mapped[str] = mapped_column(
        SQLEnum(AuditEventType, name="audit_event_type"),
        nullable=False,
        index=True
    )
    event_description: Mapped[str] = mapped_column(Text, nullable=False)
    event_data: Mapped[Optional[dict]] = mapped_column(JSON)  # Additional event metadata
    
    # Request context
    ip_address: Mapped[Optional[str]] = mapped_column(INET, index=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    session_id: Mapped[Optional[str]] = mapped_column(String(255))
    request_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Resource information
    resource_type: Mapped[Optional[str]] = mapped_column(String(100))  # e.g., "estate", "sensor", "user"
    resource_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Security classification
    severity_level: Mapped[str] = mapped_column(
        SQLEnum("low", "medium", "high", "critical", name="severity_level"),
        nullable=False,
        default="medium",
        index=True
    )
    
    # Success/failure status
    success: Mapped[bool] = mapped_column(Boolean, nullable=False, index=True)
    failure_reason: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamp
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        index=True
    )
    
    # Relationships
    user: Mapped[Optional["User"]] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<SecurityAuditLog(event_type={self.event_type}, user_id={self.user_id}, success={self.success})>"


class LoginAttempt(Base):
    """Login attempt tracking for security monitoring."""
    
    __tablename__ = "login_attempts"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Login details
    email: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    success: Mapped[bool] = mapped_column(Boolean, nullable=False, index=True)
    failure_reason: Mapped[Optional[str]] = mapped_column(String(255))
    
    # User information (if login was successful)
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        index=True
    )
    
    # Request metadata
    ip_address: Mapped[Optional[str]] = mapped_column(INET, index=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    
    # Security flags
    is_suspicious: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    blocked_by_rate_limit: Mapped[bool] = mapped_column(Boolean, default=False)
    requires_mfa: Mapped[bool] = mapped_column(Boolean, default=False)
    mfa_completed: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Timestamp
    attempted_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        index=True
    )
    
    # Relationships
    user: Mapped[Optional["User"]] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<LoginAttempt(email={self.email}, success={self.success}, attempted_at={self.attempted_at})>"


class DataEncryption(Base):
    """Data encryption key management."""
    
    __tablename__ = "data_encryption"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Encryption details
    key_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    key_version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    algorithm: Mapped[str] = mapped_column(String(50), nullable=False, default="AES-256-GCM")
    
    # Key metadata
    purpose: Mapped[str] = mapped_column(String(100), nullable=False)  # e.g., "user_data", "sensor_data"
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    
    # Key rotation
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    rotated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Usage tracking
    last_used: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    usage_count: Mapped[int] = mapped_column(Integer, default=0)
    
    def __repr__(self) -> str:
        return f"<DataEncryption(key_id={self.key_id}, purpose={self.purpose}, active={self.is_active})>"


class GDPRDataRequest(Base):
    """GDPR/PDPA data request tracking."""
    
    __tablename__ = "gdpr_data_requests"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Request details
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    request_type: Mapped[str] = mapped_column(
        SQLEnum("data_export", "data_deletion", "data_correction", "consent_withdrawal", name="gdpr_request_type"),
        nullable=False
    )
    
    status: Mapped[str] = mapped_column(
        SQLEnum("pending", "in_progress", "completed", "rejected", name="gdpr_request_status"),
        nullable=False,
        default="pending"
    )
    
    # Request metadata
    description: Mapped[Optional[str]] = mapped_column(Text)
    requested_data_types: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Processing details
    processed_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    processing_notes: Mapped[Optional[str]] = mapped_column(Text)
    completion_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # File references (for data exports)
    export_file_path: Mapped[Optional[str]] = mapped_column(String(500))
    export_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id])
    processed_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[processed_by_id])
    
    def __repr__(self) -> str:
        return f"<GDPRDataRequest(user_id={self.user_id}, type={self.request_type}, status={self.status})>"
