"""
MPOB (Malaysian Palm Oil Board) compliance models for regulatory reporting.

This module implements the comprehensive MPOB compliance system required by the PRD
including FFB harvest data, yield validation, fertilizer tracking, and automated reporting.
"""

import uuid
from datetime import datetime, date
from typing import Optional
from decimal import Decimal

from sqlalchemy import Boolean, Date, DateTime, Enum, ForeignKey, String, Text, Numeric, Integer
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.core.database import Base


class FFBHarvestData(Base):
    """Fresh Fruit Bunch harvest data for MPOB compliance."""
    
    __tablename__ = "ffb_harvest_data"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    block_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estate_blocks.id", ondelete="SET NULL"),
        index=True
    )
    
    recorded_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Harvest details
    harvest_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    ffb_quantity_kg: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False
    )
    ffb_quality_grade: Mapped[str] = mapped_column(
        Enum("grade_1", "grade_2", "grade_3", "reject", name="ffb_quality_grade"),
        nullable=False,
        default="grade_1"
    )
    
    # Additional harvest metrics
    bunch_count: Mapped[Optional[int]] = mapped_column(Integer)
    average_bunch_weight_kg: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    oil_extraction_rate_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Validation status
    is_validated: Mapped[bool] = mapped_column(Boolean, default=False)
    validated_by_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL")
    )
    validated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    validation_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # MPOB reporting
    mpob_reported: Mapped[bool] = mapped_column(Boolean, default=False)
    mpob_report_id: Mapped[Optional[str]] = mapped_column(String(100))
    mpob_reported_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    estate: Mapped["Estate"] = relationship("Estate")
    block: Mapped[Optional["EstateBlock"]] = relationship("EstateBlock")
    recorded_by: Mapped["User"] = relationship("User", foreign_keys=[recorded_by_id])
    validated_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[validated_by_id])
    
    def __repr__(self) -> str:
        return f"<FFBHarvestData(id={self.id}, date={self.harvest_date}, quantity={self.ffb_quantity_kg}kg)>"


class YieldValidationData(Base):
    """Yield validation data for MPOB compliance."""
    
    __tablename__ = "yield_validation_data"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    block_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estate_blocks.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    recorded_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Yield data
    measurement_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    yield_per_hectare_kg: Mapped[Decimal] = mapped_column(
        Numeric(10, 2),
        nullable=False
    )
    total_area_hectares: Mapped[Decimal] = mapped_column(
        Numeric(10, 4),
        nullable=False
    )
    total_yield_kg: Mapped[Decimal] = mapped_column(
        Numeric(12, 2),
        nullable=False
    )
    
    # Palm tree details
    palm_count: Mapped[Optional[int]] = mapped_column(Integer)
    productive_palms: Mapped[Optional[int]] = mapped_column(Integer)
    palm_age_years: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Validation and quality metrics
    measurement_method: Mapped[str] = mapped_column(
        Enum("manual_count", "sensor_data", "drone_survey", "combined", name="measurement_method"),
        nullable=False,
        default="manual_count"
    )
    accuracy_confidence_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # MPOB compliance
    mpob_standard_compliance: Mapped[bool] = mapped_column(Boolean, default=True)
    compliance_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    estate: Mapped["Estate"] = relationship("Estate")
    block: Mapped["EstateBlock"] = relationship("EstateBlock")
    recorded_by: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<YieldValidationData(id={self.id}, date={self.measurement_date}, yield={self.yield_per_hectare_kg}kg/ha)>"


class FertilizerUsageTracking(Base):
    """Fertilizer usage tracking for MPOB compliance."""
    
    __tablename__ = "fertilizer_usage_tracking"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    block_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estate_blocks.id", ondelete="SET NULL"),
        index=True
    )
    
    recorded_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Application details
    application_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    fertilizer_type: Mapped[str] = mapped_column(String(100), nullable=False)
    fertilizer_brand: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Quantity and composition
    quantity_kg: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    area_applied_hectares: Mapped[Decimal] = mapped_column(Numeric(10, 4), nullable=False)
    application_rate_kg_per_hectare: Mapped[Decimal] = mapped_column(Numeric(8, 2), nullable=False)
    
    # Nutrient composition
    nitrogen_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    phosphorus_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    potassium_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    other_nutrients: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Application method and conditions
    application_method: Mapped[str] = mapped_column(
        Enum("broadcast", "spot_application", "fertigation", "foliar", name="application_method"),
        nullable=False,
        default="broadcast"
    )
    weather_conditions: Mapped[Optional[str]] = mapped_column(String(200))
    soil_moisture_level: Mapped[Optional[str]] = mapped_column(
        Enum("dry", "moist", "wet", name="soil_moisture_level")
    )
    
    # Cost tracking
    cost_per_kg: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    total_cost: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    supplier: Mapped[Optional[str]] = mapped_column(String(100))
    
    # MPOB compliance
    mpob_approved_fertilizer: Mapped[bool] = mapped_column(Boolean, default=True)
    environmental_compliance: Mapped[bool] = mapped_column(Boolean, default=True)
    compliance_notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    estate: Mapped["Estate"] = relationship("Estate")
    block: Mapped[Optional["EstateBlock"]] = relationship("EstateBlock")
    recorded_by: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<FertilizerUsageTracking(id={self.id}, date={self.application_date}, type={self.fertilizer_type})>"


class MPOBComplianceReport(Base):
    """MPOB compliance reports for automated regulatory reporting."""
    
    __tablename__ = "mpob_compliance_reports"
    
    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    
    # Foreign keys
    estate_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("estates.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    generated_by_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Report details
    report_type: Mapped[str] = mapped_column(
        Enum("monthly", "quarterly", "annual", "custom", name="report_type"),
        nullable=False
    )
    reporting_period_start: Mapped[date] = mapped_column(Date, nullable=False)
    reporting_period_end: Mapped[date] = mapped_column(Date, nullable=False)
    
    # Report data
    report_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    summary_metrics: Mapped[dict] = mapped_column(JSON, nullable=False)
    compliance_status: Mapped[str] = mapped_column(
        Enum("compliant", "non_compliant", "pending_review", name="compliance_status"),
        nullable=False,
        default="pending_review"
    )
    
    # MPOB submission
    mpob_submitted: Mapped[bool] = mapped_column(Boolean, default=False)
    mpob_submission_id: Mapped[Optional[str]] = mapped_column(String(100))
    mpob_submitted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    mpob_response: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # File attachments
    report_file_path: Mapped[Optional[str]] = mapped_column(String(500))
    supporting_documents: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now()
    )
    
    # Relationships
    estate: Mapped["Estate"] = relationship("Estate")
    generated_by: Mapped["User"] = relationship("User")
    
    def __repr__(self) -> str:
        return f"<MPOBComplianceReport(id={self.id}, type={self.report_type}, period={self.reporting_period_start}-{self.reporting_period_end})>"
