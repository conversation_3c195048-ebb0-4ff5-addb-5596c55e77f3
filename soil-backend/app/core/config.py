"""
Configuration management for Yield Sight System Soil Backend.

This module handles all application configuration using Pydantic settings
with environment variable support and validation.
"""

import secrets
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, PostgresDsn, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application Settings
    APP_NAME: str = "Yield Sight System Soil Backend"
    APP_VERSION: str = "1.0.1"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # Security Settings
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_urlsafe(32), env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=1440, env="ACCESS_TOKEN_EXPIRE_MINUTES")  # 24 hours
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=30, env="REFRESH_TOKEN_EXPIRE_DAYS")
    PASSWORD_RESET_EXPIRE_HOURS: int = Field(default=2, env="PASSWORD_RESET_EXPIRE_HOURS")
    ALGORITHM: str = "HS256"
    
    # CORS Settings
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "https://app.yieldsight.com"],
        env="CORS_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "*.yieldsight.com"],
        env="ALLOWED_HOSTS"
    )
    
    # Database Settings
    DATABASE_URL: PostgresDsn = Field(
        default="postgresql+asyncpg://postgres:password@localhost:5432/soil_backend",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")
    
    # Redis Settings
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    
    # Rate Limiting Settings
    RATE_LIMIT_PER_HOUR: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")
    RATE_LIMIT_PER_MINUTE: int = Field(default=100, env="RATE_LIMIT_PER_MINUTE")
    
    # AI/ML Settings
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_API_BASE: str = Field(default="https://api.openai.com/v1", env="OPENAI_API_BASE")
    OPENAI_MODEL: str = Field(default="gpt-4", env="OPENAI_MODEL")
    OPENAI_MAX_TOKENS: int = Field(default=2000, env="OPENAI_MAX_TOKENS")
    
    # Mem0 Settings
    MEM0_API_KEY: Optional[str] = Field(default=None, env="MEM0_API_KEY")
    MEM0_BASE_URL: str = Field(default="http://localhost:8001", env="MEM0_BASE_URL")
    
    # External API Settings
    WEATHER_API_KEY: Optional[str] = Field(default=None, env="WEATHER_API_KEY")
    ELEVATION_API_KEY: Optional[str] = Field(default=None, env="ELEVATION_API_KEY")
    
    # File Storage Settings
    UPLOAD_DIR: str = Field(default="./uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "application/pdf", "text/csv"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Email Settings
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="SMTP_USE_TLS")
    FROM_EMAIL: str = Field(default="<EMAIL>", env="FROM_EMAIL")
    
    # Monitoring Settings
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    # Celery Settings (for background tasks)
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    
    # Data Processing Settings
    PREDICTION_CONFIDENCE_THRESHOLD: float = Field(default=0.7, env="PREDICTION_CONFIDENCE_THRESHOLD")
    DATA_QUALITY_THRESHOLD: float = Field(default=0.8, env="DATA_QUALITY_THRESHOLD")
    SENSOR_TIMEOUT_HOURS: int = Field(default=24, env="SENSOR_TIMEOUT_HOURS")
    
    # Backup and Retention Settings
    DATA_RETENTION_DAYS: int = Field(default=2555, env="DATA_RETENTION_DAYS")  # 7 years
    BACKUP_RETENTION_DAYS: int = Field(default=90, env="BACKUP_RETENTION_DAYS")
    AUTO_BACKUP_ENABLED: bool = Field(default=True, env="AUTO_BACKUP_ENABLED")
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("ALLOWED_FILE_TYPES", mode="before")
    @classmethod
    def assemble_file_types(cls, v):
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings."""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"


class ProductionSettings(Settings):
    """Production environment settings."""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    # Enhanced security for production
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60  # 1 hour
    RATE_LIMIT_PER_HOUR: int = 500
    RATE_LIMIT_PER_MINUTE: int = 50


class TestingSettings(Settings):
    """Testing environment settings."""
    DEBUG: bool = True
    DATABASE_URL: PostgresDsn = "postgresql+asyncpg://postgres:password@localhost:5432/soil_backend_test"
    REDIS_URL: str = "redis://localhost:6379/15"  # Use different Redis DB for tests
    LOG_LEVEL: str = "WARNING"


def get_environment_settings(environment: str = "development") -> Settings:
    """Get settings for specific environment."""
    if environment == "production":
        return ProductionSettings()
    elif environment == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Global settings instance
settings = get_settings()
