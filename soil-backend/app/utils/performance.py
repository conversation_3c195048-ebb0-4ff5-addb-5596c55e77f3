"""
Performance optimization utilities for the Soil AI platform.

This module provides caching, database optimization, and monitoring
utilities to meet the specified performance requirements.
"""

import time
import asyncio
import functools
from typing import Any, Callable, Dict, List, Optional, Union
from datetime import datetime, timedelta
import json
import hashlib

import redis
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from app.core.config import settings
from app.utils.logging import get_logger

logger = get_logger(__name__)


class PerformanceMonitor:
    """Performance monitoring and metrics collection."""
    
    def __init__(self):
        self.metrics: Dict[str, Any] = {}
        self.request_times: Dict[str, list] = {}
    
    def record_request_time(self, endpoint: str, duration_ms: float):
        """Record request timing for an endpoint."""
        if endpoint not in self.request_times:
            self.request_times[endpoint] = []
        
        self.request_times[endpoint].append({
            "duration_ms": duration_ms,
            "timestamp": datetime.utcnow()
        })
        
        # Keep only last 1000 requests per endpoint
        if len(self.request_times[endpoint]) > 1000:
            self.request_times[endpoint] = self.request_times[endpoint][-1000:]
    
    def get_endpoint_stats(self, endpoint: str) -> Dict[str, Any]:
        """Get performance statistics for an endpoint."""
        if endpoint not in self.request_times:
            return {"error": "No data available"}
        
        times = [r["duration_ms"] for r in self.request_times[endpoint]]
        
        if not times:
            return {"error": "No timing data available"}
        
        times.sort()
        count = len(times)
        
        return {
            "endpoint": endpoint,
            "request_count": count,
            "average_ms": sum(times) / count,
            "median_ms": times[count // 2],
            "p95_ms": times[int(count * 0.95)] if count > 0 else 0,
            "p99_ms": times[int(count * 0.99)] if count > 0 else 0,
            "min_ms": min(times),
            "max_ms": max(times),
            "last_hour_count": len([
                r for r in self.request_times[endpoint]
                if r["timestamp"] > datetime.utcnow() - timedelta(hours=1)
            ])
        }
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all endpoints."""
        return {
            endpoint: self.get_endpoint_stats(endpoint)
            for endpoint in self.request_times.keys()
        }


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def timing_decorator(func: Callable) -> Callable:
    """Decorator to measure function execution time."""
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            duration = (time.time() - start_time) * 1000  # Convert to milliseconds
            endpoint = getattr(func, '__name__', 'unknown')
            performance_monitor.record_request_time(endpoint, duration)
            
            # Log slow requests
            if duration > 200:  # Log requests slower than 200ms
                logger.warning(
                    "Slow request detected",
                    function=endpoint,
                    duration_ms=duration,
                    args_count=len(args),
                    kwargs_keys=list(kwargs.keys())
                )
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            duration = (time.time() - start_time) * 1000
            endpoint = getattr(func, '__name__', 'unknown')
            performance_monitor.record_request_time(endpoint, duration)
            
            if duration > 200:
                logger.warning(
                    "Slow request detected",
                    function=endpoint,
                    duration_ms=duration
                )
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


class CacheManager:
    """Redis-based caching manager for performance optimization."""
    
    def __init__(self):
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # Test connection
            self.redis_client.ping()
            self.enabled = True
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Redis cache not available: {e}")
            self.redis_client = None
            self.enabled = False
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from function arguments."""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self.enabled:
            return None
        
        try:
            value = self.redis_client.get(key)
            if value:
                return json.loads(value)
        except Exception as e:
            logger.error(f"Cache get error: {e}")
        
        return None
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Set value in cache with TTL."""
        if not self.enabled:
            return False
        
        try:
            serialized_value = json.dumps(value, default=str)
            return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        if not self.enabled:
            return False
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        if not self.enabled:
            return 0
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Cache clear pattern error: {e}")
            return 0


# Global cache manager instance
cache_manager = CacheManager()


def cached(ttl: int = 300, key_prefix: str = "default"):
    """Decorator for caching function results."""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_manager._generate_key(f"{key_prefix}:{func.__name__}", *args, **kwargs)
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, ttl)
            logger.debug(f"Cache miss for {func.__name__}, result cached")
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For sync functions, use asyncio to run cache operations
            cache_key = cache_manager._generate_key(f"{key_prefix}:{func.__name__}", *args, **kwargs)
            
            try:
                # Try to get from cache synchronously
                cached_result = cache_manager.redis_client.get(cache_key) if cache_manager.enabled else None
                if cached_result:
                    logger.debug(f"Cache hit for {func.__name__}")
                    return json.loads(cached_result)
            except Exception:
                pass
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            try:
                if cache_manager.enabled:
                    serialized_result = json.dumps(result, default=str)
                    cache_manager.redis_client.setex(cache_key, ttl, serialized_result)
                    logger.debug(f"Cache miss for {func.__name__}, result cached")
            except Exception as e:
                logger.error(f"Cache set error in sync wrapper: {e}")
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


class DatabaseOptimizer:
    """Database query optimization utilities."""
    
    @staticmethod
    def optimize_query_with_eager_loading(query, *relationships):
        """Add eager loading to query to prevent N+1 problems."""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(selectinload(relationship))
            else:
                query = query.options(relationship)
        return query
    
    @staticmethod
    def optimize_query_with_joined_loading(query, *relationships):
        """Add joined loading to query for better performance."""
        for relationship in relationships:
            if isinstance(relationship, str):
                query = query.options(joinedload(relationship))
            else:
                query = query.options(relationship)
        return query
    
    @staticmethod
    async def execute_with_timeout(db: AsyncSession, query, timeout: int = 30):
        """Execute query with timeout to prevent hanging."""
        try:
            return await asyncio.wait_for(db.execute(query), timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"Query timeout after {timeout} seconds")
            raise
    
    @staticmethod
    async def get_database_stats(db: AsyncSession) -> Dict[str, Any]:
        """Get database performance statistics."""
        try:
            # Get connection count
            conn_result = await db.execute(text("SELECT count(*) FROM pg_stat_activity"))
            connection_count = conn_result.scalar()
            
            # Get database size
            size_result = await db.execute(text("SELECT pg_database_size(current_database())"))
            database_size = size_result.scalar()
            
            # Get slow queries (if pg_stat_statements is enabled)
            try:
                slow_queries_result = await db.execute(text("""
                    SELECT query, calls, total_time, mean_time 
                    FROM pg_stat_statements 
                    WHERE mean_time > 100 
                    ORDER BY mean_time DESC 
                    LIMIT 10
                """))
                slow_queries = [dict(row) for row in slow_queries_result]
            except Exception:
                slow_queries = []
            
            return {
                "connection_count": connection_count,
                "database_size_bytes": database_size,
                "database_size_mb": database_size / (1024 * 1024) if database_size else 0,
                "slow_queries": slow_queries,
                "timestamp": datetime.utcnow().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"error": str(e)}


class PerformanceOptimizer:
    """Main performance optimization coordinator."""
    
    def __init__(self):
        self.cache = cache_manager
        self.monitor = performance_monitor
        self.db_optimizer = DatabaseOptimizer()
    
    async def get_system_health(self, db: AsyncSession) -> Dict[str, Any]:
        """Get comprehensive system health metrics."""
        
        # Get performance stats
        perf_stats = self.monitor.get_all_stats()
        
        # Get database stats
        db_stats = await self.db_optimizer.get_database_stats(db)
        
        # Get cache stats
        cache_stats = {"enabled": self.cache.enabled}
        if self.cache.enabled:
            try:
                info = self.cache.redis_client.info()
                cache_stats.update({
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_mb": info.get("used_memory", 0) / (1024 * 1024),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                    "hit_rate": info.get("keyspace_hits", 0) / max(
                        info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1
                    ) * 100
                })
            except Exception as e:
                cache_stats["error"] = str(e)
        
        # Calculate overall health score
        health_score = 100
        
        # Check API performance
        for endpoint, stats in perf_stats.items():
            if isinstance(stats, dict) and "p95_ms" in stats:
                if stats["p95_ms"] > 200:
                    health_score -= 10
                if stats["p95_ms"] > 500:
                    health_score -= 20
        
        # Check database performance
        if isinstance(db_stats, dict) and "connection_count" in db_stats:
            if db_stats["connection_count"] > 100:
                health_score -= 10
        
        # Check cache performance
        if cache_stats.get("hit_rate", 0) < 50:
            health_score -= 5
        
        health_score = max(0, health_score)
        
        return {
            "overall_health_score": health_score,
            "status": "healthy" if health_score >= 80 else "degraded" if health_score >= 60 else "unhealthy",
            "api_performance": perf_stats,
            "database_performance": db_stats,
            "cache_performance": cache_stats,
            "timestamp": datetime.utcnow().isoformat(),
            "recommendations": self._generate_recommendations(perf_stats, db_stats, cache_stats)
        }
    
    def _generate_recommendations(
        self,
        perf_stats: Dict,
        db_stats: Dict,
        cache_stats: Dict
    ) -> List[str]:
        """Generate performance optimization recommendations."""
        
        recommendations = []
        
        # API performance recommendations
        for endpoint, stats in perf_stats.items():
            if isinstance(stats, dict) and "p95_ms" in stats:
                if stats["p95_ms"] > 200:
                    recommendations.append(f"Optimize {endpoint} endpoint - P95 response time is {stats['p95_ms']:.1f}ms")
        
        # Database recommendations
        if isinstance(db_stats, dict):
            if db_stats.get("connection_count", 0) > 80:
                recommendations.append("Consider connection pooling optimization - high connection count detected")
            
            if db_stats.get("database_size_mb", 0) > 1000:
                recommendations.append("Consider database maintenance - large database size detected")
        
        # Cache recommendations
        if not cache_stats.get("enabled", False):
            recommendations.append("Enable Redis caching for better performance")
        elif cache_stats.get("hit_rate", 0) < 70:
            recommendations.append("Optimize cache strategy - low hit rate detected")
        
        return recommendations


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
