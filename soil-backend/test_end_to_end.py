#!/usr/bin/env python3
"""
End-to-end integration test for the complete soil system.

This script tests the integration between:
- Database layer (PostgreSQL with extensions)
- Soil-AI component (BGE embeddings, vector search, Mem0)
- Backend API services (FastAPI endpoints)
"""

import asyncio
import sys
import os
import logging
import uuid
import json
import httpx
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BACKEND_URL = "http://localhost:8000"
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_backend_health():
    """Test backend health endpoint."""
    logger.info("Testing backend health...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BACKEND_URL}/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            
            logger.info(f"✅ Backend health check passed: {data}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Backend health check failed: {e}")
        return False


async def test_api_documentation():
    """Test API documentation availability."""
    logger.info("Testing API documentation...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BACKEND_URL}/docs")
            
            assert response.status_code == 200
            assert "text/html" in response.headers.get("content-type", "")
            
            logger.info("✅ API documentation is accessible")
            return True
            
    except Exception as e:
        logger.error(f"❌ API documentation test failed: {e}")
        return False


async def test_database_connectivity():
    """Test database connectivity through the API."""
    logger.info("Testing database connectivity...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from embeddings.bge_embedder import BGEEmbedder
        from embeddings.vector_search import VectorSearchService
        
        # Test BGE embedder
        embedder = BGEEmbedder()
        test_text = "Testing database connectivity with soil pH 6.5"
        embedding = await embedder.embed_text_async(test_text)
        
        assert embedding is not None
        assert len(embedding) == 1024
        
        # Test vector search
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Add test content
        test_id = str(uuid.uuid4())
        await vector_service.add_content_embedding(
            test_id,
            "e2e_test",
            test_text,
            {"test": "end_to_end", "timestamp": datetime.now().isoformat()}
        )
        
        # Search for content
        results = await vector_service.search_similar_content(
            "soil pH testing",
            limit=5
        )
        
        assert len(results) > 0
        logger.info(f"✅ Database connectivity test passed. Found {len(results)} results")
        
        await vector_service.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connectivity test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_soil_ai_integration():
    """Test soil-ai component integration."""
    logger.info("Testing soil-ai integration...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from memory.mem0_adapter import Mem0Adapter
        from embeddings.bge_embedder import BGEEmbedder
        
        # Test Mem0 integration
        embedder = BGEEmbedder()
        mem0_adapter = Mem0Adapter(DATABASE_URL, embedder)
        await mem0_adapter.initialize()
        
        # Use existing test user
        test_user_id = "2ce2f130-5c65-4cc7-b35b-e4fa4a8a7263"
        
        # Add test memory
        memory_text = "End-to-end test: User prefers organic soil amendments"
        memory_id = await mem0_adapter.add_memory(
            test_user_id,
            memory_text,
            {"test": "e2e", "category": "preference"}
        )
        
        assert memory_id is not None
        
        # Search memories
        memories = await mem0_adapter.search_memories(
            test_user_id,
            "organic soil",
            limit=5
        )
        
        assert len(memories) > 0
        logger.info(f"✅ Soil-AI integration test passed. Memory ID: {memory_id}")
        
        await mem0_adapter.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Soil-AI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_vector_search_performance():
    """Test vector search performance."""
    logger.info("Testing vector search performance...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from embeddings.bge_embedder import BGEEmbedder
        from embeddings.vector_search import VectorSearchService
        
        embedder = BGEEmbedder()
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Test multiple soil scenarios
        test_scenarios = [
            "Soil pH is 7.2 with high nitrogen content",
            "Low moisture detected in sandy soil",
            "Optimal phosphorus levels for tomato cultivation",
            "Clay soil with poor drainage issues",
            "Organic matter content needs improvement"
        ]
        
        # Add test scenarios
        for i, scenario in enumerate(test_scenarios):
            test_id = str(uuid.uuid4())
            await vector_service.add_content_embedding(
                test_id,
                "performance_test",
                scenario,
                {"scenario": i, "test": "performance"}
            )
        
        # Test search performance
        start_time = datetime.now()
        results = await vector_service.search_similar_content(
            "soil pH nitrogen tomato",
            limit=10
        )
        end_time = datetime.now()
        
        search_time = (end_time - start_time).total_seconds()
        
        assert len(results) > 0
        assert search_time < 2.0  # Should be fast
        
        logger.info(f"✅ Vector search performance test passed. Search time: {search_time:.3f}s, Results: {len(results)}")
        
        await vector_service.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector search performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_system_statistics():
    """Test system statistics and monitoring."""
    logger.info("Testing system statistics...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from embeddings.vector_search import VectorSearchService
        from memory.mem0_adapter import Mem0Adapter
        from embeddings.bge_embedder import BGEEmbedder
        
        embedder = BGEEmbedder()
        
        # Test vector search statistics
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        stats = await vector_service.get_content_statistics()
        assert "total_embeddings" in stats
        assert stats["total_embeddings"] > 0
        
        logger.info(f"✅ Vector search stats: {stats}")
        await vector_service.close()
        
        # Test Mem0 statistics
        mem0_adapter = Mem0Adapter(DATABASE_URL, embedder)
        await mem0_adapter.initialize()
        
        test_user_id = "2ce2f130-5c65-4cc7-b35b-e4fa4a8a7263"
        memory_stats = await mem0_adapter.get_memory_stats(test_user_id)
        assert "total_memories" in memory_stats
        
        logger.info(f"✅ Memory stats: {memory_stats}")
        await mem0_adapter.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ System statistics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_end_to_end_tests():
    """Run all end-to-end integration tests."""
    logger.info("Starting end-to-end integration tests...")
    
    tests = [
        ("Backend Health", test_backend_health),
        ("API Documentation", test_api_documentation),
        ("Database Connectivity", test_database_connectivity),
        ("Soil-AI Integration", test_soil_ai_integration),
        ("Vector Search Performance", test_vector_search_performance),
        ("System Statistics", test_system_statistics),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*70}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*70}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("END-TO-END INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*70}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL END-TO-END INTEGRATION TESTS PASSED!")
        logger.info("✅ Complete soil system is working correctly!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests FAILED!")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_end_to_end_tests())
    sys.exit(0 if success else 1)
