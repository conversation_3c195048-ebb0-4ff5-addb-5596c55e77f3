-- Migration: Add new user roles for Minister, Researcher, General Staff, and Decision Maker personas
-- Version: 002
-- Description: Extends the user_role enum to support all 8 personas from the PRD

-- Add new values to the user_role enum
ALTER TYPE user_role ADD VALUE 'minister';
ALTER TYPE user_role ADD VALUE 'researcher';
ALTER TYPE user_role ADD VALUE 'general_staff';
ALTER TYPE user_role ADD VALUE 'decision_maker';

-- <PERSON>reate indexes for better performance on role-based queries
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active) WHERE is_active = true;

-- Update any existing admin users to have decision_maker role if needed
-- (This is optional and depends on business requirements)
-- UPDATE users SET role = 'decision_maker' WHERE role = 'admin' AND email LIKE '%director%';

-- Add comments for documentation
COMMENT ON TYPE user_role IS 'User roles supporting all 8 personas: admin, manager, agronomist, technician, minister, researcher, general_staff, decision_maker';
COMMENT ON COLUMN users.role IS 'User role determining access permissions and dashboard features';
