"""
Comprehensive test suite for Model Performance System.

Tests model performance monitoring, accuracy validation,
prediction feedback loops, and SHAP explanations.
"""

import pytest
import pytest_asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from uuid import uuid4
from unittest.mock import patch, MagicMock

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.models.user import User
from app.models.model_performance import (
    ModelPerformanceMetric,
    PredictionFeedback,
    ModelAccuracyValidation,
    SHAPExplanationRecord
)
from app.services.model_performance_service import model_performance_service
from tests.conftest import create_test_user


@pytest.mark.unit
class TestModelPerformanceModels:
    """Test model performance models."""
    
    async def test_model_performance_metric_creation(
        self,
        test_db: AsyncSession
    ):
        """Test creating model performance metric."""
        metric = ModelPerformanceMetric(
            model_name="ensemble_model",
            model_version="1.2.0",
            model_type="ensemble",
            accuracy_r2=Decimal("0.92"),
            rmse=Decimal("0.15"),
            mae=Decimal("0.12"),
            mape=Decimal("8.5"),
            avg_confidence_score=Decimal("0.87"),
            confidence_distribution={"high": 0.75, "medium": 0.20, "low": 0.05},
            meets_accuracy_target=True,
            accuracy_target_range="85-95%",
            avg_latency_ms=Decimal("245.0"),
            p95_latency_ms=Decimal("380.0"),
            p99_latency_ms=Decimal("450.0"),
            meets_latency_target=True,
            prediction_count=1247,
            error_count=5,
            error_rate=Decimal("0.004"),
            evaluation_date=date.today(),
            evaluation_period_start=datetime.utcnow() - timedelta(days=1),
            evaluation_period_end=datetime.utcnow(),
            evaluation_dataset_size=1000
        )
        
        test_db.add(metric)
        await test_db.commit()
        await test_db.refresh(metric)
        
        assert metric.id is not None
        assert metric.model_name == "ensemble_model"
        assert metric.accuracy_r2 == Decimal("0.92")
        assert metric.meets_accuracy_target is True
        assert metric.avg_latency_ms == Decimal("245.0")
        assert metric.meets_latency_target is True
        assert metric.prediction_count == 1247
        assert metric.error_rate == Decimal("0.004")
    
    async def test_prediction_feedback_creation(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating prediction feedback."""
        feedback = PredictionFeedback(
            user_id=test_user.id,
            prediction_id="pred_123456",
            model_name="ensemble_model",
            model_version="1.2.0",
            predicted_values={"ph": 6.8, "nitrogen": 25.0, "phosphorus": 18.5},
            confidence_scores={"ph": 0.92, "nitrogen": 0.88, "phosphorus": 0.85},
            feedback_type="accuracy_rating",
            feedback_rating=4,
            corrected_values={"ph": 6.9, "nitrogen": 24.5, "phosphorus": 18.8},
            feedback_text="Predictions were close but slightly off for nitrogen",
            measurement_method="lab_analysis",
            measurement_date=date.today()
        )
        
        test_db.add(feedback)
        await test_db.commit()
        await test_db.refresh(feedback)
        
        assert feedback.id is not None
        assert feedback.user_id == test_user.id
        assert feedback.prediction_id == "pred_123456"
        assert feedback.feedback_type == "accuracy_rating"
        assert feedback.feedback_rating == 4
        assert feedback.predicted_values["ph"] == 6.8
        assert feedback.corrected_values["ph"] == 6.9
        assert feedback.is_processed is False
    
    async def test_model_accuracy_validation_creation(
        self,
        test_db: AsyncSession
    ):
        """Test creating model accuracy validation."""
        validation = ModelAccuracyValidation(
            model_name="xgboost_model",
            model_version="2.1.0",
            validation_type="cross_validation",
            validation_dataset_size=5000,
            validation_method="5_fold_cv",
            overall_accuracy=Decimal("0.91"),
            parameter_accuracies={"ph": 0.93, "nitrogen": 0.89, "phosphorus": 0.90},
            r2_score=Decimal("0.91"),
            rmse=Decimal("0.16"),
            mae=Decimal("0.13"),
            confidence_calibration={"reliability": 0.88, "sharpness": 0.75},
            confidence_reliability=Decimal("0.88"),
            meets_85_95_target=True,
            accuracy_percentile=Decimal("91.0"),
            results_by_parameter={"ph": 0.93, "nitrogen": 0.89, "phosphorus": 0.90},
            validation_date=date.today(),
            validation_status="completed"
        )
        
        test_db.add(validation)
        await test_db.commit()
        await test_db.refresh(validation)
        
        assert validation.id is not None
        assert validation.model_name == "xgboost_model"
        assert validation.overall_accuracy == Decimal("0.91")
        assert validation.meets_85_95_target is True
        assert validation.validation_status == "completed"
        assert validation.parameter_accuracies["ph"] == 0.93
    
    async def test_shap_explanation_record_creation(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating SHAP explanation record."""
        explanation = SHAPExplanationRecord(
            prediction_id="pred_789012",
            model_name="neural_network",
            model_version="1.5.0",
            feature_importance={"moisture": 0.35, "temperature": 0.28, "ec": 0.22, "depth": 0.15},
            shap_values={"moisture": [0.12, -0.08], "temperature": [0.05, 0.03]},
            base_values={"ph": 6.5, "nitrogen": 20.0},
            top_positive_features=[
                {"feature": "moisture", "importance": 0.35},
                {"feature": "temperature", "importance": 0.28}
            ],
            top_negative_features=[
                {"feature": "depth", "importance": -0.15}
            ],
            explanation_confidence=Decimal("0.89"),
            user_id=test_user.id,
            input_features={"moisture": 45.5, "temperature": 22.3, "ec": 1.2, "depth": 15.0},
            prediction_output={"ph": 6.8, "nitrogen": 25.0},
            explanation_time_ms=Decimal("125.5")
        )
        
        test_db.add(explanation)
        await test_db.commit()
        await test_db.refresh(explanation)
        
        assert explanation.id is not None
        assert explanation.prediction_id == "pred_789012"
        assert explanation.model_name == "neural_network"
        assert explanation.explanation_confidence == Decimal("0.89")
        assert len(explanation.top_positive_features) == 2
        assert explanation.feature_importance["moisture"] == 0.35


@pytest.mark.unit
class TestModelPerformanceService:
    """Test model performance service functionality."""
    
    async def test_calculate_accuracy_metrics(self):
        """Test accuracy metrics calculation."""
        predictions = [
            {"ph": 6.8, "nitrogen": 25.0},
            {"ph": 7.2, "nitrogen": 28.5},
            {"ph": 6.5, "nitrogen": 22.0}
        ]
        
        actual_values = [
            {"ph": 6.9, "nitrogen": 24.5},
            {"ph": 7.0, "nitrogen": 29.0},
            {"ph": 6.6, "nitrogen": 21.5}
        ]
        
        metrics = model_performance_service._calculate_accuracy_metrics(predictions, actual_values)
        
        assert "r2" in metrics
        assert "rmse" in metrics
        assert "mae" in metrics
        assert "mape" in metrics
        assert metrics["r2"] >= 0.0  # R² should be reasonable
        assert metrics["rmse"] > 0.0  # RMSE should be positive
        assert metrics["mae"] > 0.0   # MAE should be positive
        assert metrics["mape"] >= 0.0 # MAPE should be non-negative
    
    async def test_calculate_confidence_metrics(self):
        """Test confidence metrics calculation."""
        confidence_scores = [
            {"ph": 0.92, "nitrogen": 0.88},
            {"ph": 0.85, "nitrogen": 0.90},
            {"ph": 0.78, "nitrogen": 0.82}
        ]
        
        metrics = model_performance_service._calculate_confidence_metrics(confidence_scores)
        
        assert "avg_confidence" in metrics
        assert "distribution" in metrics
        assert 0.0 <= metrics["avg_confidence"] <= 1.0
        
        distribution = metrics["distribution"]
        assert "high" in distribution
        assert "medium" in distribution
        assert "low" in distribution
        assert abs(distribution["high"] + distribution["medium"] + distribution["low"] - 1.0) < 0.01
    
    async def test_calculate_latency_metrics(self):
        """Test latency metrics calculation."""
        latencies = [200.0, 250.0, 180.0, 300.0, 220.0, 400.0, 190.0, 280.0]
        
        metrics = model_performance_service._calculate_latency_metrics(latencies)
        
        assert "avg_latency" in metrics
        assert "p95_latency" in metrics
        assert "p99_latency" in metrics
        assert metrics["avg_latency"] > 0.0
        assert metrics["p95_latency"] >= metrics["avg_latency"]
        assert metrics["p99_latency"] >= metrics["p95_latency"]
    
    async def test_record_prediction_feedback(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test recording prediction feedback."""
        feedback = await model_performance_service.record_prediction_feedback(
            user=test_user,
            prediction_id="test_pred_123",
            model_name="test_model",
            model_version="1.0.0",
            predicted_values={"ph": 6.8},
            feedback_type="accuracy_rating",
            feedback_rating=5,
            corrected_values={"ph": 6.9},
            feedback_text="Very accurate prediction",
            db=test_db
        )
        
        assert feedback.id is not None
        assert feedback.user_id == test_user.id
        assert feedback.prediction_id == "test_pred_123"
        assert feedback.feedback_rating == 5
        assert feedback.predicted_values["ph"] == 6.8
        assert feedback.corrected_values["ph"] == 6.9
    
    async def test_record_shap_explanation(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test recording SHAP explanation."""
        explanation = await model_performance_service.record_shap_explanation(
            prediction_id="test_pred_456",
            model_name="test_model",
            model_version="1.0.0",
            feature_importance={"moisture": 0.4, "temperature": 0.3, "ph": 0.3},
            shap_values={"moisture": [0.1, -0.05], "temperature": [0.08, 0.02]},
            base_values={"ph": 6.5},
            input_features={"moisture": 45.0, "temperature": 22.0, "ph": 6.8},
            prediction_output={"nitrogen": 25.0},
            explanation_confidence=0.92,
            user_id=test_user.id,
            explanation_time_ms=150.0,
            db=test_db
        )
        
        assert explanation.id is not None
        assert explanation.prediction_id == "test_pred_456"
        assert explanation.explanation_confidence == Decimal("0.92")
        assert len(explanation.top_positive_features) > 0
        assert explanation.feature_importance["moisture"] == 0.4


@pytest.mark.integration
class TestModelPerformanceAPI:
    """Test model performance API endpoints."""
    
    @pytest_asyncio.fixture
    async def auth_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="technician",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @pytest_asyncio.fixture
    async def manager_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get manager authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="manager",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    def test_create_prediction_feedback(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test creating prediction feedback via API."""
        feedback_data = {
            "prediction_id": "api_test_pred_123",
            "model_name": "ensemble_model",
            "model_version": "1.2.0",
            "predicted_values": {"ph": 6.8, "nitrogen": 25.0},
            "feedback_type": "accuracy_rating",
            "feedback_rating": 4,
            "corrected_values": {"ph": 6.9, "nitrogen": 24.5},
            "feedback_text": "Good predictions with minor adjustments needed",
            "confidence_scores": {"ph": 0.92, "nitrogen": 0.88}
        }
        
        response = client.post(
            "/api/v1/model-performance/feedback",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=feedback_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["prediction_id"] == "api_test_pred_123"
        assert data["feedback_type"] == "accuracy_rating"
        assert data["feedback_rating"] == 4
        assert data["predicted_values"]["ph"] == 6.8
        assert data["is_processed"] is False
    
    def test_get_prediction_feedback(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting prediction feedback."""
        response = client.get(
            "/api/v1/model-performance/feedback",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_accuracy_validations(
        self,
        client: TestClient,
        manager_token: str
    ):
        """Test getting accuracy validations."""
        response = client.get(
            "/api/v1/model-performance/accuracy-validations",
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_performance_metrics(
        self,
        client: TestClient,
        manager_token: str
    ):
        """Test getting performance metrics."""
        response = client.get(
            "/api/v1/model-performance/metrics",
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_shap_explanations(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting SHAP explanations."""
        response = client.get(
            "/api/v1/model-performance/shap-explanations",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_accuracy_compliance_status(
        self,
        client: TestClient,
        manager_token: str
    ):
        """Test getting accuracy compliance status."""
        response = client.get(
            "/api/v1/model-performance/accuracy-compliance",
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        if "error" not in data:
            assert "compliance_summary" in data
            assert "compliant_models" in data
            assert "non_compliant_models" in data
            assert "accuracy_distribution" in data
    
    def test_get_feedback_analytics(
        self,
        client: TestClient,
        manager_token: str
    ):
        """Test getting feedback analytics."""
        response = client.get(
            "/api/v1/model-performance/feedback-analytics",
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        if "error" not in data:
            assert "period_summary" in data
            assert "feedback_by_type" in data
            assert "feedback_by_rating" in data
            assert "rating_distribution" in data
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to model performance endpoints."""
        response = client.get("/api/v1/model-performance/feedback")
        assert response.status_code == 401
        
        response = client.post(
            "/api/v1/model-performance/feedback",
            json={"prediction_id": "test"}
        )
        assert response.status_code == 401
    
    def test_invalid_feedback_data(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test validation of invalid feedback data."""
        invalid_feedback = {
            "prediction_id": "test",
            "model_name": "test_model",
            "model_version": "1.0.0",
            "predicted_values": {"ph": 6.8},
            "feedback_type": "invalid_type",  # Invalid type
            "feedback_rating": 10  # Invalid rating (should be 1-5)
        }
        
        response = client.post(
            "/api/v1/model-performance/feedback",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=invalid_feedback
        )
        
        assert response.status_code == 422  # Validation error
