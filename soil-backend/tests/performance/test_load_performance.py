"""
Comprehensive load and performance testing suite.

Tests the system's ability to handle the specified performance requirements:
- 50-200 concurrent users
- <200ms API response times
- 10,000 readings per minute
- 99.5% uptime
"""

import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import pytest
import requests
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from app.main import app


class PerformanceTestRunner:
    """Performance test runner for load testing."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = TestClient(app)
        self.results: List[Dict[str, Any]] = []
    
    def authenticate_user(self, email: str = "<EMAIL>", password: str = "TestPassword123!") -> str:
        """Authenticate and get access token."""
        try:
            response = self.client.post(
                "/api/v1/auth/login",
                data={"username": email, "password": password}
            )
            if response.status_code == 200:
                return response.json()["access_token"]
            else:
                # Create user if doesn't exist
                self.client.post(
                    "/api/v1/auth/register",
                    json={
                        "email": email,
                        "password": password,
                        "first_name": "Test",
                        "last_name": "User",
                        "role": "technician"
                    }
                )
                # Try login again
                response = self.client.post(
                    "/api/v1/auth/login",
                    data={"username": email, "password": password}
                )
                return response.json()["access_token"] if response.status_code == 200 else None
        except Exception:
            return None
    
    def make_request(self, method: str, endpoint: str, headers: Dict = None, json_data: Dict = None) -> Dict[str, Any]:
        """Make a single request and measure performance."""
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                response = self.client.get(endpoint, headers=headers)
            elif method.upper() == "POST":
                response = self.client.post(endpoint, headers=headers, json=json_data)
            elif method.upper() == "PUT":
                response = self.client.put(endpoint, headers=headers, json=json_data)
            else:
                response = self.client.get(endpoint, headers=headers)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            return {
                "endpoint": endpoint,
                "method": method,
                "status_code": response.status_code,
                "response_time_ms": response_time,
                "success": 200 <= response.status_code < 300,
                "timestamp": datetime.utcnow(),
                "response_size": len(response.content) if hasattr(response, 'content') else 0
            }
        
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            return {
                "endpoint": endpoint,
                "method": method,
                "status_code": 500,
                "response_time_ms": response_time,
                "success": False,
                "error": str(e),
                "timestamp": datetime.utcnow(),
                "response_size": 0
            }
    
    def concurrent_load_test(
        self,
        endpoint: str,
        method: str = "GET",
        concurrent_users: int = 50,
        requests_per_user: int = 10,
        json_data: Dict = None
    ) -> Dict[str, Any]:
        """Run concurrent load test."""
        
        # Get authentication token
        token = self.authenticate_user()
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        print(f"Starting load test: {concurrent_users} users, {requests_per_user} requests each")
        print(f"Target endpoint: {method} {endpoint}")
        
        start_time = time.time()
        results = []
        
        def user_session(user_id: int) -> List[Dict[str, Any]]:
            """Simulate a user session with multiple requests."""
            user_results = []
            for request_num in range(requests_per_user):
                result = self.make_request(method, endpoint, headers, json_data)
                result["user_id"] = user_id
                result["request_num"] = request_num
                user_results.append(result)
                
                # Small delay between requests to simulate real usage
                time.sleep(0.1)
            
            return user_results
        
        # Execute concurrent user sessions
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(user_session, user_id) for user_id in range(concurrent_users)]
            
            for future in as_completed(futures):
                try:
                    user_results = future.result()
                    results.extend(user_results)
                except Exception as e:
                    print(f"User session failed: {e}")
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Analyze results
        response_times = [r["response_time_ms"] for r in results]
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        analysis = {
            "test_configuration": {
                "endpoint": endpoint,
                "method": method,
                "concurrent_users": concurrent_users,
                "requests_per_user": requests_per_user,
                "total_requests": len(results)
            },
            "performance_metrics": {
                "total_duration_seconds": total_duration,
                "requests_per_second": len(results) / total_duration if total_duration > 0 else 0,
                "average_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "median_response_time_ms": statistics.median(response_times) if response_times else 0,
                "p95_response_time_ms": self._percentile(response_times, 95) if response_times else 0,
                "p99_response_time_ms": self._percentile(response_times, 99) if response_times else 0,
                "min_response_time_ms": min(response_times) if response_times else 0,
                "max_response_time_ms": max(response_times) if response_times else 0
            },
            "reliability_metrics": {
                "success_rate_percentage": (len(successful_requests) / len(results)) * 100 if results else 0,
                "total_requests": len(results),
                "successful_requests": len(successful_requests),
                "failed_requests": len(failed_requests),
                "error_rate_percentage": (len(failed_requests) / len(results)) * 100 if results else 0
            },
            "requirements_compliance": {
                "meets_200ms_requirement": self._percentile(response_times, 95) < 200 if response_times else False,
                "meets_concurrent_users_requirement": concurrent_users >= 50,
                "meets_success_rate_requirement": (len(successful_requests) / len(results)) >= 0.995 if results else False
            },
            "detailed_results": results
        }
        
        self.results.append(analysis)
        return analysis
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data."""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def throughput_test(self, endpoint: str, target_rps: int = 167, duration_seconds: int = 60) -> Dict[str, Any]:
        """Test throughput capability (10,000 readings per minute = ~167 RPS)."""
        
        token = self.authenticate_user()
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        print(f"Starting throughput test: {target_rps} RPS for {duration_seconds} seconds")
        
        start_time = time.time()
        results = []
        request_count = 0
        
        # Sample sensor data for POST requests
        sensor_data = {
            "sensor_id": "test_sensor_001",
            "estate_id": "test_estate_001",
            "location": {"latitude": 3.1390, "longitude": 101.6869},
            "readings": {
                "ph": 6.8,
                "nitrogen": 25.0,
                "phosphorus": 18.5,
                "potassium": 22.0,
                "moisture": 45.5,
                "temperature": 28.5
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        while time.time() - start_time < duration_seconds:
            batch_start = time.time()
            
            # Send requests in batches to achieve target RPS
            batch_size = min(10, target_rps)  # Send up to 10 requests per batch
            
            with ThreadPoolExecutor(max_workers=batch_size) as executor:
                futures = []
                for _ in range(batch_size):
                    if endpoint.endswith("/sensor-data"):
                        future = executor.submit(self.make_request, "POST", endpoint, headers, sensor_data)
                    else:
                        future = executor.submit(self.make_request, "GET", endpoint, headers)
                    futures.append(future)
                
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        results.append(result)
                        request_count += 1
                    except Exception as e:
                        print(f"Request failed: {e}")
            
            # Control rate to achieve target RPS
            batch_duration = time.time() - batch_start
            target_batch_duration = batch_size / target_rps
            if batch_duration < target_batch_duration:
                time.sleep(target_batch_duration - batch_duration)
        
        total_duration = time.time() - start_time
        actual_rps = len(results) / total_duration if total_duration > 0 else 0
        
        # Analyze results
        response_times = [r["response_time_ms"] for r in results]
        successful_requests = [r for r in results if r["success"]]
        
        return {
            "test_configuration": {
                "endpoint": endpoint,
                "target_rps": target_rps,
                "duration_seconds": duration_seconds,
                "total_requests_sent": request_count
            },
            "throughput_metrics": {
                "actual_rps": actual_rps,
                "total_requests_completed": len(results),
                "total_duration_seconds": total_duration,
                "throughput_efficiency": (actual_rps / target_rps) * 100 if target_rps > 0 else 0
            },
            "performance_metrics": {
                "average_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "p95_response_time_ms": self._percentile(response_times, 95) if response_times else 0,
                "p99_response_time_ms": self._percentile(response_times, 99) if response_times else 0
            },
            "reliability_metrics": {
                "success_rate_percentage": (len(successful_requests) / len(results)) * 100 if results else 0,
                "error_rate_percentage": ((len(results) - len(successful_requests)) / len(results)) * 100 if results else 0
            },
            "requirements_compliance": {
                "meets_throughput_requirement": actual_rps >= (target_rps * 0.9),  # 90% of target
                "meets_response_time_requirement": self._percentile(response_times, 95) < 200 if response_times else False,
                "meets_reliability_requirement": (len(successful_requests) / len(results)) >= 0.995 if results else False
            }
        }


@pytest.mark.performance
class TestAPIPerformance:
    """Performance tests for API endpoints."""
    
    def setup_method(self):
        """Set up performance test runner."""
        self.runner = PerformanceTestRunner()
    
    def test_sensor_data_endpoint_load(self):
        """Test sensor data endpoint under load."""
        result = self.runner.concurrent_load_test(
            endpoint="/api/v1/sensor-data",
            method="GET",
            concurrent_users=50,
            requests_per_user=20
        )
        
        # Assert performance requirements
        assert result["performance_metrics"]["p95_response_time_ms"] < 200, \
            f"P95 response time {result['performance_metrics']['p95_response_time_ms']}ms exceeds 200ms requirement"
        
        assert result["reliability_metrics"]["success_rate_percentage"] >= 99.5, \
            f"Success rate {result['reliability_metrics']['success_rate_percentage']}% below 99.5% requirement"
        
        assert result["performance_metrics"]["requests_per_second"] > 100, \
            f"RPS {result['performance_metrics']['requests_per_second']} too low for load test"
    
    def test_predictions_endpoint_load(self):
        """Test predictions endpoint under load."""
        result = self.runner.concurrent_load_test(
            endpoint="/api/v1/predictions",
            method="GET",
            concurrent_users=100,
            requests_per_user=10
        )
        
        # Assert performance requirements
        assert result["performance_metrics"]["p95_response_time_ms"] < 200, \
            f"P95 response time {result['performance_metrics']['p95_response_time_ms']}ms exceeds 200ms requirement"
        
        assert result["reliability_metrics"]["success_rate_percentage"] >= 99.5, \
            f"Success rate {result['reliability_metrics']['success_rate_percentage']}% below 99.5% requirement"
    
    def test_sensor_data_throughput(self):
        """Test sensor data ingestion throughput."""
        result = self.runner.throughput_test(
            endpoint="/api/v1/sensor-data",
            target_rps=167,  # 10,000 per minute
            duration_seconds=30
        )
        
        # Assert throughput requirements
        assert result["throughput_metrics"]["actual_rps"] >= 150, \
            f"Actual RPS {result['throughput_metrics']['actual_rps']} below minimum requirement"
        
        assert result["performance_metrics"]["p95_response_time_ms"] < 200, \
            f"P95 response time {result['performance_metrics']['p95_response_time_ms']}ms exceeds 200ms requirement"
        
        assert result["reliability_metrics"]["success_rate_percentage"] >= 99.5, \
            f"Success rate {result['reliability_metrics']['success_rate_percentage']}% below 99.5% requirement"
    
    def test_concurrent_users_scalability(self):
        """Test scalability with increasing concurrent users."""
        user_counts = [50, 100, 150, 200]
        results = []
        
        for user_count in user_counts:
            result = self.runner.concurrent_load_test(
                endpoint="/api/v1/estates",
                method="GET",
                concurrent_users=user_count,
                requests_per_user=5
            )
            results.append(result)
            
            # Check if performance degrades significantly
            if user_count <= 200:  # Within specified range
                assert result["performance_metrics"]["p95_response_time_ms"] < 200, \
                    f"P95 response time {result['performance_metrics']['p95_response_time_ms']}ms exceeds 200ms at {user_count} users"
        
        # Ensure performance doesn't degrade too much with increased load
        response_times = [r["performance_metrics"]["p95_response_time_ms"] for r in results]
        max_degradation = max(response_times) / min(response_times) if min(response_times) > 0 else 1
        
        assert max_degradation < 3.0, \
            f"Performance degradation factor {max_degradation} too high across user loads"
    
    def test_database_query_performance(self):
        """Test database query performance under load."""
        endpoints = [
            "/api/v1/sensor-data",
            "/api/v1/estates",
            "/api/v1/predictions",
            "/api/v1/users"
        ]
        
        for endpoint in endpoints:
            result = self.runner.concurrent_load_test(
                endpoint=endpoint,
                method="GET",
                concurrent_users=75,
                requests_per_user=10
            )
            
            # Database queries should be fast
            assert result["performance_metrics"]["average_response_time_ms"] < 100, \
                f"Average response time {result['performance_metrics']['average_response_time_ms']}ms too high for {endpoint}"
            
            assert result["performance_metrics"]["p99_response_time_ms"] < 500, \
                f"P99 response time {result['performance_metrics']['p99_response_time_ms']}ms too high for {endpoint}"
    
    def test_memory_and_cpu_efficiency(self):
        """Test memory and CPU efficiency under load."""
        import psutil
        import os
        
        # Get initial system metrics
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = process.cpu_percent()
        
        # Run intensive load test
        result = self.runner.concurrent_load_test(
            endpoint="/api/v1/predictions",
            method="GET",
            concurrent_users=150,
            requests_per_user=20
        )
        
        # Check final system metrics
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = process.cpu_percent()
        
        memory_increase = final_memory - initial_memory
        
        # Memory usage should not increase excessively
        assert memory_increase < 500, \
            f"Memory increase {memory_increase}MB too high during load test"
        
        # Performance should still meet requirements
        assert result["performance_metrics"]["p95_response_time_ms"] < 200, \
            f"P95 response time {result['performance_metrics']['p95_response_time_ms']}ms exceeds requirement under load"


if __name__ == "__main__":
    # Run performance tests directly
    runner = PerformanceTestRunner()
    
    print("Running comprehensive performance tests...")
    
    # Test 1: Basic load test
    print("\n1. Basic Load Test (50 concurrent users)")
    result1 = runner.concurrent_load_test("/api/v1/sensor-data", "GET", 50, 10)
    print(f"   P95 Response Time: {result1['performance_metrics']['p95_response_time_ms']:.2f}ms")
    print(f"   Success Rate: {result1['reliability_metrics']['success_rate_percentage']:.2f}%")
    
    # Test 2: Throughput test
    print("\n2. Throughput Test (167 RPS target)")
    result2 = runner.throughput_test("/api/v1/sensor-data", 167, 30)
    print(f"   Actual RPS: {result2['throughput_metrics']['actual_rps']:.2f}")
    print(f"   P95 Response Time: {result2['performance_metrics']['p95_response_time_ms']:.2f}ms")
    
    # Test 3: High concurrency test
    print("\n3. High Concurrency Test (200 concurrent users)")
    result3 = runner.concurrent_load_test("/api/v1/estates", "GET", 200, 5)
    print(f"   P95 Response Time: {result3['performance_metrics']['p95_response_time_ms']:.2f}ms")
    print(f"   Success Rate: {result3['reliability_metrics']['success_rate_percentage']:.2f}%")
    
    print("\nPerformance testing completed!")
