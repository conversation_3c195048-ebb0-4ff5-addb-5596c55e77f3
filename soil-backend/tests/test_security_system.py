"""
Comprehensive test suite for Enhanced Security System.

Tests multi-factor authentication, encryption, audit logging,
and GDPR compliance functionality.
"""

import pytest
import pytest_asyncio
from datetime import datetime, timed<PERSON>ta
from uuid import uuid4
from unittest.mock import patch, MagicMock

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.models.user import User
from app.models.security import (
    UserMFA, MFAVerificationAttempt, SecurityAuditLog,
    LoginAttempt, GDPRDataRequest, AuditEventType, MFAMethod
)
from app.services.security_service import security_service
from tests.conftest import create_test_user


@pytest.mark.unit
class TestSecurityModels:
    """Test security models."""
    
    async def test_user_mfa_creation(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating UserMFA record."""
        mfa_settings = UserMFA(
            user_id=test_user.id,
            is_enabled=True,
            primary_method=MFAMethod.TOTP,
            totp_secret="JBSWY3DPEHPK3PXP",
            totp_verified=True,
            backup_codes=["code1", "code2", "code3"],
            enabled_at=datetime.utcnow()
        )
        
        test_db.add(mfa_settings)
        await test_db.commit()
        await test_db.refresh(mfa_settings)
        
        assert mfa_settings.id is not None
        assert mfa_settings.user_id == test_user.id
        assert mfa_settings.is_enabled is True
        assert mfa_settings.primary_method == MFAMethod.TOTP
        assert mfa_settings.totp_secret == "JBSWY3DPEHPK3PXP"
        assert mfa_settings.totp_verified is True
        assert len(mfa_settings.backup_codes) == 3
    
    async def test_mfa_verification_attempt(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating MFA verification attempt."""
        attempt = MFAVerificationAttempt(
            user_id=test_user.id,
            method_used=MFAMethod.TOTP,
            success=True,
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser"
        )
        
        test_db.add(attempt)
        await test_db.commit()
        await test_db.refresh(attempt)
        
        assert attempt.id is not None
        assert attempt.user_id == test_user.id
        assert attempt.method_used == MFAMethod.TOTP
        assert attempt.success is True
        assert attempt.ip_address == "*************"
        assert attempt.attempted_at is not None
    
    async def test_security_audit_log(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating security audit log entry."""
        audit_log = SecurityAuditLog(
            user_id=test_user.id,
            event_type=AuditEventType.LOGIN_SUCCESS,
            event_description="User logged in successfully",
            event_data={"login_method": "password", "mfa_used": True},
            ip_address="*************",
            user_agent="Mozilla/5.0 Test Browser",
            resource_type="user",
            resource_id=str(test_user.id),
            severity_level="low",
            success=True
        )
        
        test_db.add(audit_log)
        await test_db.commit()
        await test_db.refresh(audit_log)
        
        assert audit_log.id is not None
        assert audit_log.user_id == test_user.id
        assert audit_log.event_type == AuditEventType.LOGIN_SUCCESS
        assert audit_log.event_description == "User logged in successfully"
        assert audit_log.event_data["login_method"] == "password"
        assert audit_log.severity_level == "low"
        assert audit_log.success is True
    
    async def test_gdpr_data_request(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating GDPR data request."""
        gdpr_request = GDPRDataRequest(
            user_id=test_user.id,
            request_type="data_export",
            description="Request for personal data export",
            requested_data_types=["profile", "sensor_data", "predictions"],
            status="pending"
        )
        
        test_db.add(gdpr_request)
        await test_db.commit()
        await test_db.refresh(gdpr_request)
        
        assert gdpr_request.id is not None
        assert gdpr_request.user_id == test_user.id
        assert gdpr_request.request_type == "data_export"
        assert gdpr_request.status == "pending"
        assert "profile" in gdpr_request.requested_data_types
        assert gdpr_request.created_at is not None


@pytest.mark.unit
class TestSecurityService:
    """Test security service functionality."""
    
    @patch('pyotp.random_base32')
    @patch('qrcode.QRCode')
    async def test_setup_mfa_totp(
        self,
        mock_qr,
        mock_random_base32,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test setting up TOTP MFA."""
        # Mock the secret generation
        mock_random_base32.return_value = "JBSWY3DPEHPK3PXP"
        
        # Mock QR code generation
        mock_qr_instance = MagicMock()
        mock_qr.return_value = mock_qr_instance
        mock_img = MagicMock()
        mock_qr_instance.make_image.return_value = mock_img
        
        # Mock image save
        mock_img.save = MagicMock()
        
        with patch('base64.b64encode') as mock_b64:
            mock_b64.return_value = b'fake_qr_code_data'
            
            result = await security_service.setup_mfa_totp(test_user, test_db)
        
        assert "secret" in result
        assert "qr_code" in result
        assert "manual_entry_key" in result
        assert "provisioning_uri" in result
        assert result["secret"] == "JBSWY3DPEHPK3PXP"
    
    @patch('pyotp.TOTP')
    async def test_verify_totp_success(
        self,
        mock_totp_class,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test successful TOTP verification."""
        # Setup MFA for user
        mfa_settings = UserMFA(
            user_id=test_user.id,
            totp_secret="JBSWY3DPEHPK3PXP",
            totp_verified=False
        )
        test_db.add(mfa_settings)
        await test_db.commit()
        
        # Mock TOTP verification
        mock_totp = MagicMock()
        mock_totp.verify.return_value = True
        mock_totp_class.return_value = mock_totp
        
        result = await security_service.verify_totp(test_user, "123456", test_db)
        
        assert result is True
        
        # Check that MFA was marked as verified
        await test_db.refresh(mfa_settings)
        assert mfa_settings.totp_verified is True
        assert mfa_settings.is_enabled is True
    
    @patch('pyotp.TOTP')
    async def test_verify_totp_failure(
        self,
        mock_totp_class,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test failed TOTP verification."""
        # Setup MFA for user
        mfa_settings = UserMFA(
            user_id=test_user.id,
            totp_secret="JBSWY3DPEHPK3PXP",
            totp_verified=False
        )
        test_db.add(mfa_settings)
        await test_db.commit()
        
        # Mock TOTP verification failure
        mock_totp = MagicMock()
        mock_totp.verify.return_value = False
        mock_totp_class.return_value = mock_totp
        
        result = await security_service.verify_totp(test_user, "wrong_token", test_db)
        
        assert result is False
        
        # Check that MFA was not marked as verified
        await test_db.refresh(mfa_settings)
        assert mfa_settings.totp_verified is False
    
    async def test_generate_backup_codes(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test generating backup codes."""
        backup_codes = await security_service.generate_backup_codes(test_user, test_db)
        
        assert len(backup_codes) == 10
        assert all(len(code) == 8 for code in backup_codes)  # 4 hex chars = 8 uppercase chars
        assert all(code.isupper() for code in backup_codes)
    
    async def test_verify_backup_code_success(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test successful backup code verification."""
        # Generate backup codes first
        backup_codes = await security_service.generate_backup_codes(test_user, test_db)
        test_code = backup_codes[0]
        
        # Verify the code
        result = await security_service.verify_backup_code(test_user, test_code, test_db)
        
        assert result is True
        
        # Verify the code cannot be used again
        result_second = await security_service.verify_backup_code(test_user, test_code, test_db)
        assert result_second is False
    
    async def test_encrypt_decrypt_data(self):
        """Test data encryption and decryption."""
        original_data = "sensitive information"
        
        # Encrypt data
        encrypted_data = security_service.encrypt_sensitive_data(original_data)
        assert encrypted_data != original_data
        assert len(encrypted_data) > len(original_data)
        
        # Decrypt data
        decrypted_data = security_service.decrypt_sensitive_data(encrypted_data)
        assert decrypted_data == original_data
    
    async def test_create_gdpr_data_request(
        self,
        test_db: AsyncSession,
        test_user: User
    ):
        """Test creating GDPR data request."""
        gdpr_request = await security_service.create_gdpr_data_request(
            user=test_user,
            request_type="data_export",
            description="Request for data export",
            requested_data_types=["profile", "sensor_data"],
            db=test_db
        )
        
        assert gdpr_request.id is not None
        assert gdpr_request.user_id == test_user.id
        assert gdpr_request.request_type == "data_export"
        assert gdpr_request.description == "Request for data export"
        assert gdpr_request.requested_data_types == ["profile", "sensor_data"]


@pytest.mark.integration
class TestSecurityAPI:
    """Test security API endpoints."""
    
    @pytest_asyncio.fixture
    async def auth_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="technician",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @pytest_asyncio.fixture
    async def admin_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get admin authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="admin",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @patch('app.services.security_service.security_service.setup_mfa_totp')
    def test_setup_mfa_totp_endpoint(
        self,
        mock_setup_mfa,
        client: TestClient,
        auth_token: str
    ):
        """Test MFA TOTP setup endpoint."""
        mock_setup_mfa.return_value = {
            "secret": "JBSWY3DPEHPK3PXP",
            "qr_code": "fake_qr_code_data",
            "manual_entry_key": "JBSWY3DPEHPK3PXP",
            "provisioning_uri": "otpauth://totp/test"
        }
        
        response = client.post(
            "/api/v1/security/mfa/setup",
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"method": "totp"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "secret" in data
        assert "qr_code" in data
        assert data["setup_complete"] is False
    
    @patch('app.services.security_service.security_service.verify_totp')
    def test_verify_mfa_totp_endpoint(
        self,
        mock_verify_totp,
        client: TestClient,
        auth_token: str
    ):
        """Test MFA TOTP verification endpoint."""
        mock_verify_totp.return_value = True
        
        response = client.post(
            "/api/v1/security/mfa/verify",
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"token": "123456", "method": "totp"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["verified"] is True
        assert data["message"] == "MFA verification successful"
    
    @patch('app.services.security_service.security_service.generate_backup_codes')
    def test_generate_backup_codes_endpoint(
        self,
        mock_generate_codes,
        client: TestClient,
        auth_token: str
    ):
        """Test backup codes generation endpoint."""
        mock_generate_codes.return_value = ["CODE1234", "CODE5678", "CODE9012"]
        
        response = client.post(
            "/api/v1/security/mfa/backup-codes",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "backup_codes" in data
        assert len(data["backup_codes"]) == 3
    
    def test_get_mfa_status(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting MFA status."""
        response = client.get(
            "/api/v1/security/mfa/status",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mfa_enabled" in data
        assert "primary_method" in data
        assert "totp_verified" in data
        assert "backup_codes_available" in data
    
    def test_create_gdpr_data_request(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test creating GDPR data request."""
        request_data = {
            "request_type": "data_export",
            "description": "Request for personal data export",
            "requested_data_types": ["profile", "sensor_data", "predictions"]
        }
        
        response = client.post(
            "/api/v1/security/gdpr/data-request",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["request_type"] == "data_export"
        assert data["description"] == "Request for personal data export"
        assert "profile" in data["requested_data_types"]
    
    def test_get_gdpr_data_requests(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting GDPR data requests."""
        response = client.get(
            "/api/v1/security/gdpr/data-requests",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_security_summary(
        self,
        client: TestClient,
        admin_token: str
    ):
        """Test getting security summary."""
        response = client.get(
            "/api/v1/security/summary",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "total_users_with_mfa" in data
        assert "failed_login_attempts_24h" in data
        assert "security_events_24h" in data
        assert "pending_gdpr_requests" in data
        assert "active_sessions" in data
    
    def test_encrypt_decrypt_data_endpoints(
        self,
        client: TestClient,
        admin_token: str
    ):
        """Test data encryption and decryption endpoints."""
        # Test encryption
        test_data = {"sensitive_field": "confidential information"}
        
        encrypt_response = client.post(
            "/api/v1/security/encrypt-data",
            headers={"Authorization": f"Bearer {admin_token}"},
            json=test_data
        )
        
        assert encrypt_response.status_code == 200
        encrypted_data = encrypt_response.json()["encrypted_data"]
        assert encrypted_data["sensitive_field"] != test_data["sensitive_field"]
        
        # Test decryption
        decrypt_response = client.post(
            "/api/v1/security/decrypt-data",
            headers={"Authorization": f"Bearer {admin_token}"},
            json=encrypted_data
        )
        
        assert decrypt_response.status_code == 200
        decrypted_data = decrypt_response.json()["decrypted_data"]
        assert decrypted_data["sensitive_field"] == test_data["sensitive_field"]
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to security endpoints."""
        response = client.get("/api/v1/security/mfa/status")
        assert response.status_code == 401
        
        response = client.post(
            "/api/v1/security/mfa/setup",
            json={"method": "totp"}
        )
        assert response.status_code == 401
    
    def test_non_admin_cannot_access_admin_endpoints(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test that non-admin users cannot access admin endpoints."""
        response = client.get(
            "/api/v1/security/summary",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        assert response.status_code == 403
        
        response = client.post(
            "/api/v1/security/encrypt-data",
            headers={"Authorization": f"Bearer {auth_token}"},
            json={"test": "data"}
        )
        assert response.status_code == 403
