"""
Comprehensive test suite for Authorization System.

Tests the dynamic permissions system for General Staff including
authorization workflows, time-limited permissions, and audit trails.
"""

import pytest
import pytest_asyncio
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import AsyncMock, patch

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.models.user import User
from app.models.authorization import AuthorizationRequest, Authorization, AuthorizationAuditLog
from app.models.estate import Estate
from app.api.v1.authorization import router
from tests.conftest import create_test_user, create_test_estate


@pytest.mark.unit
class TestAuthorizationModels:
    """Test authorization models."""
    
    @pytest_asyncio.fixture
    async def general_staff_user(self, test_db: AsyncSession) -> User:
        """Create a general staff user."""
        return await create_test_user(
            test_db,
            email="<EMAIL>",
            role="general_staff",
            first_name="General",
            last_name="Staff"
        )
    
    @pytest_asyncio.fixture
    async def manager_user(self, test_db: AsyncSession) -> User:
        """Create a manager user."""
        return await create_test_user(
            test_db,
            email="<EMAIL>",
            role="manager",
            first_name="Test",
            last_name="Manager"
        )
    
    @pytest_asyncio.fixture
    async def test_authorization_request(
        self,
        test_db: AsyncSession,
        general_staff_user: User,
        manager_user: User,
        test_estate: Estate
    ) -> AuthorizationRequest:
        """Create a test authorization request."""
        auth_request = AuthorizationRequest(
            requester_id=general_staff_user.id,
            target_persona_id=manager_user.id,
            estate_id=test_estate.id,
            data_entry_type="ffb_harvest_data",
            permission_level="write",
            justification="Need to enter harvest data for estate analysis",
            requested_duration_days=7
        )
        
        test_db.add(auth_request)
        await test_db.commit()
        await test_db.refresh(auth_request)
        
        return auth_request
    
    async def test_authorization_request_creation(
        self,
        test_db: AsyncSession,
        general_staff_user: User,
        manager_user: User,
        test_estate: Estate
    ):
        """Test creating an authorization request."""
        auth_request = AuthorizationRequest(
            requester_id=general_staff_user.id,
            target_persona_id=manager_user.id,
            estate_id=test_estate.id,
            data_entry_type="yield_validation_data",
            permission_level="read",
            justification="Need access for research purposes",
            requested_duration_days=14
        )
        
        test_db.add(auth_request)
        await test_db.commit()
        await test_db.refresh(auth_request)
        
        assert auth_request.id is not None
        assert auth_request.status == "pending"
        assert auth_request.requester_id == general_staff_user.id
        assert auth_request.target_persona_id == manager_user.id
        assert auth_request.data_entry_type == "yield_validation_data"
        assert auth_request.permission_level == "read"
        assert auth_request.requested_duration_days == 14
        assert auth_request.created_at is not None
    
    async def test_authorization_creation(
        self,
        test_db: AsyncSession,
        general_staff_user: User,
        manager_user: User,
        test_estate: Estate
    ):
        """Test creating an authorization."""
        expires_at = datetime.utcnow() + timedelta(days=7)
        
        authorization = Authorization(
            general_staff_user_id=general_staff_user.id,
            authorizing_persona_id=manager_user.id,
            estate_id=test_estate.id,
            authorizing_persona="manager",
            data_entry_type="fertilizer_usage_tracking",
            permission_level="write",
            expires_at=expires_at,
            conditions={"max_entries_per_day": 10}
        )
        
        test_db.add(authorization)
        await test_db.commit()
        await test_db.refresh(authorization)
        
        assert authorization.id is not None
        assert authorization.is_active is True
        assert authorization.expires_at == expires_at
        assert authorization.is_valid is True  # Should be valid since not expired
        assert authorization.conditions == {"max_entries_per_day": 10}
    
    async def test_authorization_expiry(
        self,
        test_db: AsyncSession,
        general_staff_user: User,
        manager_user: User,
        test_estate: Estate
    ):
        """Test authorization expiry logic."""
        # Create expired authorization
        expired_authorization = Authorization(
            general_staff_user_id=general_staff_user.id,
            authorizing_persona_id=manager_user.id,
            estate_id=test_estate.id,
            authorizing_persona="manager",
            data_entry_type="soil_analysis_data",
            permission_level="read",
            expires_at=datetime.utcnow() - timedelta(days=1)  # Expired
        )
        
        test_db.add(expired_authorization)
        await test_db.commit()
        await test_db.refresh(expired_authorization)
        
        assert expired_authorization.is_valid is False  # Should be invalid due to expiry
    
    async def test_authorization_audit_log(
        self,
        test_db: AsyncSession,
        general_staff_user: User
    ):
        """Test authorization audit log creation."""
        audit_log = AuthorizationAuditLog(
            user_id=general_staff_user.id,
            action="request_created",
            details={
                "request_id": str(uuid4()),
                "data_entry_type": "mpob_compliance_data",
                "permission_level": "write"
            }
        )
        
        test_db.add(audit_log)
        await test_db.commit()
        await test_db.refresh(audit_log)
        
        assert audit_log.id is not None
        assert audit_log.user_id == general_staff_user.id
        assert audit_log.action == "request_created"
        assert "request_id" in audit_log.details
        assert audit_log.created_at is not None


@pytest.mark.integration
class TestAuthorizationAPI:
    """Test authorization API endpoints."""
    
    @pytest_asyncio.fixture
    async def general_staff_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get authentication token for general staff user."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="general_staff",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @pytest_asyncio.fixture
    async def manager_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get authentication token for manager user."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="manager",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    def test_create_authorization_request(
        self,
        client: TestClient,
        general_staff_token: str,
        test_estate: Estate
    ):
        """Test creating an authorization request."""
        # First create a manager user to target
        manager_response = client.post(
            "/api/v1/users/",
            headers={"Authorization": f"Bearer {general_staff_token}"},
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "role": "manager",
                "first_name": "Target",
                "last_name": "Manager"
            }
        )
        
        if manager_response.status_code == 201:
            manager_id = manager_response.json()["id"]
        else:
            # Manager might already exist, get from users list
            users_response = client.get(
                "/api/v1/users/",
                headers={"Authorization": f"Bearer {general_staff_token}"}
            )
            managers = [u for u in users_response.json() if u["role"] == "manager"]
            manager_id = managers[0]["id"] if managers else None
        
        if not manager_id:
            pytest.skip("No manager user available for testing")
        
        request_data = {
            "target_persona_id": manager_id,
            "estate_id": str(test_estate.id),
            "data_entry_type": "ffb_harvest_data",
            "permission_level": "write",
            "justification": "Need to enter harvest data for analysis",
            "requested_duration_days": 7
        }
        
        response = client.post(
            "/api/v1/authorization/requests",
            headers={"Authorization": f"Bearer {general_staff_token}"},
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "pending"
        assert data["data_entry_type"] == "ffb_harvest_data"
        assert data["permission_level"] == "write"
        assert data["requested_duration_days"] == 7
    
    def test_get_authorization_requests(
        self,
        client: TestClient,
        general_staff_token: str
    ):
        """Test getting authorization requests."""
        response = client.get(
            "/api/v1/authorization/requests",
            headers={"Authorization": f"Bearer {general_staff_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_active_authorizations(
        self,
        client: TestClient,
        general_staff_token: str
    ):
        """Test getting active authorizations."""
        response = client.get(
            "/api/v1/authorization/active",
            headers={"Authorization": f"Bearer {general_staff_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_available_forms(
        self,
        client: TestClient,
        general_staff_token: str,
        test_estate: Estate
    ):
        """Test getting available data entry forms."""
        response = client.get(
            f"/api/v1/authorization/available-forms?estate_id={test_estate.id}",
            headers={"Authorization": f"Bearer {general_staff_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "core_mpob_forms" in data
        assert "authorized_forms" in data
        assert isinstance(data["core_mpob_forms"], list)
        assert isinstance(data["authorized_forms"], list)
        
        # Check that core MPOB forms are always available
        core_forms = data["core_mpob_forms"]
        expected_core_forms = [
            "ffb_harvest_data",
            "yield_validation_data",
            "fertilizer_usage_tracking",
            "basic_operational_data"
        ]
        for form in expected_core_forms:
            assert form in core_forms
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to authorization endpoints."""
        response = client.get("/api/v1/authorization/requests")
        assert response.status_code == 401
        
        response = client.post(
            "/api/v1/authorization/requests",
            json={"data_entry_type": "test"}
        )
        assert response.status_code == 401
    
    def test_non_general_staff_cannot_create_requests(
        self,
        client: TestClient,
        manager_token: str,
        test_estate: Estate
    ):
        """Test that non-general staff cannot create authorization requests."""
        request_data = {
            "target_persona_id": str(uuid4()),
            "estate_id": str(test_estate.id),
            "data_entry_type": "ffb_harvest_data",
            "permission_level": "write",
            "justification": "Test request",
            "requested_duration_days": 7
        }
        
        response = client.post(
            "/api/v1/authorization/requests",
            headers={"Authorization": f"Bearer {manager_token}"},
            json=request_data
        )
        
        assert response.status_code == 403
        assert "Only General Staff can create authorization requests" in response.json()["detail"]


@pytest.mark.integration
class TestAuthorizationWorkflow:
    """Test complete authorization workflow."""
    
    async def test_complete_authorization_workflow(
        self,
        test_db: AsyncSession,
        client: TestClient
    ):
        """Test complete authorization workflow from request to approval."""
        # Create users
        general_staff = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="general_staff",
            password="TestPassword123!"
        )
        
        manager = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="manager",
            password="TestPassword123!"
        )
        
        estate = await create_test_estate(test_db, name="Workflow Test Estate")
        
        # Get tokens
        staff_login = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        staff_token = staff_login.json()["access_token"]
        
        manager_login = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        manager_token = manager_login.json()["access_token"]
        
        # Step 1: General staff creates authorization request
        request_data = {
            "target_persona_id": str(manager.id),
            "estate_id": str(estate.id),
            "data_entry_type": "fertilizer_usage_tracking",
            "permission_level": "write",
            "justification": "Need to track fertilizer usage for compliance",
            "requested_duration_days": 14
        }
        
        create_response = client.post(
            "/api/v1/authorization/requests",
            headers={"Authorization": f"Bearer {staff_token}"},
            json=request_data
        )
        
        assert create_response.status_code == 200
        request_id = create_response.json()["id"]
        
        # Step 2: Manager views pending requests
        pending_response = client.get(
            "/api/v1/authorization/requests",
            headers={"Authorization": f"Bearer {manager_token}"}
        )
        
        assert pending_response.status_code == 200
        pending_requests = pending_response.json()
        assert any(req["id"] == request_id for req in pending_requests)
        
        # Step 3: Manager approves the request
        approval_data = {
            "status": "approved",
            "response_message": "Approved for fertilizer tracking",
            "approved_duration_days": 10,  # Shorter than requested
            "conditions": {"max_entries_per_day": 5}
        }
        
        approve_response = client.post(
            f"/api/v1/authorization/requests/{request_id}/respond",
            headers={"Authorization": f"Bearer {manager_token}"},
            json=approval_data
        )
        
        assert approve_response.status_code == 200
        approved_request = approve_response.json()
        assert approved_request["status"] == "approved"
        assert approved_request["response_message"] == "Approved for fertilizer tracking"
        
        # Step 4: General staff checks active authorizations
        active_response = client.get(
            "/api/v1/authorization/active",
            headers={"Authorization": f"Bearer {staff_token}"}
        )
        
        assert active_response.status_code == 200
        active_auths = active_response.json()
        assert len(active_auths) > 0
        
        # Find the authorization for fertilizer tracking
        fertilizer_auth = next(
            (auth for auth in active_auths if auth["data_entry_type"] == "fertilizer_usage_tracking"),
            None
        )
        assert fertilizer_auth is not None
        assert fertilizer_auth["permission_level"] == "write"
        assert fertilizer_auth["conditions"] == {"max_entries_per_day": 5}
        
        # Step 5: Check available forms now includes authorized form
        forms_response = client.get(
            f"/api/v1/authorization/available-forms?estate_id={estate.id}",
            headers={"Authorization": f"Bearer {staff_token}"}
        )
        
        assert forms_response.status_code == 200
        forms_data = forms_response.json()
        
        # Should have the authorized form
        authorized_forms = forms_data["authorized_forms"]
        fertilizer_form = next(
            (form for form in authorized_forms if form["data_entry_type"] == "fertilizer_usage_tracking"),
            None
        )
        assert fertilizer_form is not None
        assert fertilizer_form["permission_level"] == "write"
