"""
Comprehensive test suite for MPOB Compliance System.

Tests FFB harvest data entry, yield validation, fertilizer tracking,
and automated reporting functionality.
"""

import pytest
import pytest_asyncio
from datetime import datetime, date, timedelta
from decimal import Decimal
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.models.mpob_compliance import (
    FFBHarvestData,
    YieldValidationData,
    FertilizerUsageTracking,
    MPOBComplianceReport
)
from app.models.user import User
from app.models.estate import Estate, EstateBlock
from tests.conftest import create_test_user, create_test_estate


@pytest.mark.unit
class TestMPOBComplianceModels:
    """Test MPOB compliance models."""
    
    @pytest_asyncio.fixture
    async def test_block(self, test_db: AsyncSession, test_estate: Estate) -> EstateBlock:
        """Create a test estate block."""
        from geoalchemy2 import WKTElement
        
        block = EstateBlock(
            estate_id=test_estate.id,
            block_name="Block A",
            area_hectares=Decimal("25.5"),
            boundary=WKTElement("POLYGON((101.6 3.1, 101.7 3.1, 101.7 3.2, 101.6 3.2, 101.6 3.1))", srid=4326),
            soil_type="clay_loam",
            planting_date=date(2020, 1, 15),
            palm_count=1200,
            palm_variety="Tenera"
        )
        
        test_db.add(block)
        await test_db.commit()
        await test_db.refresh(block)
        
        return block
    
    async def test_ffb_harvest_data_creation(
        self,
        test_db: AsyncSession,
        test_user: User,
        test_estate: Estate,
        test_block: EstateBlock
    ):
        """Test creating FFB harvest data."""
        ffb_data = FFBHarvestData(
            estate_id=test_estate.id,
            block_id=test_block.id,
            recorded_by_id=test_user.id,
            harvest_date=date.today(),
            ffb_quantity_kg=Decimal("1250.75"),
            ffb_quality_grade="grade_1",
            bunch_count=85,
            average_bunch_weight_kg=Decimal("14.71"),
            oil_extraction_rate_percent=Decimal("22.5")
        )
        
        test_db.add(ffb_data)
        await test_db.commit()
        await test_db.refresh(ffb_data)
        
        assert ffb_data.id is not None
        assert ffb_data.estate_id == test_estate.id
        assert ffb_data.block_id == test_block.id
        assert ffb_data.recorded_by_id == test_user.id
        assert ffb_data.ffb_quantity_kg == Decimal("1250.75")
        assert ffb_data.ffb_quality_grade == "grade_1"
        assert ffb_data.bunch_count == 85
        assert ffb_data.is_validated is False
        assert ffb_data.mpob_reported is False
        assert ffb_data.created_at is not None
    
    async def test_yield_validation_data_creation(
        self,
        test_db: AsyncSession,
        test_user: User,
        test_estate: Estate,
        test_block: EstateBlock
    ):
        """Test creating yield validation data."""
        yield_data = YieldValidationData(
            estate_id=test_estate.id,
            block_id=test_block.id,
            recorded_by_id=test_user.id,
            measurement_date=date.today(),
            yield_per_hectare_kg=Decimal("18500.00"),
            total_area_hectares=Decimal("25.5"),
            total_yield_kg=Decimal("471750.00"),
            palm_count=1200,
            productive_palms=1150,
            palm_age_years=4,
            measurement_method="manual_count",
            accuracy_confidence_percent=Decimal("95.0"),
            mpob_standard_compliance=True
        )
        
        test_db.add(yield_data)
        await test_db.commit()
        await test_db.refresh(yield_data)
        
        assert yield_data.id is not None
        assert yield_data.yield_per_hectare_kg == Decimal("18500.00")
        assert yield_data.total_area_hectares == Decimal("25.5")
        assert yield_data.palm_count == 1200
        assert yield_data.measurement_method == "manual_count"
        assert yield_data.mpob_standard_compliance is True
    
    async def test_fertilizer_usage_tracking_creation(
        self,
        test_db: AsyncSession,
        test_user: User,
        test_estate: Estate,
        test_block: EstateBlock
    ):
        """Test creating fertilizer usage tracking data."""
        fertilizer_data = FertilizerUsageTracking(
            estate_id=test_estate.id,
            block_id=test_block.id,
            recorded_by_id=test_user.id,
            application_date=date.today(),
            fertilizer_type="NPK Compound",
            fertilizer_brand="AgriGrow Premium",
            quantity_kg=Decimal("500.0"),
            area_applied_hectares=Decimal("25.5"),
            application_rate_kg_per_hectare=Decimal("19.61"),
            nitrogen_percent=Decimal("15.0"),
            phosphorus_percent=Decimal("15.0"),
            potassium_percent=Decimal("15.0"),
            other_nutrients={"magnesium": 2.0, "sulfur": 1.5},
            application_method="broadcast",
            weather_conditions="Dry, light wind",
            soil_moisture_level="moist",
            cost_per_kg=Decimal("2.50"),
            total_cost=Decimal("1250.00"),
            supplier="AgriSupply Sdn Bhd",
            mpob_approved_fertilizer=True,
            environmental_compliance=True
        )
        
        test_db.add(fertilizer_data)
        await test_db.commit()
        await test_db.refresh(fertilizer_data)
        
        assert fertilizer_data.id is not None
        assert fertilizer_data.fertilizer_type == "NPK Compound"
        assert fertilizer_data.quantity_kg == Decimal("500.0")
        assert fertilizer_data.application_rate_kg_per_hectare == Decimal("19.61")
        assert fertilizer_data.nitrogen_percent == Decimal("15.0")
        assert fertilizer_data.other_nutrients == {"magnesium": 2.0, "sulfur": 1.5}
        assert fertilizer_data.mpob_approved_fertilizer is True
        assert fertilizer_data.environmental_compliance is True
    
    async def test_mpob_compliance_report_creation(
        self,
        test_db: AsyncSession,
        test_user: User,
        test_estate: Estate
    ):
        """Test creating MPOB compliance report."""
        report_data = {
            "reporting_period": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31"
            },
            "ffb_harvest_summary": {
                "total_quantity_kg": 45000.0,
                "total_entries": 25,
                "validated_entries": 23,
                "quality_distribution": {
                    "grade_1": 20,
                    "grade_2": 3,
                    "grade_3": 2,
                    "reject": 0
                }
            },
            "yield_summary": {
                "average_yield_per_hectare_kg": 18500.0,
                "total_measurements": 5,
                "compliant_measurements": 5
            },
            "fertilizer_summary": {
                "total_quantity_kg": 2500.0,
                "total_applications": 10,
                "mpob_approved_applications": 10,
                "environmental_compliant_applications": 10
            }
        }
        
        summary_metrics = {
            "total_ffb_harvest_kg": 45000.0,
            "average_yield_per_hectare": 18500.0,
            "total_fertilizer_usage_kg": 2500.0,
            "compliance_percentage": 95.0,
            "data_completeness_percentage": 98.0
        }
        
        compliance_report = MPOBComplianceReport(
            estate_id=test_estate.id,
            generated_by_id=test_user.id,
            report_type="monthly",
            reporting_period_start=date(2024, 1, 1),
            reporting_period_end=date(2024, 1, 31),
            report_data=report_data,
            summary_metrics=summary_metrics,
            compliance_status="compliant"
        )
        
        test_db.add(compliance_report)
        await test_db.commit()
        await test_db.refresh(compliance_report)
        
        assert compliance_report.id is not None
        assert compliance_report.report_type == "monthly"
        assert compliance_report.compliance_status == "compliant"
        assert compliance_report.report_data["ffb_harvest_summary"]["total_quantity_kg"] == 45000.0
        assert compliance_report.summary_metrics["compliance_percentage"] == 95.0


@pytest.mark.integration
class TestMPOBComplianceAPI:
    """Test MPOB compliance API endpoints."""
    
    @pytest_asyncio.fixture
    async def auth_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="technician",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    @pytest_asyncio.fixture
    async def manager_token(self, client: TestClient, test_db: AsyncSession) -> str:
        """Get manager authentication token."""
        user = await create_test_user(
            test_db,
            email="<EMAIL>",
            role="manager",
            password="TestPassword123!"
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={"username": "<EMAIL>", "password": "TestPassword123!"}
        )
        assert response.status_code == 200
        return response.json()["access_token"]
    
    def test_create_ffb_harvest_data(
        self,
        client: TestClient,
        auth_token: str,
        test_estate: Estate
    ):
        """Test creating FFB harvest data via API."""
        harvest_data = {
            "estate_id": str(test_estate.id),
            "harvest_date": date.today().isoformat(),
            "ffb_quantity_kg": 1500.25,
            "ffb_quality_grade": "grade_1",
            "bunch_count": 95,
            "average_bunch_weight_kg": 15.79,
            "oil_extraction_rate_percent": 23.5
        }
        
        response = client.post(
            "/api/v1/mpob-compliance/ffb-harvest",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=harvest_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["ffb_quantity_kg"] == 1500.25
        assert data["ffb_quality_grade"] == "grade_1"
        assert data["bunch_count"] == 95
        assert data["is_validated"] is False
        assert data["mpob_reported"] is False
    
    def test_get_ffb_harvest_data(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting FFB harvest data."""
        response = client.get(
            "/api/v1/mpob-compliance/ffb-harvest",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_create_yield_validation_data(
        self,
        client: TestClient,
        auth_token: str,
        test_estate: Estate
    ):
        """Test creating yield validation data via API."""
        # First create a block
        block_data = {
            "estate_id": str(test_estate.id),
            "block_name": "Test Block API",
            "area_hectares": 30.0,
            "soil_type": "clay_loam",
            "planting_date": "2020-01-15",
            "palm_count": 1400,
            "palm_variety": "Tenera"
        }
        
        block_response = client.post(
            "/api/v1/estates/blocks",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=block_data
        )
        
        if block_response.status_code == 200:
            block_id = block_response.json()["id"]
        else:
            # Use existing block or skip
            pytest.skip("Could not create test block")
        
        yield_data = {
            "estate_id": str(test_estate.id),
            "block_id": block_id,
            "measurement_date": date.today().isoformat(),
            "yield_per_hectare_kg": 19000.0,
            "total_area_hectares": 30.0,
            "total_yield_kg": 570000.0,
            "palm_count": 1400,
            "productive_palms": 1350,
            "palm_age_years": 4,
            "measurement_method": "manual_count",
            "accuracy_confidence_percent": 95.0,
            "mpob_standard_compliance": True
        }
        
        response = client.post(
            "/api/v1/mpob-compliance/yield-validation",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=yield_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["yield_per_hectare_kg"] == 19000.0
        assert data["total_area_hectares"] == 30.0
        assert data["mpob_standard_compliance"] is True
    
    def test_create_fertilizer_usage_tracking(
        self,
        client: TestClient,
        auth_token: str,
        test_estate: Estate
    ):
        """Test creating fertilizer usage tracking via API."""
        fertilizer_data = {
            "estate_id": str(test_estate.id),
            "application_date": date.today().isoformat(),
            "fertilizer_type": "NPK Compound",
            "fertilizer_brand": "TestBrand",
            "quantity_kg": 750.0,
            "area_applied_hectares": 30.0,
            "application_rate_kg_per_hectare": 25.0,
            "nitrogen_percent": 15.0,
            "phosphorus_percent": 15.0,
            "potassium_percent": 15.0,
            "other_nutrients": {"magnesium": 2.0},
            "application_method": "broadcast",
            "weather_conditions": "Dry conditions",
            "soil_moisture_level": "moist",
            "cost_per_kg": 2.75,
            "total_cost": 2062.50,
            "supplier": "Test Supplier",
            "mpob_approved_fertilizer": True,
            "environmental_compliance": True
        }
        
        response = client.post(
            "/api/v1/mpob-compliance/fertilizer-usage",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=fertilizer_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["fertilizer_type"] == "NPK Compound"
        assert data["quantity_kg"] == 750.0
        assert data["application_rate_kg_per_hectare"] == 25.0
        assert data["mpob_approved_fertilizer"] is True
    
    def test_generate_mpob_compliance_report(
        self,
        client: TestClient,
        manager_token: str,
        test_estate: Estate
    ):
        """Test generating MPOB compliance report."""
        report_data = {
            "estate_id": str(test_estate.id),
            "report_type": "monthly",
            "reporting_period_start": "2024-01-01",
            "reporting_period_end": "2024-01-31"
        }
        
        response = client.post(
            "/api/v1/mpob-compliance/reports",
            headers={"Authorization": f"Bearer {manager_token}"},
            json=report_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["report_type"] == "monthly"
        assert data["compliance_status"] in ["compliant", "non_compliant", "pending_review"]
        assert "report_data" in data
        assert "summary_metrics" in data
    
    def test_get_mpob_compliance_summary(
        self,
        client: TestClient,
        auth_token: str,
        test_estate: Estate
    ):
        """Test getting MPOB compliance summary."""
        response = client.get(
            f"/api/v1/mpob-compliance/summary?estate_id={test_estate.id}",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "total_ffb_harvest_kg" in data
        assert "average_yield_per_hectare" in data
        assert "total_fertilizer_usage_kg" in data
        assert "compliance_percentage" in data
        assert "pending_validations" in data
    
    def test_get_mpob_standards(
        self,
        client: TestClient,
        auth_token: str
    ):
        """Test getting MPOB standards information."""
        response = client.get(
            "/api/v1/mpob-compliance/standards",
            headers={"Authorization": f"Bearer {auth_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Check structure of first standard
        standard = data[0]
        assert "standard_id" in standard
        assert "standard_name" in standard
        assert "description" in standard
        assert "requirements" in standard
        assert "compliance_criteria" in standard
        assert "last_updated" in standard
    
    def test_unauthorized_access(self, client: TestClient):
        """Test unauthorized access to MPOB compliance endpoints."""
        response = client.get("/api/v1/mpob-compliance/ffb-harvest")
        assert response.status_code == 401
        
        response = client.post(
            "/api/v1/mpob-compliance/ffb-harvest",
            json={"estate_id": str(uuid4())}
        )
        assert response.status_code == 401
    
    def test_invalid_data_validation(
        self,
        client: TestClient,
        auth_token: str,
        test_estate: Estate
    ):
        """Test validation of invalid data."""
        # Test invalid FFB harvest data
        invalid_harvest_data = {
            "estate_id": str(test_estate.id),
            "harvest_date": date.today().isoformat(),
            "ffb_quantity_kg": -100.0,  # Invalid: negative quantity
            "ffb_quality_grade": "invalid_grade",  # Invalid grade
            "oil_extraction_rate_percent": 150.0  # Invalid: > 100%
        }
        
        response = client.post(
            "/api/v1/mpob-compliance/ffb-harvest",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=invalid_harvest_data
        )
        
        assert response.status_code == 422  # Validation error
