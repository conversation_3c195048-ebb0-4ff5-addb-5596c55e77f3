-- Apache AGE Graph Database Setup for Soil Parameter Relationships
-- This script must be run by a PostgreSQL superuser

-- Load AGE extension
LOAD 'age';

-- Set search path to include AGE catalog
SET search_path = ag_catalog, "$user", public;

-- Create the soil_relationships graph
SELECT create_graph('soil_relationships');

-- Grant permissions to the application user
GRANT USAGE ON SCHEMA ag_catalog TO user_test;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA ag_catalog TO user_test;

-- Create vertex labels for soil entities
SELECT create_vlabel('soil_relationships', 'SoilParameter');
SELECT create_vlabel('soil_relationships', 'SensorReading');
SELECT create_vlabel('soil_relationships', 'Estate');
SELECT create_vlabel('soil_relationships', 'Block');
SELECT create_vlabel('soil_relationships', 'Prediction');
SELECT create_vlabel('soil_relationships', 'Anomaly');

-- Create edge labels for relationships
SELECT create_elabel('soil_relationships', 'CORRELATES_WITH');
SELECT create_elabel('soil_relationships', 'CAUSES');
SELECT create_elabel('soil_relationships', 'MEASURED_AT');
SELECT create_elabel('soil_relationships', 'LOCATED_IN');
SELECT create_elabel('soil_relationships', 'PREDICTS');
SELECT create_elabel('soil_relationships', 'INDICATES');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS soil_param_name_idx ON ag_catalog."soil_relationships_SoilParameter" USING btree ((properties->>'name'));
CREATE INDEX IF NOT EXISTS sensor_reading_timestamp_idx ON ag_catalog."soil_relationships_SensorReading" USING btree ((properties->>'timestamp'));
CREATE INDEX IF NOT EXISTS estate_name_idx ON ag_catalog."soil_relationships_Estate" USING btree ((properties->>'name'));

-- Insert basic soil parameter nodes
SELECT * FROM cypher('soil_relationships', $$
    CREATE (ph:SoilParameter {name: 'pH', unit: 'pH', optimal_min: 6.0, optimal_max: 7.5, description: 'Soil acidity/alkalinity level'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (moisture:SoilParameter {name: 'moisture', unit: '%', optimal_min: 40.0, optimal_max: 70.0, description: 'Soil water content percentage'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (temp:SoilParameter {name: 'temperature', unit: '°C', optimal_min: 20.0, optimal_max: 30.0, description: 'Soil temperature'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (ec:SoilParameter {name: 'electrical_conductivity', unit: 'dS/m', optimal_min: 0.5, optimal_max: 2.0, description: 'Soil electrical conductivity'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (n:SoilParameter {name: 'nitrogen', unit: 'ppm', optimal_min: 20.0, optimal_max: 50.0, description: 'Nitrogen content'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (p:SoilParameter {name: 'phosphorus', unit: 'ppm', optimal_min: 15.0, optimal_max: 30.0, description: 'Phosphorus content'})
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    CREATE (k:SoilParameter {name: 'potassium', unit: 'ppm', optimal_min: 100.0, optimal_max: 200.0, description: 'Potassium content'})
$$) AS (result agtype);

-- Create correlation relationships between soil parameters
SELECT * FROM cypher('soil_relationships', $$
    MATCH (ph:SoilParameter {name: 'pH'}), (n:SoilParameter {name: 'nitrogen'})
    CREATE (ph)-[:CORRELATES_WITH {correlation: -0.6, strength: 'moderate', description: 'pH affects nitrogen availability'}]->(n)
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    MATCH (ph:SoilParameter {name: 'pH'}), (p:SoilParameter {name: 'phosphorus'})
    CREATE (ph)-[:CORRELATES_WITH {correlation: -0.4, strength: 'weak', description: 'pH affects phosphorus solubility'}]->(p)
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    MATCH (moisture:SoilParameter {name: 'moisture'}), (temp:SoilParameter {name: 'temperature'})
    CREATE (moisture)-[:CORRELATES_WITH {correlation: -0.7, strength: 'strong', description: 'Higher temperature reduces moisture'}]->(temp)
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    MATCH (ec:SoilParameter {name: 'electrical_conductivity'}), (moisture:SoilParameter {name: 'moisture'})
    CREATE (ec)-[:CORRELATES_WITH {correlation: 0.5, strength: 'moderate', description: 'EC increases with moisture content'}]->(moisture)
$$) AS (result agtype);

-- Create causal relationships
SELECT * FROM cypher('soil_relationships', $$
    MATCH (ph:SoilParameter {name: 'pH'}), (n:SoilParameter {name: 'nitrogen'})
    CREATE (ph)-[:CAUSES {effect: 'nutrient_availability', impact: 'high', description: 'Low pH reduces nitrogen uptake'}]->(n)
$$) AS (result agtype);

SELECT * FROM cypher('soil_relationships', $$
    MATCH (moisture:SoilParameter {name: 'moisture'}), (n:SoilParameter {name: 'nitrogen'})
    CREATE (moisture)-[:CAUSES {effect: 'leaching', impact: 'medium', description: 'Excess moisture causes nitrogen leaching'}]->(n)
$$) AS (result agtype);

-- Create functions for graph operations
CREATE OR REPLACE FUNCTION get_soil_correlations(param_name TEXT)
RETURNS TABLE(
    related_parameter TEXT,
    correlation_strength NUMERIC,
    relationship_type TEXT,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (end_vertex.properties->>'name')::TEXT,
        (edge.properties->>'correlation')::NUMERIC,
        (edge.properties->>'strength')::TEXT,
        (edge.properties->>'description')::TEXT
    FROM cypher('soil_relationships', $$
        MATCH (start:SoilParameter)-[r:CORRELATES_WITH]->(end:SoilParameter)
        WHERE start.name = $$ || quote_literal(param_name) || $$
        RETURN end, r
    $$) AS (end_vertex agtype, edge agtype);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION add_soil_reading_to_graph(
    reading_id UUID,
    sensor_id UUID,
    estate_name TEXT,
    block_name TEXT,
    ph_value NUMERIC,
    moisture_value NUMERIC,
    temp_value NUMERIC,
    ec_value NUMERIC,
    n_value NUMERIC,
    p_value NUMERIC,
    k_value NUMERIC,
    reading_timestamp TIMESTAMP
)
RETURNS VOID AS $$
DECLARE
    cypher_query TEXT;
BEGIN
    -- Create sensor reading node
    cypher_query := format($$
        CREATE (reading:SensorReading {
            id: '%s',
            sensor_id: '%s',
            estate: '%s',
            block: '%s',
            ph: %s,
            moisture: %s,
            temperature: %s,
            ec: %s,
            nitrogen: %s,
            phosphorus: %s,
            potassium: %s,
            timestamp: '%s'
        })
    $$, reading_id, sensor_id, estate_name, block_name, 
        ph_value, moisture_value, temp_value, ec_value, 
        n_value, p_value, k_value, reading_timestamp);
    
    PERFORM cypher('soil_relationships', cypher_query);
    
    -- Create relationships to soil parameters
    PERFORM cypher('soil_relationships', format($$
        MATCH (reading:SensorReading {id: '%s'}), (ph:SoilParameter {name: 'pH'})
        CREATE (reading)-[:MEASURED_AT {value: %s, timestamp: '%s'}]->(ph)
    $$, reading_id, ph_value, reading_timestamp));
    
    -- Add more parameter relationships as needed
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_soil_correlations(TEXT) TO user_test;
GRANT EXECUTE ON FUNCTION add_soil_reading_to_graph(UUID, UUID, TEXT, TEXT, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, NUMERIC, TIMESTAMP) TO user_test;

-- Create view for easy access to soil parameter relationships
CREATE OR REPLACE VIEW soil_parameter_relationships AS
SELECT 
    (start_vertex.properties->>'name')::TEXT as source_parameter,
    (end_vertex.properties->>'name')::TEXT as target_parameter,
    (edge.properties->>'correlation')::NUMERIC as correlation,
    (edge.properties->>'strength')::TEXT as strength,
    (edge.properties->>'description')::TEXT as description
FROM cypher('soil_relationships', $$
    MATCH (start:SoilParameter)-[r:CORRELATES_WITH]->(end:SoilParameter)
    RETURN start, end, r
$$) AS (start_vertex agtype, end_vertex agtype, edge agtype);

GRANT SELECT ON soil_parameter_relationships TO user_test;
