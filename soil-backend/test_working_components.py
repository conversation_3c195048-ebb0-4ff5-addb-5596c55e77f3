#!/usr/bin/env python3
"""
Test working components of the soil system.

This script tests the components that are currently working:
- Backend API health and documentation
- Vector search performance
- Database connectivity
"""

import asyncio
import sys
import os
import logging
import uuid
import httpx
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BACKEND_URL = "http://localhost:8000"
DATABASE_URL = "postgresql://user_test:password123@localhost/db_test"


async def test_backend_health():
    """Test backend health endpoint."""
    logger.info("Testing backend health...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BACKEND_URL}/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            
            logger.info(f"✅ Backend health check passed: {data}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Backend health check failed: {e}")
        return False


async def test_api_documentation():
    """Test API documentation availability."""
    logger.info("Testing API documentation...")
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BACKEND_URL}/docs")
            
            assert response.status_code == 200
            assert "text/html" in response.headers.get("content-type", "")
            
            logger.info("✅ API documentation is accessible")
            return True
            
    except Exception as e:
        logger.error(f"❌ API documentation test failed: {e}")
        return False


async def test_vector_search_functionality():
    """Test vector search functionality."""
    logger.info("Testing vector search functionality...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from embeddings.bge_embedder import BGEEmbedder
        from embeddings.vector_search import VectorSearchService
        
        # Test BGE embedder
        embedder = BGEEmbedder()
        test_text = "Soil analysis shows optimal pH levels for crop growth"
        embedding = await embedder.embed_text_async(test_text)
        
        assert embedding is not None
        assert len(embedding) == 1024
        logger.info("✅ BGE embedder working correctly")
        
        # Test vector search
        vector_service = VectorSearchService(DATABASE_URL, embedder)
        await vector_service.initialize()
        
        # Add test content
        test_id = str(uuid.uuid4())
        await vector_service.add_content_embedding(
            test_id,
            "working_test",
            test_text,
            {"test": "working_components", "timestamp": datetime.now().isoformat()}
        )
        logger.info("✅ Content embedding added successfully")
        
        # Search for content
        results = await vector_service.search_similar_content(
            "soil pH crop growth",
            limit=5
        )
        
        assert len(results) > 0
        logger.info(f"✅ Vector search working. Found {len(results)} results")
        
        # Get statistics
        stats = await vector_service.get_content_statistics()
        logger.info(f"✅ Vector database stats: {stats}")
        
        await vector_service.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector search functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_embedding_performance():
    """Test embedding generation performance."""
    logger.info("Testing embedding performance...")
    
    try:
        # Add soil-ai path for imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../soil-ai/soil_ai'))
        
        from embeddings.bge_embedder import BGEEmbedder
        
        embedder = BGEEmbedder()
        
        # Test multiple soil scenarios
        test_scenarios = [
            "Soil pH is 7.2 with high nitrogen content",
            "Low moisture detected in sandy soil",
            "Optimal phosphorus levels for tomato cultivation",
            "Clay soil with poor drainage issues",
            "Organic matter content needs improvement"
        ]
        
        start_time = datetime.now()
        embeddings = []
        
        for scenario in test_scenarios:
            embedding = await embedder.embed_text_async(scenario)
            embeddings.append(embedding)
            assert len(embedding) == 1024
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        avg_time = total_time / len(test_scenarios)
        
        logger.info(f"✅ Generated {len(embeddings)} embeddings in {total_time:.3f}s (avg: {avg_time:.3f}s per embedding)")
        
        # Test similarity calculations
        similarity = embedder.compute_similarity(embeddings[0], embeddings[1])
        logger.info(f"✅ Similarity calculation working: {similarity:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_extensions():
    """Test database extensions are working."""
    logger.info("Testing database extensions...")
    
    try:
        import asyncpg
        
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test pgvector extension
        result = await conn.fetchval("SELECT '1'::vector(3) <-> '2'::vector(3)")
        assert result is not None
        logger.info("✅ pgvector extension working")
        
        # Test TimescaleDB extension
        result = await conn.fetchval("SELECT extname FROM pg_extension WHERE extname = 'timescaledb'")
        if result:
            logger.info("✅ TimescaleDB extension installed")
        else:
            logger.info("⚠️ TimescaleDB extension not found (optional)")
        
        # Test Apache AGE extension
        result = await conn.fetchval("SELECT extname FROM pg_extension WHERE extname = 'age'")
        if result:
            logger.info("✅ Apache AGE extension installed")
        else:
            logger.info("⚠️ Apache AGE extension not found (optional)")
        
        await conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Database extensions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_working_component_tests():
    """Run tests for working components."""
    logger.info("Starting working component tests...")
    
    tests = [
        ("Backend Health", test_backend_health),
        ("API Documentation", test_api_documentation),
        ("Vector Search Functionality", test_vector_search_functionality),
        ("Embedding Performance", test_embedding_performance),
        ("Database Extensions", test_database_extensions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*70}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*70}")
        
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("WORKING COMPONENT TEST SUMMARY")
    logger.info(f"{'='*70}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= 4:  # Most tests should pass
        logger.info("🎉 CORE SYSTEM COMPONENTS ARE WORKING!")
        logger.info("✅ The soil system is functional with key features operational!")
        return True
    else:
        logger.error(f"💥 {total - passed} critical tests FAILED!")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_working_component_tests())
    sys.exit(0 if success else 1)
