[tool:pytest]
# Pytest configuration for Yield Sight System Soil Backend

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=95
    --cov-branch
    --durations=10
    --maxfail=5

# Markers for test categorization
markers =
    unit: Unit tests for individual functions and classes
    integration: Integration tests for API endpoints and database operations
    security: Security-focused tests for authentication and authorization
    performance: Performance and load testing
    slow: Tests that take a long time to run
    database: Tests that require database connection
    external: Tests that require external services (OpenAI, Mem0)
    smoke: Basic smoke tests for critical functionality
    demo: Demo-specific functionality tests
    ai: AI and machine learning tests
    api: API endpoint tests
    cache: Cache-related tests
    gpu: Tests requiring GPU support
    heatmap: Heatmap generation tests
    scenario: Demo scenario management tests
    mpob: MPOB compliance system tests
    authorization: Authorization system tests
    model_performance: Model performance and validation tests
    mfa: Multi-factor authentication tests
    gdpr: GDPR compliance tests
    encryption: Data encryption tests
    audit: Audit logging tests
    policy: Policy analysis tests
    research: Government research tests

# Test environment
env = 
    TESTING = true
    DATABASE_URL = postgresql+asyncpg://test:test@localhost:5432/test_soil_backend
    SECRET_KEY = test-secret-key-for-testing-only
    DEBUG = true
    OPENAI_API_KEY = test-openai-key
    MEM0_API_KEY = test-mem0-key

# Async support
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = app
omit = 
    app/main.py
    */tests/*
    */venv/*
    */migrations/*
    */__pycache__/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
